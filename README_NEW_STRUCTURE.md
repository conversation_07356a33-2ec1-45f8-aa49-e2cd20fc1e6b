# Closet Glass Chic - Restructured Project

## 🏗️ New Project Structure

The project has been reorganized into separate frontend and backend directories for better separation of concerns:

```
closet-glass-chic/
├── frontend/                 # React/Vite frontend application
│   ├── src/
│   │   ├── components/      # UI components
│   │   ├── pages/          # Page components
│   │   ├── hooks/          # Custom React hooks
│   │   ├── contexts/       # React contexts
│   │   ├── lib/            # Utility libraries
│   │   └── styles/         # CSS and styling
│   ├── public/             # Static assets
│   ├── package.json        # Frontend dependencies
│   ├── vite.config.ts      # Vite configuration
│   └── tsconfig.json       # TypeScript config
├── backend/                 # Express.js backend API
│   ├── src/
│   │   ├── routes/         # API route handlers
│   │   ├── services/       # Business logic services
│   │   ├── middleware/     # Express middleware
│   │   ├── types/          # TypeScript type definitions
│   │   └── __tests__/      # Test files
│   ├── package.json        # Backend dependencies
│   ├── tsconfig.json       # TypeScript config
│   └── API_DOCUMENTATION.md # API documentation
├── Dockerfile.frontend      # Frontend Docker configuration
├── Dockerfile.backend       # Backend Docker configuration
├── docker-compose.new.yml   # New Docker Compose configuration
├── .env                     # Environment variables
└── README_NEW_STRUCTURE.md  # This file
```

## 🚀 Quick Start

### Development Mode

1. **Start both frontend and backend in development mode:**
   ```bash
   docker-compose -f docker-compose.new.yml --profile dev up
   ```

2. **Or run individually:**
   
   **Frontend:**
   ```bash
   cd frontend
   npm install
   npm run dev
   ```
   
   **Backend:**
   ```bash
   cd backend
   npm install
   npm run dev
   ```

### Production Mode

1. **Start production services:**
   ```bash
   docker-compose -f docker-compose.new.yml up
   ```

2. **Or build and run individually:**
   
   **Frontend:**
   ```bash
   cd frontend
   npm install
   npm run build
   npm run preview
   ```
   
   **Backend:**
   ```bash
   cd backend
   npm install
   npm run build
   npm start
   ```

## 🌐 Service URLs

### Development
- **Frontend:** http://localhost:5173
- **Backend API:** http://localhost:3001
- **API Documentation:** http://localhost:3001/health

### Production
- **Frontend:** http://localhost:3000
- **Backend API:** http://localhost:3001

## 📚 API Documentation

Comprehensive API documentation is available at:
- **File:** `backend/API_DOCUMENTATION.md`
- **Endpoints:** All API endpoints with examples and testing instructions

### Key API Endpoints

- `GET /health` - Health check
- `GET /api/weather?lat={lat}&lon={lon}` - Weather data
- `GET /api/cities/search?q={query}` - City search
- `POST /api/user-profile` - Create user profile
- `POST /api/upload/image` - Upload clothing images

## 🧪 Testing

### Backend API Tests
```bash
cd backend
npm test
```

### Manual API Testing
```bash
# Health check
curl http://localhost:3001/health

# Weather API
curl "http://localhost:3001/api/weather?lat=40.7128&lon=-74.0060"

# City search
curl "http://localhost:3001/api/cities/search?q=New%20York"
```

## 🔧 Configuration

### Environment Variables

Update `.env` file with required API keys:

```bash
# API Keys
VITE_OPENWEATHER_API_KEY=your_openweather_api_key
VITE_OPENWEATHER_API_URL=https://api.openweathermap.org/data/2.5
VITE_NOMINATIM_API_URL=https://nominatim.openstreetmap.org

# Backend Configuration
PORT=3001
FRONTEND_URL=http://localhost:5173

# Feature Flags
VITE_ENABLE_WEATHER_INTEGRATION=true
VITE_ENABLE_CITY_AUTOCOMPLETE=true
```

### Docker Configuration

The new Docker setup includes:
- **Multi-stage builds** for optimized production images
- **Separate services** for frontend and backend
- **Development profiles** for hot reloading
- **Health checks** for service monitoring
- **Volume mounts** for persistent data

## 🔄 Migration from Old Structure

### What Changed

1. **Separated Concerns:**
   - Frontend code moved to `frontend/` directory
   - Backend services moved to `backend/` directory
   - Shared types duplicated for independence

2. **New Docker Setup:**
   - `Dockerfile.frontend` - React/Vite application
   - `Dockerfile.backend` - Express.js API server
   - `docker-compose.new.yml` - Multi-service configuration

3. **API Endpoints:**
   - Weather data: Now served via backend API
   - City search: Proxied through backend
   - File uploads: Handled by backend with processing
   - User profiles: Backend database operations

### Benefits

- **Better Separation:** Clear distinction between frontend and backend
- **Scalability:** Services can be scaled independently
- **Development:** Easier to work on frontend/backend separately
- **Deployment:** Can deploy services to different environments
- **Testing:** Isolated testing for each service
- **Security:** Backend can implement proper authentication/authorization

## 🐛 Troubleshooting

### Common Issues

1. **CORS Errors:**
   - Ensure backend CORS is configured for frontend URL
   - Check `FRONTEND_URL` environment variable

2. **API Connection Issues:**
   - Verify backend is running on port 3001
   - Check `VITE_API_BASE_URL` in frontend environment

3. **Weather API Errors:**
   - Verify `VITE_OPENWEATHER_API_KEY` is set
   - Check API key validity at OpenWeatherMap

4. **Docker Issues:**
   - Use `docker-compose.new.yml` for new structure
   - Ensure ports 3000 and 3001 are available

### Debug Commands

```bash
# Check service status
docker-compose -f docker-compose.new.yml ps

# View logs
docker-compose -f docker-compose.new.yml logs frontend
docker-compose -f docker-compose.new.yml logs backend

# Restart services
docker-compose -f docker-compose.new.yml restart
```

## 📈 Next Steps

1. **Database Integration:** Add PostgreSQL or MongoDB for persistent storage
2. **Authentication:** Implement JWT-based user authentication
3. **File Storage:** Add cloud storage for uploaded images
4. **Monitoring:** Add logging and monitoring services
5. **CI/CD:** Set up automated testing and deployment pipelines

## 🤝 Contributing

When working with the new structure:

1. **Frontend changes:** Work in `frontend/` directory
2. **Backend changes:** Work in `backend/` directory
3. **API changes:** Update documentation in `backend/API_DOCUMENTATION.md`
4. **Docker changes:** Update respective Dockerfile and docker-compose.new.yml
5. **Testing:** Run tests for both frontend and backend

The restructured project maintains all existing functionality while providing a more maintainable and scalable architecture.
