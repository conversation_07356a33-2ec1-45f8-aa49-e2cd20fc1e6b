# Staging Environment Configuration
# This file is used for staging deployment

# Application Configuration
VITE_APP_NAME=Closet Glass Chic (Staging)
NODE_ENV=production

# Network Configuration - Staging
# Replace with your actual staging domain or IP
VITE_BASE_URL=staging.yourdomain.com

# Port Configuration
VITE_FRONTEND_PORT=5173
VITE_BACKEND_PORT=3001

# Non-VITE versions for Docker Compose
BASE_URL=staging.yourdomain.com
FRONTEND_PORT=5173
BACKEND_PORT=3001

# Feature Flags - Staging
VITE_ENABLE_DEBUG=true
VITE_ENABLE_ANALYTICS=false

# External API Services (use your staging keys)
VITE_OPENWEATHER_API_KEY=your_staging_openweather_api_key
VITE_OPENWEATHER_API_URL=https://api.openweathermap.org/data/2.5
VITE_NOMINATIM_API_URL=https://nominatim.openstreetmap.org
