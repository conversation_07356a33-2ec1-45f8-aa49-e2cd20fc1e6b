# Network Configuration Guide

This guide explains how to configure the application for external access using the consolidated environment variable approach.

## 🌐 Configuration Overview

The application now uses a **single base URL** with separate port configurations instead of multiple hardcoded URLs. This makes it much easier to configure for different environments and external access.

## 📝 Environment Variables

### Core Configuration
```bash
# Single base URL for all services (VITE_ prefix for frontend access)
VITE_BASE_URL=**************

# Port configuration (VITE_ prefix for frontend access)
VITE_FRONTEND_PORT=5173
VITE_BACKEND_PORT=3001

# Non-VITE versions for Docker Compose
BASE_URL=**************
FRONTEND_PORT=5173
BACKEND_PORT=3001
```

**Note**: Vite only exposes environment variables prefixed with `VITE_` to the frontend code. The non-prefixed versions are used by Docker Compose for container configuration.

### Computed URLs
The application automatically computes all necessary URLs from the base configuration:
- **Frontend URL**: `http://{BASE_URL}:{FRONTEND_PORT}`
- **Backend URL**: `http://{BASE_URL}:{BACKEND_PORT}`
- **API URL**: `http://{BASE_URL}:{BACKEND_PORT}/api`

## 🚀 Quick Setup for External Access

1. **Find your machine's IP address:**
   ```bash
   # On Linux/Mac
   ip addr show | grep "inet " | grep -v 127.0.0.1
   
   # On Windows
   ipconfig | findstr "IPv4"
   ```

2. **Update the .env file:**
   ```bash
   # Frontend-accessible variables (VITE_ prefix required)
   VITE_BASE_URL=*************  # Replace with your actual IP
   VITE_FRONTEND_PORT=5173
   VITE_BACKEND_PORT=3001

   # Docker Compose variables (no VITE_ prefix)
   BASE_URL=*************
   FRONTEND_PORT=5173
   BACKEND_PORT=3001
   ```

3. **Restart the application:**
   ```bash
   docker compose down
   docker compose up -d
   ```

4. **Access from any device on your network:**
   - Frontend: `http://*************:5173`
   - Backend API: `http://*************:3001/api`

## 🌍 Environment Examples

### Local Development
```bash
BASE_URL=127.0.0.1
FRONTEND_PORT=5173
BACKEND_PORT=3001
```

### Local Network Access
```bash
BASE_URL=*************  # Your machine's local IP
FRONTEND_PORT=5173
BACKEND_PORT=3001
```

### Production
```bash
BASE_URL=yourdomain.com
FRONTEND_PORT=80
BACKEND_PORT=3001
```

### Custom Ports
```bash
BASE_URL=*************
FRONTEND_PORT=8080
BACKEND_PORT=8001
```

## 🔧 Using Different Environment Files

You can use different environment files for different scenarios:

```bash
# Development
docker compose --env-file .env.development up -d

# Local network access
docker compose --env-file .env.local-network up -d

# Production
docker compose --env-file .env.production up -d
```

## 🧪 Testing Configuration

The application includes built-in configuration testing:

1. Open the frontend in your browser
2. Look for the debug panel (bottom right)
3. Click "Test Config" to verify all URLs are working correctly

## 🔍 Troubleshooting

### Cannot access from other devices
1. Check firewall settings on your machine
2. Ensure the ports are not blocked
3. Verify the BASE_URL is your machine's actual IP address

### API calls failing
1. Check that CORS is properly configured in the backend
2. Verify the computed URLs are correct using the config test
3. Ensure both frontend and backend containers are running

### Port conflicts
1. Change the port numbers in the .env file
2. Make sure no other services are using the same ports
3. Restart the containers after changing ports

## 📊 Verification Commands

```bash
# Check container ports
docker ps --format "table {{.Names}}\t{{.Ports}}"

# Test backend health
curl http://{BASE_URL}:{BACKEND_PORT}/health

# Test API endpoint
curl -X POST http://{BASE_URL}:{BACKEND_PORT}/api/auth/validate-email \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>"}'

# Check environment variables in containers
docker exec closet-frontend env | grep -E "(BASE_URL|FRONTEND_PORT|BACKEND_PORT)"
```

## 🎯 Benefits of This Approach

1. **Single Source of Truth**: One base URL controls all service URLs
2. **Easy External Access**: Just change the BASE_URL to your IP address
3. **Environment Flexibility**: Easy to switch between dev/staging/production
4. **No Hardcoded URLs**: No more localhost dependencies
5. **Port Flexibility**: Easy to change ports without touching multiple files
6. **Docker Friendly**: Works seamlessly with Docker Compose

## 🔗 Access URLs

With `BASE_URL=**************`:
- **Frontend**: http://**************:5173
- **Backend API**: http://**************:3001/api
- **Backend Health**: http://**************:3001/health

Now you can access the application from any device on your network! 🌐
