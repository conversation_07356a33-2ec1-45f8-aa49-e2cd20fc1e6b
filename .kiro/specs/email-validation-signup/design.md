# Design Document

## Overview

This feature implements real-time email validation during the user signup process by adding an onBlur event handler to the email input field that queries the backend to check if the email already exists. The design leverages the existing authentication architecture and extends the SignUpPage component with new validation states and UI feedback mechanisms.

## Architecture

The email validation feature follows a client-server architecture pattern:

1. **Frontend (React)**: Enhanced SignUpPage component with email validation state management
2. **Backend API**: Extended authentication service with email existence checking
3. **Database**: Query against existing users table to check email uniqueness

### Component Architecture

```
SignUpPage
├── Email Input Field (with onBlur handler)
├── Email Validation State Management
├── Loading Indicator Component
├── Error Message Display
└── Conditional Button State Management
```

### Data Flow

```mermaid
sequenceDiagram
    participant User
    participant SignUpPage
    participant BackendAPI
    participant Database
    
    User->>SignUpPage: Enters email and loses focus
    SignUpPage->>SignUpPage: Validate email format
    alt Email format valid
        SignUpPage->>BackendAPI: POST /auth/validate-email
        BackendAPI->>Database: SELECT user by email
        Database-->>BackendAPI: User exists/doesn't exist
        BackendAPI-->>SignUpPage: { available: boolean, message: string }
        alt Email exists
            SignUpPage->>SignUpPage: Show error, disable signup button
        else Email available
            SignUpPage->>SignUpPage: Clear errors, enable signup button
        end
    else Email format invalid
        SignUpPage->>SignUpPage: Show format error
    end
```

## Components and Interfaces

### Frontend Components

#### Enhanced SignUpPage Component

**New State Variables:**
```typescript
interface EmailValidationState {
  isValidating: boolean;
  isEmailTaken: boolean;
  validationError: string | null;
  hasValidated: boolean;
}
```

**New Props/Handlers:**
- `handleEmailBlur()`: Triggers email validation when focus leaves email field
- `validateEmailAvailability()`: Makes API call to check email existence
- `resetEmailValidation()`: Clears validation state when email changes

#### Email Validation Indicator Component

A new micro-component to display validation status:
```typescript
interface EmailValidationIndicatorProps {
  isValidating: boolean;
  isEmailTaken: boolean;
  validationError: string | null;
  hasValidated: boolean;
}
```

### Backend API Extensions

#### New Endpoint: `/auth/validate-email`

**Request:**
```typescript
interface ValidateEmailRequest {
  email: string;
}
```

**Response:**
```typescript
interface ValidateEmailResponse {
  success: boolean;
  available: boolean;
  message: string;
}
```

#### Enhanced AuthService

**New Method:**
```typescript
async validateEmailAvailability(email: string, requestId?: string): Promise<{
  available: boolean;
  message: string;
}>
```

## Data Models

### Email Validation State Model

```typescript
interface EmailValidationState {
  isValidating: boolean;        // True during API call
  isEmailTaken: boolean;        // True if email exists in database
  validationError: string | null; // Network/validation error message
  hasValidated: boolean;        // True after first validation attempt
}
```

### API Response Model

```typescript
interface EmailValidationResult {
  available: boolean;           // False if email exists
  message: string;             // User-friendly message
}
```

## Error Handling

### Frontend Error Handling

1. **Network Errors**: Display "Unable to verify email. Please try again." and allow signup to continue
2. **Timeout Errors**: Treat as network error after 5-second timeout
3. **Format Errors**: Show format validation before attempting API call
4. **API Errors**: Display specific error message from backend

### Backend Error Handling

1. **Database Connection Errors**: Return generic error message, log specific details
2. **Invalid Email Format**: Return validation error
3. **Database Query Errors**: Return generic error, allow signup to continue

### Error Recovery

- **Retry Mechanism**: User can retry validation by re-focusing and blurring email field
- **Graceful Degradation**: Network failures don't prevent signup attempt
- **Clear Error States**: Errors clear when user modifies email field

## Testing Strategy

### Unit Tests

#### Frontend Tests
1. **Email Validation State Management**
   - Test state transitions during validation lifecycle
   - Test error state handling and clearing
   - Test button disable/enable logic

2. **Email Validation Component**
   - Test onBlur event triggering
   - Test loading indicator display
   - Test error message rendering
   - Test login suggestion link functionality

3. **API Integration**
   - Mock API responses for available/unavailable emails
   - Test network error handling
   - Test timeout scenarios

#### Backend Tests
1. **Email Validation Endpoint**
   - Test with existing email addresses
   - Test with non-existent email addresses
   - Test with invalid email formats
   - Test database error scenarios

2. **AuthService Email Validation**
   - Test email existence checking
   - Test database query error handling
   - Test email format validation

### Integration Tests

1. **End-to-End Email Validation Flow**
   - Test complete validation cycle from frontend to database
   - Test error scenarios with real network conditions
   - Test user experience with various email states

2. **Signup Flow Integration**
   - Test signup prevention when email exists
   - Test successful signup when email is available
   - Test navigation to login page when email exists

### Performance Tests

1. **API Response Time**: Ensure email validation completes within 2 seconds
2. **Database Query Performance**: Optimize email lookup query with proper indexing
3. **Concurrent Validation**: Test multiple simultaneous email validations

## Security Considerations

### Input Validation
- Sanitize email input before database queries
- Validate email format on both frontend and backend
- Prevent SQL injection through parameterized queries

### Rate Limiting
- Implement rate limiting on email validation endpoint
- Prevent abuse through excessive validation requests
- Consider implementing client-side debouncing

### Information Disclosure
- Avoid revealing specific user information in error messages
- Use generic messages for security-sensitive operations
- Log detailed errors server-side only

## Performance Optimizations

### Frontend Optimizations
- Debounce email validation to prevent excessive API calls
- Cache validation results for recently checked emails
- Implement request cancellation for rapid email changes

### Backend Optimizations
- Add database index on users.email column for fast lookups
- Implement connection pooling for database queries
- Add response caching for frequently checked domains

### Network Optimizations
- Minimize payload size in validation responses
- Implement request timeout handling
- Use appropriate HTTP status codes for different scenarios