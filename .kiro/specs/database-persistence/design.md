# Design Document

## Overview

The current application architecture has a critical flaw: the frontend authentication system uses Dexie (IndexedDB) for local browser storage, while the backend has a properly configured PostgreSQL database with persistent volumes. When Docker containers restart, the browser's IndexedDB is cleared, but the PostgreSQL data persists. The solution is to migrate the authentication system to use the backend PostgreSQL database through API calls, ensuring true persistence across container restarts.

## Architecture

### Current Architecture Issues
- Frontend uses Dexie (IndexedDB) for user authentication data
- Backend has PostgreSQL database but no authentication endpoints
- No communication between frontend auth service and backend database
- Data loss occurs on browser refresh/container restart

### Target Architecture
- Frontend authentication service calls backend API endpoints
- Backend provides RESTful authentication endpoints
- PostgreSQL database stores all user authentication data
- Session management through database-backed tokens
- Persistent data across all container restarts

## Components and Interfaces

### Backend Components

#### 1. Authentication Database Schema
```sql
-- Users table for authentication
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP WITH TIME ZONE,
    status user_status DEFAULT 'active'
);

-- Link users to user_profiles
ALTER TABLE user_profiles ADD COLUMN user_id UUID REFERENCES users(id) ON DELETE CASCADE;
```

#### 2. Authentication Service (Backend)
```typescript
interface AuthService {
  register(email: string, password: string, profileData: UserProfileData): Promise<AuthResult>
  login(email: string, password: string): Promise<AuthResult>
  logout(sessionToken: string): Promise<void>
  verifySession(sessionToken: string): Promise<UserSession | null>
  refreshSession(sessionToken: string): Promise<AuthResult>
}
```

#### 3. Authentication Routes
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user
- `POST /api/auth/refresh` - Refresh session token

#### 4. Session Management
```typescript
interface UserSession {
  id: string
  userId: string
  sessionToken: string
  expiresAt: Date
  createdAt: Date
  lastAccessedAt: Date
}
```

### Frontend Components

#### 1. Updated Authentication Service
```typescript
interface AuthService {
  register(credentials: AuthCredentials, profileData: UserProfileData): Promise<AuthResult>
  signIn(credentials: AuthCredentials): Promise<AuthResult>
  signOut(): Promise<void>
  getCurrentUser(): Promise<UserProfile | null>
  refreshToken(): Promise<boolean>
}
```

#### 2. API Client Integration
- HTTP client for backend communication
- Token management and automatic refresh
- Error handling and retry logic
- Request/response interceptors

#### 3. Session Storage
- Store session tokens in localStorage/sessionStorage
- Automatic token refresh before expiration
- Clear tokens on logout/error

## Data Models

### User Authentication Model
```typescript
interface User {
  id: string
  email: string
  createdAt: string
  updatedAt: string
  lastLoginAt?: string
  status: 'active' | 'inactive' | 'suspended'
}
```

### Session Model
```typescript
interface Session {
  sessionToken: string
  userId: string
  expiresAt: string
  refreshToken?: string
}
```

### Authentication Request/Response Models
```typescript
interface RegisterRequest {
  email: string
  password: string
  profileData: {
    firstName: string
    lastName: string
    gender: string
    dateOfBirth: string
    cityName: string
    latitude: number
    longitude: number
  }
}

interface LoginRequest {
  email: string
  password: string
}

interface AuthResponse {
  success: boolean
  user?: UserProfile
  sessionToken?: string
  refreshToken?: string
  expiresAt?: string
  error?: string
}
```

## Error Handling

### Backend Error Handling
- Database connection errors with retry logic
- Password hashing/verification errors
- Session validation errors
- Rate limiting for authentication attempts
- Proper HTTP status codes and error messages

### Frontend Error Handling
- Network connectivity issues
- Token expiration handling
- Authentication failures
- Session timeout handling
- Graceful degradation when backend is unavailable

### Error Response Format
```typescript
interface ApiError {
  success: false
  error: string
  code: string
  details?: any
}
```

## Testing Strategy

### Backend Testing
- Unit tests for authentication service methods
- Integration tests for database operations
- API endpoint testing with various scenarios
- Session management testing
- Password security testing

### Frontend Testing
- Unit tests for authentication service
- Integration tests for API communication
- Session management testing
- Error handling scenarios
- User flow testing (register, login, logout)

### End-to-End Testing
- Complete authentication flow testing
- Container restart persistence testing
- Session expiration and refresh testing
- Multi-user scenario testing

## Security Considerations

### Password Security
- Use bcrypt for password hashing with proper salt rounds
- Implement password strength requirements
- Secure password reset functionality

### Session Security
- Generate cryptographically secure session tokens
- Implement session expiration and refresh
- Store sessions securely in database
- Clear expired sessions automatically

### API Security
- Rate limiting on authentication endpoints
- CORS configuration for frontend-backend communication
- Input validation and sanitization
- SQL injection prevention

## Migration Strategy

### Phase 1: Backend Implementation
1. Create authentication database schema
2. Implement backend authentication service
3. Create authentication API endpoints
4. Add session management

### Phase 2: Frontend Migration
1. Update frontend authentication service to use API
2. Remove Dexie dependency for authentication
3. Implement token management
4. Update error handling

### Phase 3: Testing and Validation
1. Test complete authentication flow
2. Verify persistence across container restarts
3. Performance testing
4. Security validation

## Database Schema Changes

### New Tables
```sql
-- Users table for authentication
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP WITH TIME ZONE,
    status user_status DEFAULT 'active'
);

-- Update user_profiles to reference users
ALTER TABLE user_profiles ADD COLUMN user_id UUID REFERENCES users(id) ON DELETE CASCADE;
CREATE INDEX idx_user_profiles_user_id ON user_profiles(user_id);

-- Update user_sessions to use proper foreign key
ALTER TABLE user_sessions DROP CONSTRAINT user_sessions_user_id_fkey;
ALTER TABLE user_sessions ADD CONSTRAINT user_sessions_user_id_fkey 
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
```

### Indexes for Performance
```sql
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX idx_user_sessions_expires ON user_sessions(expires_at);
```