# Development Environment Configuration
# This file is used for local development

# Application Configuration
VITE_APP_NAME=Closet Glass Chic (Dev)
NODE_ENV=development

# Network Configuration - Local Development
VITE_BASE_URL=127.0.0.1

# Port Configuration
VITE_FRONTEND_PORT=5173
VITE_BACKEND_PORT=3001

# Non-VITE versions for Docker Compose
BASE_URL=127.0.0.1
FRONTEND_PORT=5173
BACKEND_PORT=3001

# Development Server
VITE_HOST=0.0.0.0

# Feature Flags - Development
VITE_ENABLE_DEBUG=true
VITE_ENABLE_ANALYTICS=false

# External API Services (use your own keys)
VITE_OPENWEATHER_API_KEY=********************************
VITE_OPENWEATHER_API_URL=https://api.openweathermap.org/data/2.5
VITE_NOMINATIM_API_URL=https://nominatim.openstreetmap.org
