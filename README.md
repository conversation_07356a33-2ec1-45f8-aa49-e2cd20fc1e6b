# Closet Glass Chic

A modern, minimalist fashion wardrobe management application built with React, TypeScript, and Tailwind CSS. Features a sophisticated glass morphism design inspired by ZARA's aesthetic.

## Features

- **Smart Wardrobe Management**: Organize and categorize your clothing items
- **Weather-Based Outfit Suggestions**: Get personalized recommendations based on current weather
- **Outfit Scheduling**: Plan your outfits in advance
- **Glass Morphism UI**: Beautiful, modern interface with liquid glass effects
- **Responsive Design**: Optimized for mobile and desktop
- **Dark/Light Mode**: Adaptive theming support
- **Docker Support**: Fully containerized application for easy deployment

## Tech Stack

- **Frontend**: React 18, TypeScript, Vite
- **Styling**: Tailwind CSS, Radix UI components
- **Icons**: Lucide React
- **Routing**: React Router DOM
- **State Management**: React Query (TanStack Query)
- **Form Handling**: React Hook Form with Zod validation
- **Containerization**: Docker, <PERSON>er Compose, Nginx

## Getting Started

### Option 1: Docker (Recommended)

The easiest way to run the application is using Docker:

1. Clone the repository:
```bash
git clone <repository-url>
cd closet-glass-chic
```

2. Build and run with Docker Compose:
```bash
docker compose build
docker compose up -d
```

3. Open [http://localhost:3000](http://localhost:3000) in your browser

### Environment Configuration

The application uses environment variables for configuration. You can customize the base URLs and other settings:

1. Copy the example environment file:
```bash
cp .env.example .env
```

2. Edit `.env` to match your deployment:
```bash
# Network Configuration - Single base URL for all services
BASE_URL=**************  # Your machine's IP for external access
FRONTEND_PORT=5173
BACKEND_PORT=3001
```

3. For different environments, use the appropriate env file:
- `.env.development` - Local development
- `.env.staging` - Staging environment
- `.env.production` - Production deployment

4. To stop the application:
```bash
docker compose down
```

### Option 2: Local Development

#### Prerequisites

- Node.js 18+
- npm or yarn

#### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd closet-glass-chic
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open [http://localhost:5173](http://localhost:5173) in your browser

## Docker Commands

- **Build the application**: `docker compose build`
- **Run in production mode**: `docker compose up -d`
- **Run in development mode**: `docker compose --profile dev up -d`
- **View logs**: `docker compose logs -f`
- **Stop the application**: `docker compose down`
- **Health check**: `curl http://localhost:3000/health`

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── ui/             # Base UI components (buttons, inputs, etc.)
│   │   ├── glass-button.tsx    # Standardized button component
│   │   └── glass-input.tsx     # Standardized input component
│   ├── GlassCard.tsx   # Glass morphism card component
│   ├── Layout.tsx      # Main layout wrapper
│   └── TabBar.tsx      # Bottom navigation
├── pages/              # Page components
│   ├── Home.tsx        # Dashboard/home page
│   ├── Closet.tsx      # Wardrobe management
│   ├── Schedule.tsx    # Outfit planning
│   └── Profile.tsx     # User profile
├── hooks/              # Custom React hooks
├── lib/                # Utility functions
└── assets/             # Static assets
```

## Design System

The application uses a consistent design system featuring:

- **Typography**:
  - `zara-title`: Large headings with light font weight
  - `zara-subtitle`: Medium headings with extra-light weight
  - `zara-body`: Body text with normal weight
- **Colors**: Monochromatic palette with HSL color system
- **Glass Morphism**: Multiple glass effect variants
  - `glass-subtle`: Light glass effect for backgrounds
  - `glass-panel`: Standard glass effect for cards
  - `glass-modal`: Strong glass effect for modals
  - `glass-navigation`: Navigation-specific glass effect
- **Components**: Standardized UI components with consistent styling
  - `GlassButton`: Unified button component with multiple variants
  - `GlassInput`: Consistent input styling with glass effects
  - `GlassCard`: Flexible card component with glass morphism
- **Spacing**: Consistent padding and margin patterns
- **Animations**: Smooth transitions and micro-interactions

## Environment Variables

The application supports the following environment variables (see `.env` file):

- `VITE_APP_NAME`: Application name
- `VITE_APP_VERSION`: Application version
- `NODE_ENV`: Environment (development/production)
- `VITE_HOST`: Development server host
- `VITE_PORT`: Development server port

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Commit your changes: `git commit -am 'Add new feature'`
4. Push to the branch: `git push origin feature/new-feature`
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
