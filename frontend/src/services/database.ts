import Dexie, { Table } from 'dexie';
import { UserProfile, UserProfileTable } from '@/types/user';

// Database schema
export class ClosetDatabase extends Dexie {
  userProfiles!: Table<UserProfileTable>;

  constructor() {
    super(import.meta.env.VITE_DB_NAME || 'ClosetGlassChic');
    
    this.version(1).stores({
      userProfiles: '++id, firstName, lastName, gender, dateOfBirth, cityName, latitude, longitude, weatherPreferences, createdAt, updatedAt'
    });
  }
}

export const db = new ClosetDatabase();

// Database operations
export class UserProfileService {
  
    // Create a new user profile
  static async createUserProfile(profile: Omit<UserProfile, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const now = new Date().toISOString();
      const userProfileData: Omit<UserProfileTable, 'id'> = {
        firstName: profile.firstName,
        lastName: profile.lastName,
        gender: profile.gender,
        dateOfBirth: profile.dateOfBirth,
        cityName: profile.cityName,
        latitude: profile.latitude,
        longitude: profile.longitude,
        weatherPreferences: JSON.stringify(profile.weatherPreferences || {}),
        createdAt: now,
        updatedAt: now,
      };
      
      const validation = UserProfileService.validateUserProfile(profile);
      if (!validation.isValid) {
        throw new Error(`Invalid user profile: ${validation.errors.join(', ')}`);
      }

      const id = await db.userProfiles.add(userProfileData as UserProfileTable);
      return id.toString();
    } catch (error) {
      console.error('Error creating user profile:', error);
      throw new Error('Failed to create user profile');
    }
  }

  // Get user profile by ID
  static async getUserProfile(id: string): Promise<UserProfile | null> {
    try {
      const profile = await db.userProfiles.get(id);
      if (!profile) return null;

      return this.mapTableToProfile(profile);
    } catch (error) {
      console.error('Error getting user profile:', error);
      throw new Error('Failed to retrieve user profile');
    }
  }

  // Get the current user profile (assuming single user for now)
  static async getCurrentUserProfile(): Promise<UserProfile | null> {
    try {
      const profiles = await db.userProfiles.orderBy('createdAt').reverse().limit(1).toArray();
      if (profiles.length === 0) return null;

      return this.mapTableToProfile(profiles[0]);
    } catch (error) {
      console.error('Error getting current user profile:', error);
      throw new Error('Failed to retrieve current user profile');
    }
  }

  // Update user profile
  static async updateUserProfile(id: string, updates: Partial<Omit<UserProfile, 'id' | 'createdAt'>>): Promise<void> {
    try {
      const updateData: Partial<UserProfileTable> = {
        ...updates,
        weatherPreferences: updates.weatherPreferences ? JSON.stringify(updates.weatherPreferences) : undefined,
        updatedAt: new Date().toISOString(),
      };

      await db.userProfiles.update(id, updateData);
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw new Error('Failed to update user profile');
    }
  }

  // Delete user profile
  static async deleteUserProfile(id: string): Promise<void> {
    try {
      await db.userProfiles.delete(id);
    } catch (error) {
      console.error('Error deleting user profile:', error);
      throw new Error('Failed to delete user profile');
    }
  }

  // Check if user profile exists
  static async hasUserProfile(): Promise<boolean> {
    try {
      const count = await db.userProfiles.count();
      return count > 0;
    } catch (error) {
      console.error('Error checking user profile existence:', error);
      return false;
    }
  }

  // Clear all user profiles (for testing/reset)
  static async clearAllProfiles(): Promise<void> {
    try {
      await db.userProfiles.clear();
    } catch (error) {
      console.error('Error clearing user profiles:', error);
      throw new Error('Failed to clear user profiles');
    }
  }

  // Validate user profile data
  static validateUserProfile(profile: Partial<UserProfile>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!profile.firstName || profile.firstName.trim().length < 1) {
      errors.push('First name is required');
    }

    if (!profile.lastName || profile.lastName.trim().length < 1) {
      errors.push('Last name is required');
    }

    if (!profile.gender || !['Male', 'Female', 'Other', 'Prefer not to say'].includes(profile.gender)) {
      errors.push('Valid gender selection is required');
    }

    if (!profile.dateOfBirth) {
      errors.push('Date of birth is required');
    } else {
      const birthDate = new Date(profile.dateOfBirth);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      
      if (age < 13 || age > 120) {
        errors.push('Age must be between 13 and 120 years');
      }
    }

    if (!profile.cityName || profile.cityName.trim().length < 2) {
      errors.push('City name is required');
    }

    if (typeof profile.latitude !== 'number' || typeof profile.longitude !== 'number') {
      errors.push('Valid location coordinates are required');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  // Helper method to map database table to UserProfile
  private static mapTableToProfile(tableData: UserProfileTable): UserProfile {
    return {
      id: tableData.id,
      firstName: tableData.firstName,
      lastName: tableData.lastName,
      gender: tableData.gender as UserProfile['gender'],
      dateOfBirth: tableData.dateOfBirth,
      cityName: tableData.cityName,
      latitude: tableData.latitude,
      longitude: tableData.longitude,
      weatherPreferences: tableData.weatherPreferences ? JSON.parse(tableData.weatherPreferences) : undefined,
      createdAt: tableData.createdAt,
      updatedAt: tableData.updatedAt,
    };
  }
}

// Initialize database
export async function initializeDatabase(): Promise<void> {
  try {
    await db.open();
    console.log('Database initialized successfully');
  } catch (error) {
    console.error('Failed to initialize database:', error);
    throw new Error('Database initialization failed');
  }
}

// Export database instance for direct access if needed
export { db as database };
