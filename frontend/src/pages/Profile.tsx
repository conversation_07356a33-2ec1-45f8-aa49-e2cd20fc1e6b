import { User, Setting<PERSON>, <PERSON>, Shield, HelpCircle } from 'lucide-react';
import { GlassCard } from '@/components/GlassCard';
import { GlassButton } from '@/components/ui/glass-button';
import fashionHeroBg from '@/assets/fashion-hero-bg.jpg';

export const Profile = () => {
  const profileSections = [
    {
      title: 'Account',
      items: [
        { icon: User, label: 'Personal Information', action: () => {} },
        { icon: Settings, label: 'Preferences', action: () => {} }
      ]
    },
    {
      title: 'Notifications',
      items: [
        { icon: Bell, label: 'Weather Alerts', action: () => {} },
        { icon: Bell, label: 'Outfit Reminders', action: () => {} }
      ]
    },
    {
      title: 'Support',
      items: [
        { icon: Shield, label: 'Privacy & Security', action: () => {} },
        { icon: HelpCircle, label: 'Help & Support', action: () => {} }
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-white relative overflow-hidden">
      {/* Subtle background pattern for consistency */}
      <div className="absolute inset-0 opacity-[0.02] z-0 pointer-events-none">
        <div className="w-full h-full" style={{
          backgroundImage: `radial-gradient(circle at 25% 25%, hsl(var(--zara-charcoal)) 1px, transparent 1px)`,
          backgroundSize: '60px 60px'
        }} />
      </div>
      {/* Content */}
      <div className="relative z-10 px-6 pt-20 pb-32">
        {/* Header */}
        <h1 className="zara-title mb-6">Profile</h1>
        {/* Profile card */}
        <div className="max-w-xl mx-auto">
          <div className="bg-white p-6 mb-6 rounded-2xl shadow-lg border border-zara-light-gray">
            <div className="flex items-center space-x-4">
              {/* Profile photo */}
              <div className="relative w-16 h-16">
                <div className="w-full h-full rounded-full bg-zara-light-gray flex items-center justify-center">
                  <User size={28} className="text-muted-foreground" />
                </div>
                <div className="absolute inset-0 rounded-full ring-2 ring-zara-light-gray/40" />
              </div>
              {/* Profile info */}
              <div className="flex-1">
                <h2 className="zara-subtitle">Your Name</h2>
                <p className="zara-body text-muted-foreground">City, Country</p>
                <p className="zara-body text-muted-foreground mt-1">Member since January 2024</p>
              </div>
              <GlassButton variant="secondary" size="sm">
                Edit
              </GlassButton>
            </div>
          </div>
        </div>
        {/* Profile sections */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-3xl mx-auto">
          {profileSections.map((section, sectionIdx) => (
            <div
              key={`${section.title}-${sectionIdx}`}
              className="bg-white p-5 rounded-2xl border border-zara-light-gray shadow-md"
            >
              <h2 className="mb-4 text-lg font-semibold text-zara-charcoal tracking-tight">
                {section.title}
              </h2>
              <div className="space-y-3">
                {section.items.map((item, itemIdx) => (
                  <button
                    key={`${section.title}-${item.label}-${itemIdx}`}
                    className="flex items-center w-full px-3 py-2 rounded-lg hover:bg-zara-light-gray/40 transition group"
                    onClick={item.action}
                  >
                    <item.icon size={20} className="mr-3 text-muted-foreground group-hover:text-zara-charcoal transition" />
                    <span className="text-base text-zara-charcoal group-hover:font-semibold transition">
                      {item.label}
                    </span>
                  </button>
                ))}
              </div>
            </div>
          ))}
          {/* Sign out */}
          <div className="bg-white p-4 rounded-2xl border border-zara-light-gray shadow-md">
            <GlassButton variant="destructive" className="w-full">
              Sign Out
            </GlassButton>
          </div>
        </div>
      </div>
    </div>
  );
};