import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { Layout } from "./components/Layout";
import { Home } from "./pages/Home";
import { Closet } from "./pages/Closet";
import { Schedule } from "./pages/Schedule";
import { Profile } from "./pages/Profile";
import NotFound from "./pages/NotFound";
import { OnboardingProvider } from "./contexts/OnboardingContext";
import { OnboardingRouter } from "./components/onboarding/OnboardingRouter";
import { useOnboarding } from "./hooks/useOnboarding";
import { initializeDatabase } from "./services/database";
import { ErrorBoundary } from "./components/ErrorBoundary";
import { useEffect } from "react";

const queryClient = new QueryClient();

// Main App Component with Onboarding Integration
const MainApp = () => {
  const { isCompleted, resetOnboarding } = useOnboarding();

  // Initialize database on app start
  useEffect(() => {
    initializeDatabase().catch(error => {
      console.error('Failed to initialize database:', error);
    });
  }, []);

  const handleOnboardingComplete = () => {
    // Onboarding completion is handled by the context
    // Additional completion logic can be added here if needed
    console.log('Onboarding completed successfully');
  };

  // Add reset functionality for testing (can be removed in production)
  const handleResetOnboarding = () => {
    if (window.confirm('Are you sure you want to reset the onboarding? This will clear all progress.')) {
      resetOnboarding();
    }
  };

  // Show onboarding if not completed
  if (!isCompleted) {
    return <OnboardingRouter onComplete={handleOnboardingComplete} />;
  }

  // Show main application
  return (
    <BrowserRouter>
      <Layout>
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/closet" element={<Closet />} />
          <Route path="/schedule" element={<Schedule />} />
          <Route path="/profile" element={<Profile />} />
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>

        {/* Development helper - remove in production */}
        {process.env.NODE_ENV === 'development' && (
          <button
            onClick={handleResetOnboarding}
            className="fixed bottom-4 left-4 z-50 px-3 py-1 text-xs bg-red-500 text-white rounded opacity-50 hover:opacity-100"
            title="Reset Onboarding (Dev Only)"
          >
            Reset Onboarding
          </button>
        )}
      </Layout>
    </BrowserRouter>
  );
};

const App = () => (
  <ErrorBoundary>
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <OnboardingProvider>
          <Toaster />
          <Sonner />
          <MainApp />
        </OnboardingProvider>
      </TooltipProvider>
    </QueryClientProvider>
  </ErrorBoundary>
);

export default App;
