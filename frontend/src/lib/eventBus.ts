// Simple app-wide event bus for reactive updates without a full state library
// Usage:
//  - emitClosetItemAdded(item)
//  - const unsubscribe = onClosetItemAdded(handler)

export type ClosetItem = {
  id: string;
  name: string;
  category: string;
  color: string;
  brand?: string;
  season?: string;
  image?: string;
};

const bus = new EventTarget();

const EVENTS = {
  CLOSET_ITEM_ADDED: 'closet:item-added',
} as const;

type ClosetItemAddedDetail = { item: ClosetItem };

export function emitClosetItemAdded(item: ClosetItem): void {
  bus.dispatchEvent(new CustomEvent<ClosetItemAddedDetail>(EVENTS.CLOSET_ITEM_ADDED, { detail: { item } }));
}

export function onClosetItemAdded(handler: (item: ClosetItem) => void): () => void {
  const listener = (e: Event) => {
    const ce = e as CustomEvent<ClosetItemAddedDetail>;
    if (ce.detail?.item) handler(ce.detail.item);
  };
  bus.addEventListener(EVENTS.CLOSET_ITEM_ADDED, listener as EventListener);
  return () => bus.removeEventListener(EVENTS.CLOSET_ITEM_ADDED, listener as EventListener);
}
