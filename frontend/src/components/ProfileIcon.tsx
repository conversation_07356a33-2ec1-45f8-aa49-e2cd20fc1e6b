import { User } from 'lucide-react';
import { useState } from 'react';
import { GlassCard } from './GlassCard';

export const ProfileIcon = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>
      <button
        onClick={() => setIsModalOpen(true)}
        className="fixed top-6 right-6 z-40 p-3 glass-subtle rounded-full hover:scale-110 transition-all duration-200"
      >
        <User size={20} className="text-foreground" />
      </button>

      {isModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-6">
          {/* Background overlay */}
          <div 
            className="absolute inset-0 bg-black/20 backdrop-blur-sm"
            onClick={() => setIsModalOpen(false)}
          />
          
          {/* Profile modal */}
          <GlassCard variant="modal" className="relative w-full max-w-sm p-8">
            <div className="text-center space-y-6">
              {/* Profile photo */}
              <div className="relative mx-auto w-20 h-20">
                <div className="w-full h-full rounded-full bg-muted flex items-center justify-center glass-subtle">
                  <User size={32} className="text-muted-foreground" />
                </div>
                <div className="absolute inset-0 rounded-full ring-2 ring-white/30" />
              </div>

              {/* Profile info */}
              <div className="space-y-4">
                <div>
                  <label className="zara-body text-muted-foreground">Name</label>
                  <input 
                    type="text" 
                    placeholder="Your name"
                    className="w-full mt-1 px-4 py-3 bg-white/20 border border-white/30 rounded-xl zara-body placeholder:text-muted-foreground/60 focus:outline-none focus:ring-2 focus:ring-primary/30"
                  />
                </div>
                
                <div>
                  <label className="zara-body text-muted-foreground">Location</label>
                  <input 
                    type="text" 
                    placeholder="City, Country"
                    className="w-full mt-1 px-4 py-3 bg-white/20 border border-white/30 rounded-xl zara-body placeholder:text-muted-foreground/60 focus:outline-none focus:ring-2 focus:ring-primary/30"
                  />
                </div>
              </div>

              {/* Action buttons */}
              <div className="flex space-x-3 pt-4">
                <button
                  onClick={() => setIsModalOpen(false)}
                  className="flex-1 px-6 py-3 bg-white/10 border border-white/20 rounded-xl zara-body text-muted-foreground hover:bg-white/20 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => setIsModalOpen(false)}
                  className="flex-1 px-6 py-3 bg-primary text-primary-foreground rounded-xl zara-body hover:bg-primary/90 transition-colors"
                >
                  Save
                </button>
              </div>
            </div>
          </GlassCard>
        </div>
      )}
    </>
  );
};