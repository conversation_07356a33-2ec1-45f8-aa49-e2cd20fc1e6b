import { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { TabBar } from './TabBar';
import { ProfileIcon } from './ProfileIcon';

interface LayoutProps {
  children: React.ReactNode;
}

export const Layout = ({ children }: LayoutProps) => {
  const [isTabBarVisible, setIsTabBarVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);
  const location = useLocation();

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      
      // Hide tab bar when scrolling down, show when scrolling up
      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        setIsTabBarVisible(false);
      } else {
        setIsTabBarVisible(true);
      }
      
      setLastScrollY(currentScrollY);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [lastScrollY]);

  // Always show tab bar on profile page
  const showTabBar = location.pathname === '/profile' || isTabBarVisible;

  return (
    <div className="min-h-screen">
      <ProfileIcon />
      {children}
      <TabBar isVisible={showTabBar} />
    </div>
  );
};