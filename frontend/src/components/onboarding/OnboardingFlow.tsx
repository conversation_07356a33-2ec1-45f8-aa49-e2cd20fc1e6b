import { useState } from 'react';
import { useOnboarding } from '@/hooks/useOnboarding';
import { OnboardingLayout } from './OnboardingLayout';
import { WelcomeScreen } from './WelcomeScreen';
import { FeatureIntroScreen } from './FeatureIntroScreen';
import { UserProfileForm } from './UserProfileForm';
import { cn } from '@/lib/utils';
import { UserProfileFormData } from '@/types/user';
import { UserProfileService } from '@/services/database';

// Feature illustrations as SVG components
const ClosetIllustration = () => (
  <div className="flex items-center justify-center w-full h-full">
    <svg className="w-24 h-24 text-zara-charcoal" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} 
            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
    </svg>
  </div>
);

const OutfitIllustration = () => (
  <div className="flex items-center justify-center w-full h-full">
    <svg className="w-24 h-24 text-zara-charcoal" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} 
            d="M8 7V3a1 1 0 011-1h6a1 1 0 011 1v4h4a1 1 0 011 1v2a1 1 0 01-1 1h-4v8a2 2 0 01-2 2H10a2 2 0 01-2-2v-8H4a1 1 0 01-1-1V8a1 1 0 011-1h4z" />
    </svg>
  </div>
);

const StyleIllustration = () => (
  <div className="flex items-center justify-center w-full h-full">
    <svg className="w-24 h-24 text-zara-charcoal" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} 
            d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
    </svg>
  </div>
);

interface OnboardingFlowProps {
  onComplete: () => void;
}

export const OnboardingFlow = ({ onComplete }: OnboardingFlowProps) => {
  const {
    currentStep,
    totalSteps,
    currentStepData,
    nextStep,
    previousStep,
    skipStep,
    completeOnboarding,
    updateUserProfile,
    canGoPrevious,
    isFirstStep,
    isLastStep
  } = useOnboarding();

  const [isTransitioning, setIsTransitioning] = useState(false);
  const [direction, setDirection] = useState<'forward' | 'backward'>('forward');

  // Handle smooth transitions between screens
  const handleTransition = (callback: () => void, transitionDirection: 'forward' | 'backward' = 'forward') => {
    setDirection(transitionDirection);
    setIsTransitioning(true);
    
    setTimeout(() => {
      callback();
      setTimeout(() => {
        setIsTransitioning(false);
      }, 100);
    }, 300);
  };

  const handleNext = () => {
    if (isLastStep) {
      handleTransition(() => {
        completeOnboarding();
        onComplete();
      });
    } else {
      handleTransition(nextStep, 'forward');
    }
  };

  // Handle user profile form submission
  const handleUserProfileSubmit = async (profileData: UserProfileFormData) => {
    try {
      // Save user profile to context
      updateUserProfile(profileData);

      // Save to database if city is selected
      if (profileData.selectedCity) {
        const userProfile = {
          firstName: profileData.firstName,
          lastName: profileData.lastName,
          gender: profileData.gender as any,
          dateOfBirth: profileData.dateOfBirth,
          cityName: profileData.cityName,
          latitude: parseFloat(profileData.selectedCity.lat),
          longitude: parseFloat(profileData.selectedCity.lon),
        };

        await UserProfileService.createUserProfile(userProfile);
      }

      // Continue to next step
      handleTransition(nextStep, 'forward');
    } catch (error) {
      console.error('Error saving user profile:', error);
      // Still continue to next step even if database save fails
      handleTransition(nextStep, 'forward');
    }
  };

  const handlePrevious = () => {
    if (canGoPrevious) {
      handleTransition(previousStep, 'backward');
    }
  };

  const handleSkip = () => {
    handleTransition(() => {
      completeOnboarding();
      onComplete();
    });
  };

  const handleSkipStep = () => {
    handleTransition(skipStep, 'forward');
  };

  // Get illustration for current step
  const getIllustration = (stepId: string) => {
    switch (stepId) {
      case 'closet-management':
        return <ClosetIllustration />;
      case 'outfit-planning':
        return <OutfitIllustration />;
      case 'style-preferences':
        return <StyleIllustration />;
      default:
        return <ClosetIllustration />;
    }
  };

  // Welcome screen (step 0)
  if (isFirstStep) {
    return (
      <OnboardingLayout 
        className={cn(
          "transition-all duration-500 ease-liquid",
          isTransitioning && direction === 'forward' && "opacity-0 translate-x-full",
          isTransitioning && direction === 'backward' && "opacity-0 -translate-x-full"
        )}
      >
        <WelcomeScreen 
          onContinue={() => handleTransition(nextStep, 'forward')}
          onSkip={handleSkip}
        />
      </OnboardingLayout>
    );
  }

  // User profile form (step 1)
  if (currentStepData && currentStepData.id === 'user-profile') {
    return (
      <OnboardingLayout
        className={cn(
          "transition-all duration-500 ease-liquid",
          isTransitioning && direction === 'forward' && "opacity-0 translate-x-full",
          isTransitioning && direction === 'backward' && "opacity-0 -translate-x-full"
        )}
      >
        <UserProfileForm
          onNext={handleUserProfileSubmit}
          onPrevious={handlePrevious}
          onSkip={handleSkipStep}
          canSkip={currentStepData.canSkip}
          currentStep={currentStep + 1}
          totalSteps={totalSteps}
        />
      </OnboardingLayout>
    );
  }

  // Feature introduction screens
  if (currentStepData) {
    return (
      <OnboardingLayout
        showSkipButton={currentStepData.canSkip}
        onSkip={handleSkipStep}
        className={cn(
          "transition-all duration-500 ease-liquid",
          isTransitioning && direction === 'forward' && "opacity-0 translate-x-full",
          isTransitioning && direction === 'backward' && "opacity-0 -translate-x-full"
        )}
      >
        <FeatureIntroScreen
          title={currentStepData.title}
          description={currentStepData.description}
          illustration={getIllustration(currentStepData.id)}
          currentStep={currentStep + 1} // +1 because we show step numbers starting from 1
          totalSteps={totalSteps}
          onNext={handleNext}
          onPrevious={handlePrevious}
          onSkip={handleSkipStep}
          canSkip={currentStepData.canSkip}
        />
      </OnboardingLayout>
    );
  }

  // Fallback - should not reach here
  return null;
};