import { useEffect, useState } from 'react';
import { GlassCard } from '../GlassCard';
import { ZaraButton } from '../ui/zara-button';
import { ProgressIndicator } from '../ui/progress-indicator';
import { cn } from '@/lib/utils';

interface FeatureIntroScreenProps {
  title: string;
  description: string;
  illustration: React.ReactNode;
  currentStep: number;
  totalSteps: number;
  onNext: () => void;
  onPrevious: () => void;
  onSkip: () => void;
  canSkip?: boolean;
}

export const FeatureIntroScreen = ({
  title,
  description,
  illustration,
  currentStep,
  totalSteps,
  onNext,
  onPrevious,
  onSkip,
  canSkip = true
}: FeatureIntroScreenProps) => {
  const [isVisible, setIsVisible] = useState(false);
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  // Handle swipe gestures
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;
    
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe && currentStep < totalSteps) {
      onNext();
    }
    if (isRightSwipe && currentStep > 1) {
      onPrevious();
    }
  };

  const isFirstStep = currentStep === 1;
  const isLastStep = currentStep === totalSteps;

  return (
    <div 
      className="min-h-screen relative overflow-hidden bg-gradient-to-br from-zara-white via-zara-light-gray to-zara-medium-gray"
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {/* Subtle background pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="w-full h-full" style={{
          backgroundImage: `radial-gradient(circle at 25% 25%, hsl(var(--zara-charcoal)) 1px, transparent 1px)`,
          backgroundSize: '50px 50px'
        }} />
      </div>

      {/* Main content */}
      <div className="relative z-10 min-h-screen flex flex-col">
        {/* Progress indicator */}
        <div className="pt-12 pb-6 px-6">
          <ProgressIndicator
            currentStep={currentStep}
            totalSteps={totalSteps}
            variant="dots"
            size="md"
            className={cn(
              "transition-all duration-500 ease-premium",
              isVisible ? "opacity-100 translate-y-0" : "opacity-0 -translate-y-4"
            )}
          />
        </div>

        {/* Feature content */}
        <div className="flex-1 flex items-center justify-center p-6">
          <GlassCard 
            variant="modal"
            className={cn(
              "max-w-lg w-full p-8 space-y-8 animate-glass-scale-in",
              "transition-all duration-800 ease-liquid",
              isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
            )}
          >
            {/* Illustration area */}
            <div className={cn(
              "flex items-center justify-center h-48 glass-subtle rounded-2xl",
              "transition-all duration-600 ease-premium",
              isVisible ? "opacity-100 scale-100" : "opacity-0 scale-95"
            )}
            style={{ animationDelay: '200ms' }}>
              {illustration}
            </div>

            {/* Content */}
            <div className="text-center space-y-4">
              <h2 className={cn(
                "zara-title transition-all duration-600 ease-premium",
                isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-4"
              )}
              style={{ animationDelay: '400ms' }}>
                {title}
              </h2>
              
              <p className={cn(
                "zara-body text-zara-dark-gray leading-relaxed",
                "transition-all duration-600 ease-premium",
                isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-4"
              )}
              style={{ animationDelay: '600ms' }}>
                {description}
              </p>
            </div>

            {/* Navigation buttons */}
            <div className="flex items-center justify-between pt-4 gap-4">
              <ZaraButton
                variant="ghost"
                size="md"
                onClick={onPrevious}
                disabled={isFirstStep}
                className={cn(
                  "transition-all duration-600 ease-premium",
                  isVisible ? "opacity-100 translate-x-0" : "opacity-0 -translate-x-4",
                  isFirstStep && "opacity-50 cursor-not-allowed"
                )}
                style={{ animationDelay: '800ms' }}
              >
                Previous
              </ZaraButton>

              <div className="flex gap-3">
                {canSkip && (
                  <ZaraButton
                    variant="minimal"
                    size="md"
                    onClick={onSkip}
                    className={cn(
                      "text-zara-dark-gray hover:text-zara-charcoal",
                      "transition-all duration-600 ease-premium",
                      isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-4"
                    )}
                    style={{ animationDelay: '1000ms' }}
                  >
                    Skip
                  </ZaraButton>
                )}

                <ZaraButton
                  variant="primary"
                  size="md"
                  morphOnHover
                  onClick={onNext}
                  className={cn(
                    "transition-all duration-600 ease-premium",
                    isVisible ? "opacity-100 translate-x-0" : "opacity-0 translate-x-4"
                  )}
                  style={{ animationDelay: '800ms' }}
                >
                  {isLastStep ? 'Get Started' : 'Next'}
                </ZaraButton>
              </div>
            </div>
          </GlassCard>
        </div>

        {/* Swipe hint */}
        <div className="pb-8 px-6">
          <p className={cn(
            "text-center zara-caption text-zara-dark-gray",
            "transition-all duration-600 ease-premium",
            isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-4"
          )}
          style={{ animationDelay: '1200ms' }}>
            Swipe left or right to navigate
          </p>
        </div>
      </div>

      {/* Floating decorative elements */}
      <div className="absolute top-1/4 left-8 w-6 h-6 glass-subtle rounded-full animate-pulse opacity-20" />
      <div className="absolute bottom-1/3 right-12 w-4 h-4 glass-subtle rounded-full animate-pulse opacity-15" 
           style={{ animationDelay: '1.5s' }} />
      <div className="absolute top-2/3 left-16 w-8 h-8 glass-subtle rounded-full animate-pulse opacity-25" 
           style={{ animationDelay: '3s' }} />
    </div>
  );
};