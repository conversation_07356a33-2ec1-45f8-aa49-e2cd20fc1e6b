import { useEffect, useState } from 'react';
import { GlassCard } from '../GlassCard';
import { ZaraButton } from '../ui/zara-button';
import { cn } from '@/lib/utils';

interface WelcomeScreenProps {
  onContinue: () => void;
  onSkip: () => void;
}

export const WelcomeScreen = ({ onContinue, onSkip }: WelcomeScreenProps) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Trigger animation after component mounts
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="min-h-screen relative overflow-hidden bg-gradient-to-br from-zara-white via-zara-light-gray to-zara-medium-gray">
      {/* Fashion background with overlay */}
      <div className="absolute inset-0">
        <div 
          className="w-full h-full bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: `url('/src/assets/fashion-hero-bg.jpg')`,
          }}
        />
        <div className="absolute inset-0 bg-gradient-to-t from-zara-white/90 via-zara-white/60 to-zara-white/30" />
      </div>

      {/* Main content */}
      <div className="relative z-10 min-h-screen flex items-center justify-center p-6">
        <GlassCard 
          variant="hero" 
          morphOnHover={false}
          className={cn(
            "max-w-md w-full p-8 text-center space-y-8 animate-glass-scale-in",
            "transition-all duration-800 ease-liquid",
            isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
          )}
        >
          {/* Logo/Brand area */}
          <div className="space-y-4">
            <div className="w-16 h-16 mx-auto rounded-full glass-medium flex items-center justify-center">
              <svg 
                className="w-8 h-8 text-zara-charcoal" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={1.5} 
                  d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" 
                />
              </svg>
            </div>
            
            <h1 className="zara-hero">Welcome</h1>
          </div>

          {/* Welcome message */}
          <div className="space-y-4">
            <h2 className="zara-title">Your Fashion Assistant</h2>
            <p className="zara-body text-zara-dark-gray leading-relaxed">
              Discover a new way to organize your wardrobe and plan your outfits with style. 
              Let's create your perfect fashion experience.
            </p>
          </div>

          {/* Action buttons */}
          <div className="space-y-4 pt-4">
            <ZaraButton
              variant="primary"
              size="lg"
              fullWidth
              morphOnHover
              onClick={onContinue}
              className="animate-glass-fade-in"
              style={{ animationDelay: '400ms' }}
            >
              Get Started
            </ZaraButton>
            
            <ZaraButton
              variant="ghost"
              size="md"
              fullWidth
              onClick={onSkip}
              className={cn(
                "animate-glass-fade-in text-zara-dark-gray",
                "hover:text-zara-charcoal transition-colors"
              )}
              style={{ animationDelay: '600ms' }}
            >
              Skip for now
            </ZaraButton>
          </div>

          {/* Subtle decorative elements */}
          <div className="absolute -top-4 -right-4 w-8 h-8 glass-subtle rounded-full opacity-60" />
          <div className="absolute -bottom-4 -left-4 w-6 h-6 glass-subtle rounded-full opacity-40" />
        </GlassCard>
      </div>

      {/* Floating glass elements for ambiance */}
      <div className="absolute top-20 left-10 w-12 h-12 glass-subtle rounded-full animate-pulse opacity-30" />
      <div className="absolute bottom-32 right-16 w-8 h-8 glass-subtle rounded-full animate-pulse opacity-20" 
           style={{ animationDelay: '1s' }} />
      <div className="absolute top-1/3 right-8 w-4 h-4 glass-subtle rounded-full animate-pulse opacity-25" 
           style={{ animationDelay: '2s' }} />
    </div>
  );
};