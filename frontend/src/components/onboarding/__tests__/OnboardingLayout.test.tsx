import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { OnboardingLayout } from '../OnboardingLayout';

describe('OnboardingLayout', () => {
  const mockOnSkip = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders children correctly', () => {
    render(
      <OnboardingLayout>
        <div data-testid="test-child">Test Content</div>
      </OnboardingLayout>
    );
    
    expect(screen.getByTestId('test-child')).toBeInTheDocument();
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  it('shows skip button when showSkipButton is true', () => {
    render(
      <OnboardingLayout showSkipButton onSkip={mockOnSkip}>
        <div>Content</div>
      </OnboardingLayout>
    );
    
    expect(screen.getByRole('button', { name: 'Skip' })).toBeInTheDocument();
  });

  it('hides skip button when showSkipButton is false', () => {
    render(
      <OnboardingLayout showSkipButton={false}>
        <div>Content</div>
      </OnboardingLayout>
    );
    
    expect(screen.queryByRole('button', { name: 'Skip' })).not.toBeInTheDocument();
  });

  it('calls onSkip when skip button is clicked', () => {
    render(
      <OnboardingLayout showSkipButton onSkip={mockOnSkip}>
        <div>Content</div>
      </OnboardingLayout>
    );
    
    const skipButton = screen.getByRole('button', { name: 'Skip' });
    fireEvent.click(skipButton);
    
    expect(mockOnSkip).toHaveBeenCalledTimes(1);
  });

  it('applies custom className', () => {
    render(
      <OnboardingLayout className="custom-class">
        <div>Content</div>
      </OnboardingLayout>
    );
    
    const container = document.querySelector('.custom-class');
    expect(container).toBeInTheDocument();
  });

  it('has proper layout structure', () => {
    render(
      <OnboardingLayout>
        <div>Content</div>
      </OnboardingLayout>
    );
    
    const container = document.querySelector('.min-h-screen.relative.overflow-hidden');
    expect(container).toBeInTheDocument();
  });

  it('applies visibility animation', async () => {
    render(
      <OnboardingLayout>
        <div>Content</div>
      </OnboardingLayout>
    );
    
    // Initially should have opacity-0
    const container = document.querySelector('.min-h-screen');
    expect(container).toHaveClass('opacity-0');
    
    // Should become visible after animation
    await waitFor(() => {
      expect(container).toHaveClass('opacity-100');
    }, { timeout: 1000 });
  });

  it('renders ambient background elements', () => {
    render(
      <OnboardingLayout>
        <div>Content</div>
      </OnboardingLayout>
    );
    
    // Check for floating glass orbs
    const floatingOrbs = document.querySelectorAll('.glass-subtle.rounded-full.animate-pulse');
    expect(floatingOrbs.length).toBeGreaterThan(0);
    
    // Check for gradient overlay
    const gradientOverlay = document.querySelector('.bg-gradient-to-br');
    expect(gradientOverlay).toBeInTheDocument();
  });

  it('skip button has proper styling and focus states', () => {
    render(
      <OnboardingLayout showSkipButton onSkip={mockOnSkip}>
        <div>Content</div>
      </OnboardingLayout>
    );
    
    const skipButton = screen.getByRole('button', { name: 'Skip' });
    expect(skipButton).toHaveClass('glass-subtle', 'rounded-full');
    expect(skipButton).toHaveClass('focus:outline-none', 'focus:ring-2');
  });

  it('skip button is positioned correctly', () => {
    render(
      <OnboardingLayout showSkipButton onSkip={mockOnSkip}>
        <div>Content</div>
      </OnboardingLayout>
    );
    
    const skipButton = screen.getByRole('button', { name: 'Skip' });
    const buttonContainer = skipButton.parentElement;
    expect(buttonContainer).toHaveClass('absolute', 'top-6', 'right-6', 'z-50');
  });

  it('handles missing onSkip prop gracefully', () => {
    render(
      <OnboardingLayout showSkipButton>
        <div>Content</div>
      </OnboardingLayout>
    );
    
    // Should not render skip button if onSkip is not provided
    expect(screen.queryByRole('button', { name: 'Skip' })).not.toBeInTheDocument();
  });

  it('applies proper transition classes', () => {
    render(
      <OnboardingLayout>
        <div>Content</div>
      </OnboardingLayout>
    );
    
    const container = document.querySelector('.min-h-screen');
    expect(container).toHaveClass('transition-all', 'duration-1000', 'ease-liquid');
    
    const contentWrapper = document.querySelector('.transition-all.duration-800.ease-liquid');
    expect(contentWrapper).toBeInTheDocument();
  });
});