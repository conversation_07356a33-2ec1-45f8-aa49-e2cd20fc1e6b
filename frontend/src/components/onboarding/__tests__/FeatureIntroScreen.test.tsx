import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { FeatureIntroScreen } from '../FeatureIntroScreen';

describe('FeatureIntroScreen', () => {
  const mockOnNext = vi.fn();
  const mockOnPrevious = vi.fn();
  const mockOnSkip = vi.fn();

  const defaultProps = {
    title: 'Test Feature',
    description: 'This is a test feature description',
    illustration: <div data-testid="test-illustration">Test Illustration</div>,
    currentStep: 2,
    totalSteps: 4,
    onNext: mockOnNext,
    onPrevious: mockOnPrevious,
    onSkip: mockOnSkip,
    canSkip: true
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders feature intro screen with correct content', () => {
    render(<FeatureIntroScreen {...defaultProps} />);
    
    expect(screen.getByText('Test Feature')).toBeInTheDocument();
    expect(screen.getByText('This is a test feature description')).toBeInTheDocument();
    expect(screen.getByTestId('test-illustration')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Next' })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Previous' })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Skip' })).toBeInTheDocument();
  });

  it('displays correct progress indicator', () => {
    render(<FeatureIntroScreen {...defaultProps} />);
    
    // Progress indicator should show current step
    const progressDots = document.querySelectorAll('.rounded-full');
    expect(progressDots.length).toBeGreaterThanOrEqual(4); // At least totalSteps dots
  });

  it('calls onNext when Next button is clicked', () => {
    render(<FeatureIntroScreen {...defaultProps} />);
    
    const nextButton = screen.getByRole('button', { name: 'Next' });
    fireEvent.click(nextButton);
    
    expect(mockOnNext).toHaveBeenCalledTimes(1);
  });

  it('calls onPrevious when Previous button is clicked', () => {
    render(<FeatureIntroScreen {...defaultProps} />);
    
    const previousButton = screen.getByRole('button', { name: 'Previous' });
    fireEvent.click(previousButton);
    
    expect(mockOnPrevious).toHaveBeenCalledTimes(1);
  });

  it('calls onSkip when Skip button is clicked', () => {
    render(<FeatureIntroScreen {...defaultProps} />);
    
    const skipButton = screen.getByRole('button', { name: 'Skip' });
    fireEvent.click(skipButton);
    
    expect(mockOnSkip).toHaveBeenCalledTimes(1);
  });

  it('disables Previous button on first step', () => {
    render(<FeatureIntroScreen {...defaultProps} currentStep={1} />);
    
    const previousButton = screen.getByRole('button', { name: 'Previous' });
    expect(previousButton).toBeDisabled();
    expect(previousButton).toHaveClass('opacity-50', 'cursor-not-allowed');
  });

  it('shows "Get Started" on last step', () => {
    render(<FeatureIntroScreen {...defaultProps} currentStep={4} />);
    
    expect(screen.getByRole('button', { name: 'Get Started' })).toBeInTheDocument();
    expect(screen.queryByRole('button', { name: 'Next' })).not.toBeInTheDocument();
  });

  it('hides Skip button when canSkip is false', () => {
    render(<FeatureIntroScreen {...defaultProps} canSkip={false} />);
    
    expect(screen.queryByRole('button', { name: 'Skip' })).not.toBeInTheDocument();
  });

  it('handles touch swipe gestures', () => {
    render(<FeatureIntroScreen {...defaultProps} />);
    
    const container = document.querySelector('.min-h-screen');
    
    // Simulate left swipe (should trigger next)
    fireEvent.touchStart(container!, { 
      targetTouches: [{ clientX: 100 }] 
    });
    fireEvent.touchMove(container!, { 
      targetTouches: [{ clientX: 40 }] 
    });
    fireEvent.touchEnd(container!);
    
    expect(mockOnNext).toHaveBeenCalledTimes(1);
  });

  it('handles right swipe gesture for previous', () => {
    render(<FeatureIntroScreen {...defaultProps} />);
    
    const container = document.querySelector('.min-h-screen');
    
    // Simulate right swipe (should trigger previous)
    fireEvent.touchStart(container!, { 
      targetTouches: [{ clientX: 40 }] 
    });
    fireEvent.touchMove(container!, { 
      targetTouches: [{ clientX: 100 }] 
    });
    fireEvent.touchEnd(container!);
    
    expect(mockOnPrevious).toHaveBeenCalledTimes(1);
  });

  it('displays swipe hint text', () => {
    render(<FeatureIntroScreen {...defaultProps} />);
    
    expect(screen.getByText('Swipe left or right to navigate')).toBeInTheDocument();
  });

  it('applies correct animation classes', async () => {
    render(<FeatureIntroScreen {...defaultProps} />);
    
    // Check for animation classes
    const glassCard = document.querySelector('.animate-glass-scale-in');
    expect(glassCard).toBeInTheDocument();
    
    // Wait for visibility animation
    await waitFor(() => {
      const visibleElement = document.querySelector('.opacity-100');
      expect(visibleElement).toBeInTheDocument();
    }, { timeout: 1000 });
  });

  it('renders floating decorative elements', () => {
    render(<FeatureIntroScreen {...defaultProps} />);
    
    const floatingElements = document.querySelectorAll('.absolute.glass-subtle.rounded-full');
    expect(floatingElements.length).toBeGreaterThan(0);
  });

  it('has proper accessibility attributes', () => {
    render(<FeatureIntroScreen {...defaultProps} />);
    
    const nextButton = screen.getByRole('button', { name: 'Next' });
    const previousButton = screen.getByRole('button', { name: 'Previous' });
    const skipButton = screen.getByRole('button', { name: 'Skip' });
    
    expect(nextButton).toBeEnabled();
    expect(previousButton).toBeEnabled();
    expect(skipButton).toBeEnabled();
  });
});