import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { OnboardingFlow } from '../OnboardingFlow';
import { OnboardingProvider } from '@/contexts/OnboardingContext';

// Mock the hooks
const mockUseOnboarding = {
  currentStep: 0,
  totalSteps: 4,
  currentStepData: null,
  nextStep: vi.fn(),
  previousStep: vi.fn(),
  skipStep: vi.fn(),
  completeOnboarding: vi.fn(),
  canGoPrevious: false,
  isFirstStep: true,
  isLastStep: false
};

vi.mock('@/hooks/useOnboarding', () => ({
  useOnboarding: () => mockUseOnboarding
}));

describe('OnboardingFlow', () => {
  const mockOnComplete = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    // Reset mock to default state
    Object.assign(mockUseOnboarding, {
      currentStep: 0,
      totalSteps: 4,
      currentStepData: null,
      nextStep: vi.fn(),
      previousStep: vi.fn(),
      skipStep: vi.fn(),
      completeOnboarding: vi.fn(),
      canGoPrevious: false,
      isFirstStep: true,
      isLastStep: false
    });
  });

  const renderWithProvider = (component: React.ReactElement) => {
    return render(
      <OnboardingProvider>
        {component}
      </OnboardingProvider>
    );
  };

  it('renders welcome screen on first step', () => {
    render(<OnboardingFlow onComplete={mockOnComplete} />);
    
    expect(screen.getByText('Welcome')).toBeInTheDocument();
    expect(screen.getByText('Your Fashion Assistant')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Get Started' })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Skip for now' })).toBeInTheDocument();
  });

  it('renders feature intro screen for subsequent steps', () => {
    // Mock being on step 1 (closet management)
    Object.assign(mockUseOnboarding, {
      currentStep: 1,
      isFirstStep: false,
      currentStepData: {
        id: 'closet-management',
        title: 'Organize Your Closet',
        description: 'Add your clothing items and categorize them for easy browsing and outfit planning.',
        illustration: 'closet-illustration',
        canSkip: true
      }
    });

    render(<OnboardingFlow onComplete={mockOnComplete} />);
    
    expect(screen.getByText('Organize Your Closet')).toBeInTheDocument();
    expect(screen.getByText(/Add your clothing items and categorize them/)).toBeInTheDocument();
  });

  it('handles next step transition from welcome screen', async () => {
    render(<OnboardingFlow onComplete={mockOnComplete} />);
    
    const getStartedButton = screen.getByRole('button', { name: 'Get Started' });
    fireEvent.click(getStartedButton);
    
    // Should trigger transition
    await waitFor(() => {
      expect(mockUseOnboarding.nextStep).toHaveBeenCalledTimes(1);
    });
  });

  it('handles skip from welcome screen', async () => {
    render(<OnboardingFlow onComplete={mockOnComplete} />);
    
    const skipButton = screen.getByRole('button', { name: 'Skip for now' });
    fireEvent.click(skipButton);
    
    await waitFor(() => {
      expect(mockUseOnboarding.completeOnboarding).toHaveBeenCalledTimes(1);
      expect(mockOnComplete).toHaveBeenCalledTimes(1);
    });
  });

  it('handles next step from feature intro screen', async () => {
    // Mock being on a feature intro step
    Object.assign(mockUseOnboarding, {
      currentStep: 1,
      isFirstStep: false,
      isLastStep: false,
      currentStepData: {
        id: 'closet-management',
        title: 'Organize Your Closet',
        description: 'Test description',
        illustration: 'closet-illustration',
        canSkip: true
      }
    });

    render(<OnboardingFlow onComplete={mockOnComplete} />);
    
    const nextButton = screen.getByRole('button', { name: 'Next' });
    fireEvent.click(nextButton);
    
    await waitFor(() => {
      expect(mockUseOnboarding.nextStep).toHaveBeenCalledTimes(1);
    });
  });

  it('handles previous step from feature intro screen', async () => {
    // Mock being on step 2 with ability to go back
    Object.assign(mockUseOnboarding, {
      currentStep: 2,
      isFirstStep: false,
      canGoPrevious: true,
      currentStepData: {
        id: 'outfit-planning',
        title: 'Plan Your Outfits',
        description: 'Test description',
        illustration: 'outfit-illustration',
        canSkip: true
      }
    });

    render(<OnboardingFlow onComplete={mockOnComplete} />);
    
    const previousButton = screen.getByRole('button', { name: 'Previous' });
    fireEvent.click(previousButton);
    
    await waitFor(() => {
      expect(mockUseOnboarding.previousStep).toHaveBeenCalledTimes(1);
    });
  });

  it('completes onboarding on last step', async () => {
    // Mock being on the last step
    Object.assign(mockUseOnboarding, {
      currentStep: 3,
      isFirstStep: false,
      isLastStep: true,
      currentStepData: {
        id: 'style-preferences',
        title: 'Personalize Your Style',
        description: 'Test description',
        illustration: 'style-illustration',
        canSkip: true
      }
    });

    render(<OnboardingFlow onComplete={mockOnComplete} />);
    
    const getStartedButton = screen.getByRole('button', { name: 'Get Started' });
    fireEvent.click(getStartedButton);
    
    await waitFor(() => {
      expect(mockUseOnboarding.completeOnboarding).toHaveBeenCalledTimes(1);
      expect(mockOnComplete).toHaveBeenCalledTimes(1);
    });
  });

  it('handles skip step from feature intro screen', async () => {
    // Mock being on a skippable step
    Object.assign(mockUseOnboarding, {
      currentStep: 1,
      isFirstStep: false,
      currentStepData: {
        id: 'closet-management',
        title: 'Organize Your Closet',
        description: 'Test description',
        illustration: 'closet-illustration',
        canSkip: true
      }
    });

    render(<OnboardingFlow onComplete={mockOnComplete} />);
    
    // Get all skip buttons and click the one in the feature intro screen (not the layout one)
    const skipButtons = screen.getAllByRole('button', { name: 'Skip' });
    const featureSkipButton = skipButtons.find(button => 
      button.className.includes('minimal') || button.className.includes('border-b')
    );
    
    expect(featureSkipButton).toBeDefined();
    fireEvent.click(featureSkipButton!);
    
    await waitFor(() => {
      expect(mockUseOnboarding.skipStep).toHaveBeenCalledTimes(1);
    });
  });

  it('renders correct illustrations for different steps', () => {
    // Test closet management illustration
    Object.assign(mockUseOnboarding, {
      currentStep: 1,
      isFirstStep: false,
      currentStepData: {
        id: 'closet-management',
        title: 'Organize Your Closet',
        description: 'Test description',
        illustration: 'closet-illustration',
        canSkip: true
      }
    });

    render(<OnboardingFlow onComplete={mockOnComplete} />);
    
    // Should render closet SVG icon
    const svgElement = document.querySelector('svg');
    expect(svgElement).toBeInTheDocument();
  });

  it('applies transition classes during navigation', async () => {
    render(<OnboardingFlow onComplete={mockOnComplete} />);
    
    const getStartedButton = screen.getByRole('button', { name: 'Get Started' });
    fireEvent.click(getStartedButton);
    
    // Should apply transition classes
    await waitFor(() => {
      const transitionElement = document.querySelector('.transition-all.duration-500.ease-liquid');
      expect(transitionElement).toBeInTheDocument();
    });
  });

  it('returns null when no current step data and not first step', () => {
    // Mock invalid state
    Object.assign(mockUseOnboarding, {
      currentStep: 1,
      isFirstStep: false,
      currentStepData: null
    });

    const { container } = render(<OnboardingFlow onComplete={mockOnComplete} />);
    expect(container.firstChild).toBeNull();
  });

  it('handles transition timing correctly', async () => {
    render(<OnboardingFlow onComplete={mockOnComplete} />);
    
    const getStartedButton = screen.getByRole('button', { name: 'Get Started' });
    fireEvent.click(getStartedButton);
    
    // Should not immediately call nextStep (due to transition delay)
    expect(mockUseOnboarding.nextStep).not.toHaveBeenCalled();
    
    // Should call after transition delay
    await waitFor(() => {
      expect(mockUseOnboarding.nextStep).toHaveBeenCalledTimes(1);
    }, { timeout: 500 });
  });
});