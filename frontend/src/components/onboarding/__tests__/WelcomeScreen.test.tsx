import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { WelcomeScreen } from '../WelcomeScreen';

describe('WelcomeScreen', () => {
  const mockOnContinue = vi.fn();
  const mockOnSkip = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders welcome screen with correct content', () => {
    render(<WelcomeScreen onContinue={mockOnContinue} onSkip={mockOnSkip} />);
    
    expect(screen.getByText('Welcome')).toBeInTheDocument();
    expect(screen.getByText('Your Fashion Assistant')).toBeInTheDocument();
    expect(screen.getByText(/Discover a new way to organize your wardrobe/)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Get Started' })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Skip for now' })).toBeInTheDocument();
  });

  it('calls onContinue when Get Started button is clicked', () => {
    render(<WelcomeScreen onContinue={mockOnContinue} onSkip={mockOnSkip} />);
    
    const getStartedButton = screen.getByRole('button', { name: 'Get Started' });
    fireEvent.click(getStartedButton);
    
    expect(mockOnContinue).toHaveBeenCalledTimes(1);
  });

  it('calls onSkip when Skip button is clicked', () => {
    render(<WelcomeScreen onContinue={mockOnContinue} onSkip={mockOnSkip} />);
    
    const skipButton = screen.getByRole('button', { name: 'Skip for now' });
    fireEvent.click(skipButton);
    
    expect(mockOnSkip).toHaveBeenCalledTimes(1);
  });

  it('has proper accessibility attributes', () => {
    render(<WelcomeScreen onContinue={mockOnContinue} onSkip={mockOnSkip} />);
    
    const getStartedButton = screen.getByRole('button', { name: 'Get Started' });
    const skipButton = screen.getByRole('button', { name: 'Skip for now' });
    
    expect(getStartedButton).toBeEnabled();
    expect(skipButton).toBeEnabled();
  });

  it('applies animation classes correctly', async () => {
    render(<WelcomeScreen onContinue={mockOnContinue} onSkip={mockOnSkip} />);
    
    // Check for animation classes
    const glassCard = document.querySelector('.animate-glass-scale-in');
    expect(glassCard).toBeInTheDocument();
    
    // Wait for visibility animation to complete
    await waitFor(() => {
      const visibleElement = document.querySelector('.opacity-100');
      expect(visibleElement).toBeInTheDocument();
    }, { timeout: 1000 });
  });

  it('renders decorative elements', () => {
    render(<WelcomeScreen onContinue={mockOnContinue} onSkip={mockOnSkip} />);
    
    // Check for floating glass elements
    const floatingElements = document.querySelectorAll('.glass-subtle.rounded-full');
    expect(floatingElements.length).toBeGreaterThan(0);
  });

  it('has proper button styling and morphing effects', () => {
    render(<WelcomeScreen onContinue={mockOnContinue} onSkip={mockOnSkip} />);
    
    const getStartedButton = screen.getByRole('button', { name: 'Get Started' });
    expect(getStartedButton).toHaveClass('bg-zara-black', 'text-zara-white');
    
    const skipButton = screen.getByRole('button', { name: 'Skip for now' });
    expect(skipButton).toHaveClass('bg-transparent', 'text-zara-dark-gray');
  });
});