import { cn } from '@/lib/utils';

interface ProgressIndicatorProps {
  currentStep: number;
  totalSteps: number;
  variant?: 'dots' | 'bar';
  size?: 'sm' | 'md' | 'lg';
  showNumbers?: boolean;
  className?: string;
}

export const ProgressIndicator = ({ 
  currentStep, 
  totalSteps, 
  variant = 'dots',
  size = 'md',
  showNumbers = false,
  className 
}: ProgressIndicatorProps) => {
  const progress = (currentStep / totalSteps) * 100;

  if (variant === 'bar') {
    const sizes = {
      sm: 'h-1',
      md: 'h-2', 
      lg: 'h-3'
    };

    return (
      <div className={cn('w-full space-y-2', className)}>
        {showNumbers && (
          <div className="flex justify-between items-center">
            <span className="zara-caption text-zara-dark-gray">
              Step {currentStep} of {totalSteps}
            </span>
            <span className="zara-caption text-zara-dark-gray">
              {Math.round(progress)}%
            </span>
          </div>
        )}
        
        <div className={cn(
          'w-full bg-zara-light-gray rounded-full overflow-hidden glass-subtle',
          sizes[size]
        )}>
          <div 
            className={cn(
              'h-full bg-gradient-to-r from-zara-charcoal to-zara-black',
              'transition-all duration-500 ease-premium',
              'relative overflow-hidden'
            )}
            style={{ width: `${progress}%` }}
          >
            {/* Animated shimmer effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse" />
          </div>
        </div>
      </div>
    );
  }

  // Dots variant
  const dotSizes = {
    sm: 'w-2 h-2',
    md: 'w-3 h-3',
    lg: 'w-4 h-4'
  };

  const dotSpacing = {
    sm: 'gap-2',
    md: 'gap-3',
    lg: 'gap-4'
  };

  return (
    <div className={cn('flex items-center justify-center', dotSpacing[size], className)}>
      {Array.from({ length: totalSteps }, (_, index) => {
        const stepNumber = index + 1;
        const isActive = stepNumber === currentStep;
        const isCompleted = stepNumber < currentStep;
        const isUpcoming = stepNumber > currentStep;

        return (
          <div
            key={stepNumber}
            className={cn(
              'rounded-full transition-all duration-300 ease-glass',
              dotSizes[size],
              {
                // Active step - larger with glass effect
                'scale-125 glass-medium border-2 border-zara-charcoal': isActive,
                
                // Completed steps - solid with subtle glow
                'bg-zara-charcoal shadow-lg': isCompleted,
                
                // Upcoming steps - subtle glass
                'glass-subtle border border-zara-medium-gray': isUpcoming
              }
            )}
          >
            {/* Active step indicator */}
            {isActive && (
              <div className="w-full h-full rounded-full bg-zara-charcoal animate-pulse" />
            )}
            
            {/* Completed step checkmark (for larger sizes) */}
            {isCompleted && size === 'lg' && (
              <div className="w-full h-full flex items-center justify-center">
                <svg className="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
            )}
          </div>
        );
      })}
      
      {showNumbers && (
        <span className="ml-4 zara-caption text-zara-dark-gray">
          {currentStep}/{totalSteps}
        </span>
      )}
    </div>
  );
};