import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import { ProgressIndicator } from '../progress-indicator';

describe('ProgressIndicator', () => {
  describe('dots variant', () => {
    it('renders correct number of dots', () => {
      const { container } = render(
        <ProgressIndicator currentStep={2} totalSteps={4} variant="dots" />
      );
      
      // Look for the main step dots, not all rounded elements
      const stepContainer = container.querySelector('div[class*="flex items-center justify-center"]');
      const dots = stepContainer?.children;
      expect(dots).toHaveLength(4);
    });

    it('applies correct styles to active step', () => {
      const { container } = render(
        <ProgressIndicator currentStep={2} totalSteps={4} variant="dots" />
      );
      
      const stepContainer = container.querySelector('div[class*="flex items-center justify-center"]');
      const dots = Array.from(stepContainer?.children || []);
      const activeStep = dots[1]; // currentStep 2 = index 1
      
      expect(activeStep).toHaveClass('scale-125');
      expect(activeStep).toHaveClass('glass-medium');
      expect(activeStep).toHaveClass('border-2');
      expect(activeStep).toHaveClass('border-zara-charcoal');
    });

    it('applies correct styles to completed steps', () => {
      const { container } = render(
        <ProgressIndicator currentStep={3} totalSteps={4} variant="dots" />
      );
      
      const stepContainer = container.querySelector('div[class*="flex items-center justify-center"]');
      const dots = Array.from(stepContainer?.children || []);
      const completedStep = dots[0]; // step 1 is completed
      
      expect(completedStep).toHaveClass('bg-zara-charcoal');
      expect(completedStep).toHaveClass('shadow-lg');
    });

    it('applies correct styles to upcoming steps', () => {
      const { container } = render(
        <ProgressIndicator currentStep={2} totalSteps={4} variant="dots" />
      );
      
      const stepContainer = container.querySelector('div[class*="flex items-center justify-center"]');
      const dots = Array.from(stepContainer?.children || []);
      const upcomingStep = dots[2]; // step 3 is upcoming
      
      expect(upcomingStep).toHaveClass('glass-subtle');
      expect(upcomingStep).toHaveClass('border');
      expect(upcomingStep).toHaveClass('border-zara-medium-gray');
    });

    it('applies different sizes correctly', () => {
      const sizes = ['sm', 'md', 'lg'] as const;
      
      sizes.forEach(size => {
        const { container, unmount } = render(
          <ProgressIndicator currentStep={1} totalSteps={3} variant="dots" size={size} />
        );
        
        const stepContainer = container.querySelector('div[class*="flex items-center justify-center"]');
        const dots = Array.from(stepContainer?.children || []);
        
        switch (size) {
          case 'sm':
            expect(dots[0]).toHaveClass('w-2');
            expect(dots[0]).toHaveClass('h-2');
            break;
          case 'md':
            expect(dots[0]).toHaveClass('w-3');
            expect(dots[0]).toHaveClass('h-3');
            break;
          case 'lg':
            expect(dots[0]).toHaveClass('w-4');
            expect(dots[0]).toHaveClass('h-4');
            break;
        }
        
        unmount();
      });
    });

    it('shows checkmark for completed steps in large size', () => {
      const { container } = render(
        <ProgressIndicator currentStep={3} totalSteps={4} variant="dots" size="lg" />
      );
      
      const checkmarks = container.querySelectorAll('svg');
      expect(checkmarks.length).toBeGreaterThan(0);
    });

    it('shows step numbers when showNumbers is enabled', () => {
      render(
        <ProgressIndicator currentStep={2} totalSteps={4} variant="dots" showNumbers />
      );
      
      expect(screen.getByText('2/4')).toBeInTheDocument();
    });
  });

  describe('bar variant', () => {
    it('renders progress bar correctly', () => {
      const { container } = render(
        <ProgressIndicator currentStep={2} totalSteps={4} variant="bar" />
      );
      
      const progressBar = container.querySelector('div[style*="width: 50%"]');
      expect(progressBar).toBeInTheDocument();
    });

    it('calculates progress percentage correctly', () => {
      const { container } = render(
        <ProgressIndicator currentStep={3} totalSteps={4} variant="bar" />
      );
      
      const progressBar = container.querySelector('div[style*="width: 75%"]');
      expect(progressBar).toBeInTheDocument();
    });

    it('applies different sizes correctly', () => {
      const sizes = ['sm', 'md', 'lg'] as const;
      
      sizes.forEach(size => {
        const { container, unmount } = render(
          <ProgressIndicator currentStep={1} totalSteps={3} variant="bar" size={size} />
        );
        
        const progressContainer = container.querySelector('div[class*="bg-zara-light-gray"]');
        
        switch (size) {
          case 'sm':
            expect(progressContainer).toHaveClass('h-1');
            break;
          case 'md':
            expect(progressContainer).toHaveClass('h-2');
            break;
          case 'lg':
            expect(progressContainer).toHaveClass('h-3');
            break;
        }
        
        unmount();
      });
    });

    it('shows step information when showNumbers is enabled', () => {
      render(
        <ProgressIndicator currentStep={2} totalSteps={4} variant="bar" showNumbers />
      );
      
      expect(screen.getByText('Step 2 of 4')).toBeInTheDocument();
      expect(screen.getByText('50%')).toBeInTheDocument();
    });

    it('has shimmer animation effect', () => {
      const { container } = render(
        <ProgressIndicator currentStep={2} totalSteps={4} variant="bar" />
      );
      
      const shimmer = container.querySelector('.animate-pulse');
      expect(shimmer).toBeInTheDocument();
    });
  });

  it('applies custom className', () => {
    const { container } = render(
      <ProgressIndicator 
        currentStep={1} 
        totalSteps={3} 
        className="custom-class" 
      />
    );
    
    expect(container.firstChild).toHaveClass('custom-class');
  });

  it('handles edge cases correctly', () => {
    // Test with currentStep = 0
    const { container: container1 } = render(
      <ProgressIndicator currentStep={0} totalSteps={3} variant="bar" />
    );
    
    const progressBar1 = container1.querySelector('div[style*="width: 0%"]');
    expect(progressBar1).toBeInTheDocument();

    // Test with currentStep = totalSteps
    const { container: container2 } = render(
      <ProgressIndicator currentStep={3} totalSteps={3} variant="bar" />
    );
    
    const progressBar2 = container2.querySelector('div[style*="width: 100%"]');
    expect(progressBar2).toBeInTheDocument();
  });
});