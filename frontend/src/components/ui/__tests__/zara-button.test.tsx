import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { ZaraButton } from '../zara-button';

describe('ZaraButton', () => {
  it('renders children correctly', () => {
    render(<ZaraButton>Click me</ZaraButton>);
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });

  it('applies default variant and size classes', () => {
    render(<ZaraButton>Default Button</ZaraButton>);
    const button = screen.getByRole('button');
    
    expect(button).toHaveClass('bg-zara-black');
    expect(button).toHaveClass('text-zara-white');
    expect(button).toHaveClass('px-4');
    expect(button).toHaveClass('py-2');
    expect(button).toHaveClass('min-h-[40px]');
  });

  it('applies different variant styles', () => {
    const variants = ['primary', 'secondary', 'ghost', 'minimal', 'cta'] as const;
    
    variants.forEach(variant => {
      const { unmount } = render(<ZaraButton variant={variant}>Button</ZaraButton>);
      const button = screen.getByRole('button');
      
      switch (variant) {
        case 'primary':
          expect(button).toHaveClass('bg-zara-black');
          break;
        case 'secondary':
          expect(button).toHaveClass('bg-zara-white');
          break;
        case 'ghost':
          expect(button).toHaveClass('bg-transparent');
          break;
        case 'minimal':
          expect(button).toHaveClass('border-b');
          expect(button).toHaveClass('rounded-none');
          break;
        case 'cta':
          expect(button).toHaveClass('glass-medium');
          break;
      }
      
      unmount();
    });
  });

  it('applies different size classes', () => {
    const sizes = ['xs', 'sm', 'md', 'lg', 'xl'] as const;
    
    sizes.forEach(size => {
      const { unmount } = render(<ZaraButton size={size}>Button</ZaraButton>);
      const button = screen.getByRole('button');
      
      switch (size) {
        case 'xs':
          expect(button).toHaveClass('min-h-[24px]');
          break;
        case 'sm':
          expect(button).toHaveClass('min-h-[32px]');
          break;
        case 'md':
          expect(button).toHaveClass('min-h-[40px]');
          break;
        case 'lg':
          expect(button).toHaveClass('min-h-[48px]');
          break;
        case 'xl':
          expect(button).toHaveClass('min-h-[56px]');
          break;
      }
      
      unmount();
    });
  });

  it('applies fullWidth class when enabled', () => {
    render(<ZaraButton fullWidth>Full Width Button</ZaraButton>);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('w-full');
  });

  it('shows loading state correctly', () => {
    render(<ZaraButton loading>Loading Button</ZaraButton>);
    const button = screen.getByRole('button');
    
    expect(button).toBeDisabled();
    expect(screen.getByText('Loading...')).toBeInTheDocument();
    expect(button.querySelector('.animate-spin')).toBeInTheDocument();
  });

  it('applies morphOnHover effects', () => {
    render(<ZaraButton morphOnHover>Morph Button</ZaraButton>);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('hover:backdrop-blur-xl');
    expect(button).toHaveClass('hover:scale-105');
  });

  it('handles click events', () => {
    const handleClick = vi.fn();
    render(<ZaraButton onClick={handleClick}>Clickable Button</ZaraButton>);
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('is disabled when disabled prop is true', () => {
    render(<ZaraButton disabled>Disabled Button</ZaraButton>);
    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
    expect(button).toHaveClass('disabled:opacity-50');
  });

  it('is disabled when loading is true', () => {
    render(<ZaraButton loading>Loading Button</ZaraButton>);
    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
  });

  it('applies custom className', () => {
    render(<ZaraButton className="custom-class">Custom Button</ZaraButton>);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('custom-class');
  });

  it('forwards ref correctly', () => {
    const ref = vi.fn();
    render(<ZaraButton ref={ref}>Ref Button</ZaraButton>);
    expect(ref).toHaveBeenCalled();
  });

  it('applies focus styles', () => {
    render(<ZaraButton>Focus Button</ZaraButton>);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('focus:outline-none');
    expect(button).toHaveClass('focus:ring-2');
    expect(button).toHaveClass('focus:ring-zara-charcoal');
  });
});