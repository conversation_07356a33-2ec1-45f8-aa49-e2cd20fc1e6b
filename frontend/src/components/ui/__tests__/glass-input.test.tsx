import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { GlassInput } from '../glass-input';

describe('GlassInput', () => {
  it('renders input correctly', () => {
    render(<GlassInput placeholder="Test input" />);
    const input = screen.getByPlaceholderText('Test input');
    expect(input).toBeInTheDocument();
  });

  it('renders label when provided', () => {
    render(<GlassInput label="Test Label" />);
    expect(screen.getByText('Test Label')).toBeInTheDocument();
  });

  it('applies default variant classes', () => {
    render(<GlassInput data-testid="input" />);
    const input = screen.getByTestId('input');
    
    expect(input).toHaveClass('glass-subtle');
    expect(input).toHaveClass('rounded-lg');
    expect(input).toHaveClass('px-4');
    expect(input).toHaveClass('py-3');
  });

  it('applies different variant styles', () => {
    const variants = ['default', 'search', 'minimal'] as const;
    
    variants.forEach(variant => {
      const { unmount } = render(<GlassInput variant={variant} data-testid={`input-${variant}`} />);
      const input = screen.getByTestId(`input-${variant}`);
      
      switch (variant) {
        case 'default':
          expect(input).toHaveClass('rounded-lg');
          expect(input).toHaveClass('px-4');
          break;
        case 'search':
          expect(input).toHaveClass('rounded-full');
          expect(input).toHaveClass('px-6');
          expect(input).toHaveClass('pl-12');
          break;
        case 'minimal':
          expect(input).toHaveClass('rounded-none');
          expect(input).toHaveClass('border-b-2');
          expect(input).toHaveClass('bg-transparent');
          break;
      }
      
      unmount();
    });
  });

  it('applies different glass intensities', () => {
    const intensities = ['subtle', 'light', 'medium', 'strong'] as const;
    
    intensities.forEach(intensity => {
      const { unmount } = render(<GlassInput glassIntensity={intensity} data-testid={`input-${intensity}`} />);
      const input = screen.getByTestId(`input-${intensity}`);
      expect(input).toHaveClass(`glass-${intensity}`);
      unmount();
    });
  });

  it('shows search icon for search variant', () => {
    const { container } = render(<GlassInput variant="search" />);
    const searchIcon = container.querySelector('svg');
    expect(searchIcon).toBeInTheDocument();
  });

  it('handles focus and blur events', () => {
    const handleFocus = vi.fn();
    const handleBlur = vi.fn();
    
    render(<GlassInput onFocus={handleFocus} onBlur={handleBlur} data-testid="input" />);
    const input = screen.getByTestId('input');
    
    fireEvent.focus(input);
    expect(handleFocus).toHaveBeenCalledTimes(1);
    
    fireEvent.blur(input);
    expect(handleBlur).toHaveBeenCalledTimes(1);
  });

  it('applies focus styles when focused', () => {
    render(<GlassInput data-testid="input" />);
    const input = screen.getByTestId('input');
    
    fireEvent.focus(input);
    expect(input).toHaveClass('glass-light');
    expect(input).toHaveClass('ring-2');
  });

  it('applies morphing effects when morphOnFocus is enabled', () => {
    render(<GlassInput morphOnFocus data-testid="input" />);
    const input = screen.getByTestId('input');
    
    fireEvent.focus(input);
    expect(input).toHaveClass('scale-105');
    expect(input).toHaveClass('backdrop-blur-xl');
  });

  it('shows error message when error prop is provided', () => {
    render(<GlassInput error="This field is required" />);
    expect(screen.getByText('This field is required')).toBeInTheDocument();
  });

  it('applies error styles when error is present', () => {
    render(<GlassInput error="Error message" data-testid="input" />);
    const input = screen.getByTestId('input');
    expect(input).toHaveClass('border-red-500');
  });

  it('applies custom className', () => {
    render(<GlassInput className="custom-class" data-testid="input" />);
    const input = screen.getByTestId('input');
    expect(input).toHaveClass('custom-class');
  });

  it('forwards ref correctly', () => {
    const ref = vi.fn();
    render(<GlassInput ref={ref} />);
    expect(ref).toHaveBeenCalled();
  });

  it('shows shimmer effect when focused with morphOnFocus', () => {
    const { container } = render(<GlassInput morphOnFocus data-testid="input" />);
    const input = screen.getByTestId('input');
    
    fireEvent.focus(input);
    
    const shimmer = container.querySelector('.animate-pulse');
    expect(shimmer).toBeInTheDocument();
  });

  it('applies minimal variant focus styles correctly', () => {
    render(<GlassInput variant="minimal" data-testid="input" />);
    const input = screen.getByTestId('input');
    
    expect(input).toHaveClass('border-zara-medium-gray');
    
    fireEvent.focus(input);
    expect(input).toHaveClass('focus:border-zara-charcoal');
  });
});