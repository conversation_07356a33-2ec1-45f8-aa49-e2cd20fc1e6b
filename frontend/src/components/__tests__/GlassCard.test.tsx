import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { GlassCard } from '../GlassCard';

describe('GlassCard', () => {
  it('renders children correctly', () => {
    render(
      <GlassCard>
        <div>Test content</div>
      </GlassCard>
    );
    
    expect(screen.getByText('Test content')).toBeInTheDocument();
  });

  it('applies default variant classes', () => {
    const { container } = render(
      <GlassCard>
        <div>Test content</div>
      </GlassCard>
    );
    
    const card = container.firstChild as HTMLElement;
    expect(card).toHaveClass('glass-medium');
    expect(card).toHaveClass('rounded-2xl');
    expect(card).toHaveClass('transition-glass');
  });

  it('applies different variant classes', () => {
    const { container } = render(
      <GlassCard variant="hero">
        <div>Test content</div>
      </GlassCard>
    );
    
    const card = container.firstChild as HTMLElement;
    expect(card).toHaveClass('glass-hero');
  });

  it('applies product variant with hover effects', () => {
    const { container } = render(
      <GlassCard variant="product">
        <div>Test content</div>
      </GlassCard>
    );
    
    const card = container.firstChild as HTMLElement;
    expect(card).toHaveClass('glass-product');
  });

  it('applies morphing hover effects when enabled', () => {
    const { container } = render(
      <GlassCard morphOnHover>
        <div>Test content</div>
      </GlassCard>
    );
    
    const card = container.firstChild as HTMLElement;
    expect(card).toHaveClass('glass-morphing');
  });

  it('applies interactive classes when onClick is provided', () => {
    const handleClick = vi.fn();
    const { container } = render(
      <GlassCard onClick={handleClick}>
        <div>Test content</div>
      </GlassCard>
    );
    
    const card = container.firstChild as HTMLElement;
    expect(card).toHaveClass('cursor-pointer');
    expect(card).toHaveClass('glass-interactive');
  });

  it('handles click events', () => {
    const handleClick = vi.fn();
    const { container } = render(
      <GlassCard onClick={handleClick}>
        <div>Test content</div>
      </GlassCard>
    );
    
    const card = container.firstChild as HTMLElement;
    fireEvent.click(card);
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('overrides variant with glassIntensity prop', () => {
    const { container } = render(
      <GlassCard variant="subtle" glassIntensity="strong">
        <div>Test content</div>
      </GlassCard>
    );
    
    const card = container.firstChild as HTMLElement;
    expect(card).toHaveClass('glass-strong');
    expect(card).not.toHaveClass('glass-subtle');
  });

  it('applies custom className', () => {
    const { container } = render(
      <GlassCard className="custom-class">
        <div>Test content</div>
      </GlassCard>
    );
    
    const card = container.firstChild as HTMLElement;
    expect(card).toHaveClass('custom-class');
  });

  it('applies all variant types correctly', () => {
    const variants = ['subtle', 'light', 'medium', 'strong', 'prominent', 'navigation', 'modal', 'hero', 'product'] as const;
    
    variants.forEach(variant => {
      const { container } = render(
        <GlassCard variant={variant}>
          <div>Test content</div>
        </GlassCard>
      );
      
      const card = container.firstChild as HTMLElement;
      expect(card).toHaveClass(`glass-${variant}`);
    });
  });
});