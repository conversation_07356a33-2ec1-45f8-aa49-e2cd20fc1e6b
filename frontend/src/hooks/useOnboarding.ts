import { useOnboardingContext } from '../contexts/OnboardingContext';

/**
 * Custom hook for accessing onboarding state and actions
 * Provides a simplified interface for components that need onboarding functionality
 */
export function useOnboarding() {
  const {
    state,
    steps,
    nextStep,
    previousStep,
    skipStep,
    completeOnboarding,
    resetOnboarding,
    updateUserPreferences,
    updateUserProfile,
    getCurrentStep,
    canGoNext,
    canGoPrevious,
  } = useOnboardingContext();

  return {
    // State
    isCompleted: state.isCompleted,
    currentStep: state.currentStep,
    skippedSteps: state.skippedSteps,
    completedAt: state.completedAt,
    userPreferences: state.userPreferences,
    totalSteps: steps.length,

    // Current step info
    currentStepData: getCurrentStep(),
    progress: ((state.currentStep + 1) / steps.length) * 100,

    // Navigation
    nextStep,
    previousStep,
    skipStep,
    canGoNext: canGoNext(),
    canGoPrevious: canGoPrevious(),

    // Completion
    completeOnboarding,
    resetOnboarding,

    // Preferences
    updateUserPreferences,

    // User profile
    updateUserProfile,

    // Utility
    isFirstStep: state.currentStep === 0,
    isLastStep: state.currentStep === steps.length - 1,
    canSkipCurrentStep: getCurrentStep()?.canSkip ?? false,
  };
}