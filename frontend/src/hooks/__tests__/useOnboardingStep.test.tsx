import { describe, it, expect, beforeEach, vi } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { ReactNode } from 'react';
import { OnboardingProvider } from '../../contexts/OnboardingContext';
import { useOnboardingStep } from '../useOnboardingStep';
import { useOnboarding } from '../useOnboarding';

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Test wrapper component
const TestWrapper = ({ children }: { children: ReactNode }) => (
  <OnboardingProvider>{children}</OnboardingProvider>
);

describe('useOnboardingStep', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  describe('Step data retrieval', () => {
    it('should return current step when no stepId provided', () => {
      const { result } = renderHook(() => useOnboardingStep(), {
        wrapper: TestWrapper,
      });

      expect(result.current.step?.id).toBe('welcome');
      expect(result.current.stepIndex).toBe(0);
      expect(result.current.title).toBe('Welcome to Your Fashion Assistant');
      expect(result.current.description).toBe('Discover a new way to organize your wardrobe and plan your outfits with style.');
      expect(result.current.illustration).toBe('welcome-illustration');
    });

    it('should return specific step when stepId provided', () => {
      const { result } = renderHook(() => useOnboardingStep('closet-management'), {
        wrapper: TestWrapper,
      });

      expect(result.current.step?.id).toBe('closet-management');
      expect(result.current.stepIndex).toBe(1);
      expect(result.current.title).toBe('Organize Your Closet');
      expect(result.current.canSkip).toBe(true);
    });

    it('should handle invalid stepId gracefully', () => {
      const { result } = renderHook(() => useOnboardingStep('invalid-step'), {
        wrapper: TestWrapper,
      });

      expect(result.current.step).toBeUndefined();
      expect(result.current.stepIndex).toBe(-1);
      expect(result.current.title).toBe('');
      expect(result.current.description).toBe('');
      expect(result.current.illustration).toBe('');
    });
  });

  describe('Step status', () => {
    it('should correctly identify current step', () => {
      const { result } = renderHook(() => useOnboardingStep('welcome'), {
        wrapper: TestWrapper,
      });

      expect(result.current.isCurrent).toBe(true);
      expect(result.current.isCompleted).toBe(false);
      expect(result.current.isSkipped).toBe(false);
    });

    it('should correctly identify completed steps', () => {
      const { result } = renderHook(() => {
        const onboarding = useOnboarding();
        const step = useOnboardingStep('welcome');
        return { onboarding, step };
      }, {
        wrapper: TestWrapper,
      });

      // Move to step 2
      act(() => {
        result.current.onboarding.nextStep();
        result.current.onboarding.nextStep();
      });

      expect(result.current.step.isCurrent).toBe(false);
      expect(result.current.step.isCompleted).toBe(true);
      expect(result.current.step.isSkipped).toBe(false);
    });

    it('should correctly identify skipped steps', () => {
      const { result } = renderHook(() => {
        const onboarding = useOnboarding();
        const step = useOnboardingStep('closet-management');
        return { onboarding, step };
      }, {
        wrapper: TestWrapper,
      });

      // Go to step 1 and skip it
      act(() => {
        result.current.onboarding.nextStep(); // Now on closet-management (step 1)
      });

      // Verify we're on the right step before skipping
      expect(result.current.step.isCurrent).toBe(true);
      expect(result.current.onboarding.currentStep).toBe(1);

      act(() => {
        result.current.onboarding.skipStep(); // Skip closet-management, move to step 2
      });

      // After skipping, we should be on step 2, so closet-management is no longer current
      expect(result.current.onboarding.currentStep).toBe(2); // Should be on step 2 now
      expect(result.current.step.isCurrent).toBe(false);
      expect(result.current.step.isCompleted).toBe(true);
      expect(result.current.step.isSkipped).toBe(true);
    });

    it('should identify when onboarding is completed', () => {
      const { result } = renderHook(() => {
        const onboarding = useOnboarding();
        const step = useOnboardingStep('welcome');
        return { onboarding, step };
      }, {
        wrapper: TestWrapper,
      });

      act(() => {
        result.current.onboarding.completeOnboarding();
      });

      expect(result.current.step.isCompleted).toBe(true);
    });
  });

  describe('Step navigation', () => {
    it('should navigate to specific step', () => {
      const { result } = renderHook(() => {
        const onboarding = useOnboarding();
        const step = useOnboardingStep('outfit-planning');
        return { onboarding, step };
      }, {
        wrapper: TestWrapper,
      });

      expect(result.current.onboarding.currentStep).toBe(0);

      act(() => {
        result.current.step.goToStep();
      });

      expect(result.current.onboarding.currentStep).toBe(2);
      expect(result.current.onboarding.currentStepData?.id).toBe('outfit-planning');
    });

    it('should not navigate to invalid step', () => {
      const { result } = renderHook(() => {
        const onboarding = useOnboarding();
        const step = useOnboardingStep('invalid-step');
        return { onboarding, step };
      }, {
        wrapper: TestWrapper,
      });

      const initialStep = result.current.onboarding.currentStep;

      act(() => {
        result.current.step.goToStep();
      });

      expect(result.current.onboarding.currentStep).toBe(initialStep);
    });
  });

  describe('Step actions', () => {
    it('should execute step action when available', async () => {
      const mockAction = vi.fn().mockResolvedValue(undefined);
      
      // We need to test this by mocking the step configuration
      // Since we can't easily modify the steps in the context, we'll test the error handling
      const { result } = renderHook(() => useOnboardingStep('welcome'), {
        wrapper: TestWrapper,
      });

      // The welcome step doesn't have an action, so this should not throw
      await act(async () => {
        await result.current.executeStepAction();
      });

      // No error should be thrown
      expect(true).toBe(true);
    });

    it('should handle step action errors', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      // Create a hook that would have a failing action
      // Since we can't easily modify the context steps, we'll simulate this scenario
      const { result } = renderHook(() => useOnboardingStep('welcome'), {
        wrapper: TestWrapper,
      });

      // Manually set an action that will fail
      if (result.current.step) {
        result.current.step.action = vi.fn().mockRejectedValue(new Error('Action failed'));
        
        await act(async () => {
          try {
            await result.current.executeStepAction();
          } catch (error) {
            expect(error).toBeInstanceOf(Error);
          }
        });
      }

      consoleSpy.mockRestore();
    });
  });

  describe('Step properties', () => {
    it('should provide correct canSkip property', () => {
      const { result: welcomeResult } = renderHook(() => useOnboardingStep('welcome'), {
        wrapper: TestWrapper,
      });

      const { result: closetResult } = renderHook(() => useOnboardingStep('closet-management'), {
        wrapper: TestWrapper,
      });

      expect(welcomeResult.current.canSkip).toBe(false);
      expect(closetResult.current.canSkip).toBe(true);
    });

    it('should handle missing step properties gracefully', () => {
      const { result } = renderHook(() => useOnboardingStep('invalid-step'), {
        wrapper: TestWrapper,
      });

      expect(result.current.canSkip).toBe(false);
      expect(result.current.title).toBe('');
      expect(result.current.description).toBe('');
      expect(result.current.illustration).toBe('');
    });
  });

  describe('Dynamic step updates', () => {
    it('should update when onboarding state changes', () => {
      const { result } = renderHook(() => {
        const onboarding = useOnboarding();
        const step = useOnboardingStep();
        return { onboarding, step };
      }, {
        wrapper: TestWrapper,
      });

      expect(result.current.step.step?.id).toBe('welcome');
      expect(result.current.step.isCurrent).toBe(true);

      act(() => {
        result.current.onboarding.nextStep();
      });

      expect(result.current.step.step?.id).toBe('closet-management');
      expect(result.current.step.isCurrent).toBe(true);
    });

    it('should maintain step reference when using specific stepId', () => {
      const { result } = renderHook(() => {
        const onboarding = useOnboarding();
        const step = useOnboardingStep('outfit-planning');
        return { onboarding, step };
      }, {
        wrapper: TestWrapper,
      });

      expect(result.current.step.step?.id).toBe('outfit-planning');
      expect(result.current.step.isCurrent).toBe(false);

      // Move to the outfit-planning step
      act(() => {
        result.current.onboarding.nextStep();
        result.current.onboarding.nextStep();
      });

      expect(result.current.step.step?.id).toBe('outfit-planning');
      expect(result.current.step.isCurrent).toBe(true);
    });
  });

  describe('Edge cases', () => {
    it('should handle step boundaries correctly', () => {
      const { result: firstStep } = renderHook(() => useOnboardingStep('welcome'), {
        wrapper: TestWrapper,
      });

      const { result: lastStep } = renderHook(() => useOnboardingStep('style-preferences'), {
        wrapper: TestWrapper,
      });

      expect(firstStep.current.stepIndex).toBe(0);
      expect(lastStep.current.stepIndex).toBe(3);
    });

    it('should work with all defined steps', () => {
      const stepIds = ['welcome', 'closet-management', 'outfit-planning', 'style-preferences'];
      
      stepIds.forEach((stepId, index) => {
        const { result } = renderHook(() => useOnboardingStep(stepId), {
          wrapper: TestWrapper,
        });

        expect(result.current.step?.id).toBe(stepId);
        expect(result.current.stepIndex).toBe(index);
        expect(result.current.title).toBeTruthy();
        expect(result.current.description).toBeTruthy();
      });
    });
  });
});