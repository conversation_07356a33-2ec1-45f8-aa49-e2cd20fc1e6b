import { useOnboardingContext, OnboardingStep } from '../contexts/OnboardingContext';

/**
 * Custom hook for accessing specific onboarding step functionality
 * Useful for components that need to work with individual steps
 */
export function useOnboardingStep(stepId?: string) {
  const { state, steps, dispatch } = useOnboardingContext();

  // Find step by ID or use current step
  const getStep = (id?: string): OnboardingStep | undefined => {
    if (id) {
      return steps.find(step => step.id === id);
    }
    return steps[state.currentStep];
  };

  const step = getStep(stepId);
  const stepIndex = stepId ? steps.findIndex(s => s.id === stepId) : state.currentStep;

  // Check if this step has been skipped
  const isSkipped = stepId ? state.skippedSteps.includes(stepId) : false;

  // Check if this step is the current step
  const isCurrent = stepIndex === state.currentStep;

  // Check if this step has been completed (user has moved past it)
  const isCompleted = stepIndex < state.currentStep || state.isCompleted;

  // Navigate to this specific step
  const goToStep = () => {
    if (stepIndex >= 0 && stepIndex < steps.length) {
      dispatch({ type: 'SET_CURRENT_STEP', payload: stepIndex });
    }
  };

  // Execute step action if it exists
  const executeStepAction = async () => {
    if (step?.action) {
      try {
        await step.action();
      } catch (error) {
        console.error(`Failed to execute action for step ${step.id}:`, error);
        throw error;
      }
    }
  };

  return {
    // Step data
    step,
    stepIndex,
    
    // Step status
    isSkipped,
    isCurrent,
    isCompleted,
    canSkip: step?.canSkip ?? false,
    
    // Step navigation
    goToStep,
    executeStepAction,
    
    // Step info
    title: step?.title ?? '',
    description: step?.description ?? '',
    illustration: step?.illustration ?? '',
  };
}