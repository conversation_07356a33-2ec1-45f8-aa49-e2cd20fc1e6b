#!/usr/bin/env node

/**
 * Test script to verify sign up and login functionality
 * This script uses Puppeteer to automate browser interactions
 */

const puppeteer = require('puppeteer');

async function testAuthFlow() {
  let browser;
  
  try {
    console.log('🚀 Starting authentication flow test...');
    
    // Launch browser
    browser = await puppeteer.launch({ 
      headless: false, // Set to true for headless mode
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Navigate to the application
    console.log('📱 Navigating to application...');
    await page.goto('http://localhost:5173', { waitUntil: 'networkidle0' });
    
    // Test Sign Up Flow
    console.log('✍️  Testing sign up flow...');
    
    // Look for sign up button
    await page.waitForSelector('button', { timeout: 5000 });
    
    // Take a screenshot to see the current state
    await page.screenshot({ path: 'landing-page.png' });
    console.log('📸 Screenshot saved: landing-page.png');
    
    // Try to find and click sign up button
    const signUpButton = await page.$('button:contains("Sign Up"), button:contains("Get Started")');
    if (signUpButton) {
      await signUpButton.click();
      console.log('✅ Clicked sign up button');
    } else {
      // Try alternative selectors
      const buttons = await page.$$('button');
      console.log(`Found ${buttons.length} buttons on the page`);
      
      for (let i = 0; i < buttons.length; i++) {
        const buttonText = await page.evaluate(el => el.textContent, buttons[i]);
        console.log(`Button ${i}: "${buttonText}"`);
        
        if (buttonText.toLowerCase().includes('sign up') || 
            buttonText.toLowerCase().includes('get started')) {
          await buttons[i].click();
          console.log(`✅ Clicked button: "${buttonText}"`);
          break;
        }
      }
    }
    
    // Wait for navigation or form to appear
    await page.waitForTimeout(2000);
    await page.screenshot({ path: 'after-signup-click.png' });
    console.log('📸 Screenshot saved: after-signup-click.png');
    
    // Fill out sign up form if it exists
    const emailInput = await page.$('input[type="email"], input[name="email"]');
    if (emailInput) {
      console.log('📝 Filling out sign up form...');
      
      await emailInput.type('<EMAIL>');
      
      const passwordInput = await page.$('input[type="password"], input[name="password"]');
      if (passwordInput) {
        await passwordInput.type('TestPassword123!');
      }
      
      // Fill other required fields if they exist
      const firstNameInput = await page.$('input[name="firstName"], input[placeholder*="first name" i]');
      if (firstNameInput) {
        await firstNameInput.type('Test');
      }
      
      const lastNameInput = await page.$('input[name="lastName"], input[placeholder*="last name" i]');
      if (lastNameInput) {
        await lastNameInput.type('User');
      }
      
      // Submit the form
      const submitButton = await page.$('button[type="submit"], button:contains("Sign Up"), button:contains("Create Account")');
      if (submitButton) {
        await submitButton.click();
        console.log('✅ Submitted sign up form');
        
        // Wait for response
        await page.waitForTimeout(3000);
        await page.screenshot({ path: 'after-signup-submit.png' });
        console.log('📸 Screenshot saved: after-signup-submit.png');
      }
    }
    
    // Test Login Flow
    console.log('🔐 Testing login flow...');
    
    // Navigate back to login if needed
    const loginButton = await page.$('button:contains("Sign In"), button:contains("Login"), a:contains("Sign In")');
    if (loginButton) {
      await loginButton.click();
      await page.waitForTimeout(2000);
    }
    
    // Fill login form
    const loginEmailInput = await page.$('input[type="email"], input[name="email"]');
    if (loginEmailInput) {
      await loginEmailInput.clear();
      await loginEmailInput.type('<EMAIL>');
      
      const loginPasswordInput = await page.$('input[type="password"], input[name="password"]');
      if (loginPasswordInput) {
        await loginPasswordInput.clear();
        await loginPasswordInput.type('TestPassword123!');
        
        const loginSubmitButton = await page.$('button[type="submit"], button:contains("Sign In"), button:contains("Login")');
        if (loginSubmitButton) {
          await loginSubmitButton.click();
          console.log('✅ Submitted login form');
          
          // Wait for response
          await page.waitForTimeout(3000);
          await page.screenshot({ path: 'after-login.png' });
          console.log('📸 Screenshot saved: after-login.png');
        }
      }
    }
    
    // Check if we're logged in by looking for user-specific content
    const userContent = await page.$('[data-testid="user-profile"], .user-menu, .dashboard');
    if (userContent) {
      console.log('🎉 Login successful - user content found!');
    } else {
      console.log('⚠️  Login status unclear - no obvious user content found');
    }
    
    console.log('✅ Authentication flow test completed');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (browser) {
      await browser.close();
    }
    process.exit(1);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Check if puppeteer is available
try {
  require.resolve('puppeteer');
  testAuthFlow();
} catch (error) {
  console.log('❌ Puppeteer not found. Installing...');
  console.log('Please run: npm install puppeteer');
  console.log('Then run this script again.');
  
  // Alternative: Use curl to test API endpoints
  console.log('\n🔄 Falling back to API testing...');
  testWithCurl();
}

async function testWithCurl() {
  const { exec } = require('child_process');
  const util = require('util');
  const execAsync = util.promisify(exec);
  
  try {
    console.log('🌐 Testing frontend accessibility...');
    const { stdout } = await execAsync('curl -s -o /dev/null -w "%{http_code}" http://localhost:5173');
    
    if (stdout.trim() === '200') {
      console.log('✅ Frontend is accessible at http://localhost:5173');
      console.log('📝 Manual testing instructions:');
      console.log('1. Open http://localhost:5173 in your browser');
      console.log('2. Look for Sign Up button and click it');
      console.log('3. Fill out the form with:');
      console.log('   - Email: <EMAIL>');
      console.log('   - Password: TestPassword123!');
      console.log('   - First Name: Test');
      console.log('   - Last Name: User');
      console.log('4. Submit the form');
      console.log('5. Try logging in with the same credentials');
    } else {
      console.log('❌ Frontend not accessible');
    }
    
  } catch (error) {
    console.error('❌ Curl test failed:', error.message);
  }
}