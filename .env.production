# Production Environment Configuration
# This file is used for production deployment

# Application Configuration
VITE_APP_NAME=Closet Glass Chic
NODE_ENV=production

# Network Configuration - Production
# Replace with your actual production domain or IP
VITE_BASE_URL=yourdomain.com

# Port Configuration - Production (typically 80/443 for web, but can be custom)
VITE_FRONTEND_PORT=80
VITE_BACKEND_PORT=3001

# Non-VITE versions for Docker Compose
BASE_URL=yourdomain.com
FRONTEND_PORT=80
BACKEND_PORT=3001

# Feature Flags - Production
VITE_ENABLE_DEBUG=false
VITE_ENABLE_ANALYTICS=true

# External API Services (use your production keys)
VITE_OPENWEATHER_API_KEY=your_production_openweather_api_key
VITE_OPENWEATHER_API_URL=https://api.openweathermap.org/data/2.5
VITE_NOMINATIM_API_URL=https://nominatim.openstreetmap.org

# Security and Performance
VITE_API_TIMEOUT=15000
VITE_API_RATE_LIMIT_REQUESTS=100
VITE_API_RATE_LIMIT_WINDOW=60000
