{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "strict": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "declaration": true, "outDir": "./dist", "rootDir": "./src", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts"]}