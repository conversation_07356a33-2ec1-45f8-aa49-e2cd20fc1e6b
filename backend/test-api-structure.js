#!/usr/bin/env node

/**
 * Test script to verify API structure and integration logic
 * This tests the code structure without requiring external APIs to be running
 */

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function testAPIStructure() {
  console.log('🔧 Testing API Structure and Integration Logic');
  console.log('===============================================');

  // Test 1: Check if service files exist and can be imported
  console.log('📦 Testing service imports...');
  
  try {
    const { ColorExtractionService } = await import('./dist/services/colorExtractionApi.js');
    const { ClothDetectionService } = await import('./dist/services/clothDetectionApi.js');
    
    console.log('   ✅ ColorExtractionService imported successfully');
    console.log('   ✅ ClothDetectionService imported successfully');

    // Test 2: Check service initialization
    console.log('\n🚀 Testing service initialization...');
    
    ColorExtractionService.initialize();
    ClothDetectionService.initialize();
    
    console.log('   ✅ Services initialized without errors');

    // Test 3: Check if the external analysis function exists in clothing route
    console.log('\n🔄 Testing clothing route integration...');
    
    const clothingRouteContent = await fs.readFile(
      path.join(__dirname, 'dist', 'routes', 'clothing.js'), 
      'utf-8'
    );
    
    const hasExternalAnalysis = clothingRouteContent.includes('runExternalAnalysis');
    const hasColorExtraction = clothingRouteContent.includes('ColorExtractionService');
    const hasClothDetection = clothingRouteContent.includes('ClothDetectionService');
    
    console.log(`   ${hasExternalAnalysis ? '✅' : '❌'} runExternalAnalysis function found`);
    console.log(`   ${hasColorExtraction ? '✅' : '❌'} ColorExtractionService integration found`);
    console.log(`   ${hasClothDetection ? '✅' : '❌'} ClothDetectionService integration found`);

    // Test 4: Check environment variables
    console.log('\n🌍 Testing environment configuration...');
    
    const envContent = await fs.readFile(path.join(__dirname, '..', '.env'), 'utf-8');
    
    const hasColorHost = envContent.includes('COLOR_EXTRACTION_API_HOST');
    const hasColorPort = envContent.includes('COLOR_EXTRACTION_API_PORT');
    const hasClothHost = envContent.includes('CLOTH_DETECTION_API_HOST');
    const hasClothPort = envContent.includes('CLOTH_DETECTION_API_PORT');
    
    console.log(`   ${hasColorHost ? '✅' : '❌'} COLOR_EXTRACTION_API_HOST configured`);
    console.log(`   ${hasColorPort ? '✅' : '❌'} COLOR_EXTRACTION_API_PORT configured`);
    console.log(`   ${hasClothHost ? '✅' : '❌'} CLOTH_DETECTION_API_HOST configured`);
    console.log(`   ${hasClothPort ? '✅' : '❌'} CLOTH_DETECTION_API_PORT configured`);

    // Test 5: Check frontend interface updates
    console.log('\n🎨 Testing frontend interface updates...');
    
    const frontendApiContent = await fs.readFile(
      path.join(__dirname, '..', 'src', 'services', 'clothingApi.ts'), 
      'utf-8'
    );
    
    const hasColorDetails = frontendApiContent.includes('ColorDetails');
    const hasClothingDetection = frontendApiContent.includes('ClothingDetection');
    const hasExtendedResponse = frontendApiContent.includes('colorDetails?:');
    
    console.log(`   ${hasColorDetails ? '✅' : '❌'} ColorDetails interface found`);
    console.log(`   ${hasClothingDetection ? '✅' : '❌'} ClothingDetection interface found`);
    console.log(`   ${hasExtendedResponse ? '✅' : '❌'} Extended response interface found`);

    // Test 6: Simulate the analysis workflow
    console.log('\n🔄 Testing analysis workflow simulation...');
    
    // Mock external API responses
    const mockColorResponse = {
      color_name: "navy blue",
      hex: "#1e3a8a",
      rgb: { r: 30, g: 58, b: 138 }
    };
    
    const mockClothResponse = {
      detections: [
        { category: "shirt, blouse", score: 0.864 },
        { category: "jacket", score: 0.685 }
      ],
      processing_time_sec: 1.433
    };
    
    // Simulate the analysis result structure
    const category = ClothDetectionService.getBestCategory(mockClothResponse.detections);
    
    const analysisResult = {
      success: true,
      analysis: {
        category: category,
        color: mockColorResponse.color_name
      },
      colorDetails: {
        hex: mockColorResponse.hex,
        rgb: mockColorResponse.rgb
      },
      detections: mockClothResponse.detections,
      processingTime: mockClothResponse.processing_time_sec
    };
    
    console.log('   ✅ Analysis workflow structure verified');
    console.log('   📊 Mock analysis result:');
    console.log(`      Category: ${analysisResult.analysis.category}`);
    console.log(`      Color: ${analysisResult.analysis.color}`);
    console.log(`      Hex: ${analysisResult.colorDetails.hex}`);
    console.log(`      Detections: ${analysisResult.detections.length} found`);
    console.log(`      Processing time: ${analysisResult.processingTime}s`);

    console.log('\n✅ All structure tests passed!');
    console.log('\n📋 Integration Summary:');
    console.log('   ✅ External API services created and configured');
    console.log('   ✅ Backend route updated to use external APIs');
    console.log('   ✅ Frontend interfaces extended for new data');
    console.log('   ✅ Environment variables configured');
    console.log('   ✅ Analysis workflow structure verified');
    
    console.log('\n🚀 Ready for testing with live external APIs!');
    console.log('   To test with live APIs, ensure the following services are running:');
    console.log('   - Color Extraction API: http://192.168.29.105:7563/api/color');
    console.log('   - Cloth Detection API: http://192.168.29.105:9743/api/analyze');

  } catch (error) {
    console.error('❌ Structure test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run the test
testAPIStructure().catch(console.error);
