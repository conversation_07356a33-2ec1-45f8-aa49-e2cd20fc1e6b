# DEPRECATED: Python Clothing Analysis Setup

⚠️ **This directory is deprecated and no longer used by the application.**

The application now uses external APIs for clothing image analysis:
- **Color Extraction API**: http://**************:7563/api/color
- **Cloth Detection API**: http://**************:9743/api/analyze

This directory contains the legacy Python scripts that were previously used for local image analysis, including background removal and automated clothing categorization. These files are kept for reference but are not executed by the application.

## Prerequisites

- Python 3.8 or higher
- pip package manager

## Installation

1. Create a virtual environment (recommended):
```bash
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

2. Install required packages:
```bash
pip install -r requirements.txt
```

## Usage

The clothing analyzer can be run directly from the command line:

```bash
python clothing_analyzer.py <input_image_path> <output_directory>
```

### Example:
```bash
python clothing_analyzer.py /path/to/shirt.jpg /path/to/output/
```

### Output:
The script outputs <PERSON><PERSON><PERSON> with the analysis results:
```json
{
  "success": true,
  "processed_image_path": "/path/to/output/shirt_processed.png",
  "analysis": {
    "category": "T-Shirts",
    "color": "Blue"
  }
}
```

## Features

1. **Background Removal**: Uses the `rembg` library to automatically remove backgrounds from clothing images
2. **Clothing Type Detection**: Basic computer vision techniques to detect clothing categories
3. **Color Analysis**: Extracts dominant colors using K-means clustering
4. **Error Handling**: Comprehensive error handling with JSON output

## Supported Clothing Categories

- T-Shirts
- Blouses  
- Sweaters
- Jeans
- Trousers
- Skirts
- Dresses
- Jackets
- Coats
- Sneakers
- Heels
- Boots
- Accessories

## Notes

- The clothing detection uses simplified heuristics. For production use, consider training custom ML models
- Color detection works best with well-lit, high-contrast images
- Background removal works best with clear subject-background separation

## Troubleshooting

1. **Import errors**: Make sure all dependencies are installed in your virtual environment
2. **Memory issues**: Large images may require more RAM. Consider resizing images before processing
3. **Poor detection**: Ensure images are well-lit and the clothing item is clearly visible
