#!/usr/bin/env python3
"""
DEPRECATED: Clothing Analysis Script
This script is no longer used. The application now uses external APIs for:
- Color extraction: http://**************:7563/api/color
- Cloth detection: http://**************:9743/api/analyze

This file is kept for reference but is not executed by the application.
"""

import sys
import json
import os
from pathlib import Path
import cv2
import numpy as np
from PIL import Image
from rembg import remove
import webcolors
from sklearn.cluster import KMeans

def remove_background(input_path: str, output_path: str) -> bool:
    """
    Remove background from clothing image using rembg.
    
    Args:
        input_path: Path to input image
        output_path: Path to save processed image
        
    Returns:
        bool: Success status
    """
    try:
        with open(input_path, 'rb') as input_file:
            input_data = input_file.read()
        
        # Remove background
        output_data = remove(input_data)
        
        # Save processed image
        with open(output_path, 'wb') as output_file:
            output_file.write(output_data)
            
        return True
    except Exception as e:
        print(f"Error removing background: {e}", file=sys.stderr)
        return False

def detect_clothing_type(image_path: str) -> str:
    """
    Detect clothing type using basic computer vision techniques.
    
    Args:
        image_path: Path to the image
        
    Returns:
        str: Detected clothing category
    """
    try:
        # Load image
        image = cv2.imread(image_path)
        if image is None:
            return "Unknown"
            
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Get image dimensions
        height, width = gray.shape
        aspect_ratio = height / width
        
        # Basic heuristics for clothing type detection
        # These are simplified rules - in production, you'd use ML models
        
        if aspect_ratio > 1.5:
            # Tall and narrow - likely pants, jeans, or dress
            if height > width * 2:
                return "Jeans"  # Very tall
            else:
                return "Dresses"  # Moderately tall
        elif aspect_ratio < 0.8:
            # Wide - likely top or outerwear
            return "T-Shirts"
        else:
            # Square-ish - could be various items
            # Use contour analysis for better detection
            contours, _ = cv2.findContours(gray, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if contours:
                largest_contour = max(contours, key=cv2.contourArea)
                x, y, w, h = cv2.boundingRect(largest_contour)
                
                # Analyze the shape
                if h > w * 1.2:
                    return "Trousers"
                else:
                    return "Sweaters"
            
        return "T-Shirts"  # Default fallback
        
    except Exception as e:
        print(f"Error detecting clothing type: {e}", file=sys.stderr)
        return "Unknown"

def get_dominant_color(image_path: str) -> str:
    """
    Extract the dominant color from the clothing item.
    
    Args:
        image_path: Path to the image
        
    Returns:
        str: Dominant color name
    """
    try:
        # Load image
        image = cv2.imread(image_path)
        if image is None:
            return "Unknown"
            
        # Convert BGR to RGB
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Reshape image to be a list of pixels
        pixels = image_rgb.reshape(-1, 3)
        
        # Remove black pixels (likely background)
        non_black_pixels = pixels[np.sum(pixels, axis=1) > 30]
        
        if len(non_black_pixels) == 0:
            return "Black"
            
        # Use KMeans to find dominant colors
        kmeans = KMeans(n_clusters=3, random_state=42, n_init=10)
        kmeans.fit(non_black_pixels)
        
        # Get the most dominant color (largest cluster)
        labels = kmeans.labels_
        label_counts = np.bincount(labels)
        dominant_color_index = np.argmax(label_counts)
        dominant_color_rgb = kmeans.cluster_centers_[dominant_color_index]
        
        # Convert RGB to color name
        try:
            closest_name = webcolors.rgb_to_name(tuple(map(int, dominant_color_rgb)))
            return closest_name.title()
        except ValueError:
            # If exact match not found, find closest color
            min_colors = {}
            for key, name in webcolors.CSS3_HEX_TO_NAMES.items():
                r_c, g_c, b_c = webcolors.hex_to_rgb(key)
                rd = (r_c - dominant_color_rgb[0]) ** 2
                gd = (g_c - dominant_color_rgb[1]) ** 2
                bd = (b_c - dominant_color_rgb[2]) ** 2
                min_colors[(rd + gd + bd)] = name
            
            closest_color = min_colors[min(min_colors.keys())]
            return closest_color.title()
            
    except Exception as e:
        print(f"Error detecting color: {e}", file=sys.stderr)
        return "Unknown"

def analyze_clothing(input_path: str, output_dir: str) -> dict:
    """
    Complete clothing analysis pipeline.
    
    Args:
        input_path: Path to input image
        output_dir: Directory to save processed image
        
    Returns:
        dict: Analysis results
    """
    try:
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Generate output filename
        input_filename = Path(input_path).stem
        output_path = os.path.join(output_dir, f"{input_filename}_processed.png")
        
        # Remove background
        background_removed = remove_background(input_path, output_path)
        
        if not background_removed:
            return {
                "success": False,
                "error": "Failed to remove background"
            }
        
        # Analyze the processed image
        clothing_type = detect_clothing_type(output_path)
        dominant_color = get_dominant_color(output_path)
        
        return {
            "success": True,
            "processed_image_path": output_path,
            "analysis": {
                "category": clothing_type,
                "color": dominant_color
            }
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

def main():
    """Main function to handle command line arguments."""
    if len(sys.argv) != 3:
        print("Usage: python clothing_analyzer.py <input_image_path> <output_directory>", file=sys.stderr)
        sys.exit(1)
    
    input_path = sys.argv[1]
    output_dir = sys.argv[2]
    
    # Validate input file exists
    if not os.path.exists(input_path):
        result = {
            "success": False,
            "error": f"Input file does not exist: {input_path}"
        }
        print(json.dumps(result))
        sys.exit(1)
    
    # Perform analysis
    result = analyze_clothing(input_path, output_dir)
    
    # Output result as JSON
    print(json.dumps(result))
    
    # Exit with appropriate code
    sys.exit(0 if result["success"] else 1)

if __name__ == "__main__":
    main()
