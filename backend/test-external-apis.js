#!/usr/bin/env node

/**
 * Test script for external API integration
 * This script tests the color extraction and cloth detection APIs
 * without requiring database connection
 */

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { ColorExtractionService } from './dist/services/colorExtractionApi.js';
import { ClothDetectionService } from './dist/services/clothDetectionApi.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function testExternalAPIs() {
  console.log('🧪 Testing External API Integration');
  console.log('=====================================');

  // Initialize services
  console.log('📡 Initializing services...');
  ColorExtractionService.initialize();
  ClothDetectionService.initialize();

  // Check if we have a test image
  const testImagePath = path.join(__dirname, 'uploads', 'clothing');
  
  try {
    const files = await fs.readdir(testImagePath);
    const imageFiles = files.filter(file => 
      file.toLowerCase().match(/\.(jpg|jpeg|png|webp)$/i)
    );

    if (imageFiles.length === 0) {
      console.log('❌ No test images found in uploads/clothing directory');
      console.log('   Please upload an image through the frontend first, or place a test image in the uploads/clothing directory');
      return;
    }

    const testImage = imageFiles[0];
    const imagePath = path.join(testImagePath, testImage);
    
    console.log(`🖼️  Using test image: ${testImage}`);
    
    // Read the image file
    const imageBuffer = await fs.readFile(imagePath);
    console.log(`📁 Image size: ${imageBuffer.length} bytes`);

    // Test health checks first
    console.log('\n🏥 Testing API health checks...');
    
    const colorHealthy = await ColorExtractionService.healthCheck();
    const clothHealthy = await ClothDetectionService.healthCheck();
    
    console.log(`   Color Extraction API: ${colorHealthy ? '✅ Healthy' : '❌ Unhealthy'}`);
    console.log(`   Cloth Detection API: ${clothHealthy ? '✅ Healthy' : '❌ Unhealthy'}`);

    if (!colorHealthy && !clothHealthy) {
      console.log('\n❌ Both APIs are unhealthy. Please check if the external services are running:');
      console.log('   - Color Extraction API: http://192.168.29.105:7563');
      console.log('   - Cloth Detection API: http://192.168.29.105:9743');
      return;
    }

    // Test color extraction
    if (colorHealthy) {
      console.log('\n🎨 Testing Color Extraction API...');
      try {
        const colorResult = await ColorExtractionService.extractColor(imageBuffer, testImage);
        console.log('   ✅ Color extraction successful:');
        console.log(`      Color: ${colorResult.color_name}`);
        console.log(`      Hex: ${colorResult.hex}`);
        console.log(`      RGB: r=${colorResult.rgb.r}, g=${colorResult.rgb.g}, b=${colorResult.rgb.b}`);
      } catch (error) {
        console.log(`   ❌ Color extraction failed: ${error.message}`);
      }
    }

    // Test cloth detection
    if (clothHealthy) {
      console.log('\n👕 Testing Cloth Detection API...');
      try {
        const clothResult = await ClothDetectionService.detectClothing(imageBuffer, testImage);
        console.log('   ✅ Cloth detection successful:');
        console.log(`      Processing time: ${clothResult.processing_time_sec}s`);
        console.log(`      Detections (${clothResult.detections.length}):`);
        
        clothResult.detections.forEach((detection, index) => {
          console.log(`        ${index + 1}. ${detection.category} (${(detection.score * 100).toFixed(1)}%)`);
        });

        const bestCategory = ClothDetectionService.getBestCategory(clothResult.detections);
        console.log(`      Best category: ${bestCategory}`);
      } catch (error) {
        console.log(`   ❌ Cloth detection failed: ${error.message}`);
      }
    }

    // Test combined analysis (simulating the actual endpoint)
    if (colorHealthy && clothHealthy) {
      console.log('\n🔄 Testing Combined Analysis...');
      try {
        const [colorResult, clothResult] = await Promise.all([
          ColorExtractionService.extractColor(imageBuffer, testImage),
          ClothDetectionService.detectClothing(imageBuffer, testImage)
        ]);

        const category = ClothDetectionService.getBestCategory(clothResult.detections);
        
        const analysisResult = {
          success: true,
          analysis: {
            category: category,
            color: colorResult.color_name
          },
          colorDetails: {
            hex: colorResult.hex,
            rgb: colorResult.rgb
          },
          detections: clothResult.detections,
          processingTime: clothResult.processing_time_sec
        };

        console.log('   ✅ Combined analysis successful:');
        console.log('   📊 Final Result:');
        console.log(`      Category: ${analysisResult.analysis.category}`);
        console.log(`      Color: ${analysisResult.analysis.color}`);
        console.log(`      Hex: ${analysisResult.colorDetails.hex}`);
        console.log(`      Processing time: ${analysisResult.processingTime}s`);
        
      } catch (error) {
        console.log(`   ❌ Combined analysis failed: ${error.message}`);
      }
    }

    console.log('\n✅ External API integration test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testExternalAPIs().catch(console.error);
