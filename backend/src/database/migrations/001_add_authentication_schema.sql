-- Migration: Add authentication schema
-- Description: Create users table and update user_profiles to reference it
-- Date: 2025-01-27

-- Create users table for authentication
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP WITH TIME ZONE,
    status user_status DEFAULT 'active'
);

-- Add user_id foreign key column to user_profiles table
-- First check if the column doesn't already exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_profiles' 
        AND column_name = 'user_id'
    ) THEN
        ALTER TABLE user_profiles ADD COLUMN user_id UUID REFERENCES users(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);
CREATE INDEX IF NOT EXISTS idx_users_last_login ON users(last_login_at);
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON user_profiles(user_id);

-- Update user_sessions table to reference users instead of user_profiles
-- First, drop the existing foreign key constraint
ALTER TABLE user_sessions DROP CONSTRAINT IF EXISTS user_sessions_user_id_fkey;

-- Add the new foreign key constraint to reference users table
ALTER TABLE user_sessions ADD CONSTRAINT user_sessions_user_id_fkey 
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- Add trigger for automatic updated_at timestamp on users table
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create function to validate email format
CREATE OR REPLACE FUNCTION validate_user_email()
RETURNS TRIGGER AS $
BEGIN
    -- Normalize email to lowercase
    NEW.email = LOWER(TRIM(NEW.email));
    
    -- Additional email validation
    IF LENGTH(NEW.email) < 5 OR LENGTH(NEW.email) > 255 THEN
        RAISE EXCEPTION 'Email must be between 5 and 255 characters';
    END IF;
    
    RETURN NEW;
END;
$ language 'plpgsql';

-- Create trigger for email validation
DROP TRIGGER IF EXISTS validate_user_email_trigger ON users;
CREATE TRIGGER validate_user_email_trigger 
    BEFORE INSERT OR UPDATE ON users 
    FOR EACH ROW EXECUTE FUNCTION validate_user_email();

-- Create function to update last_login_at timestamp
CREATE OR REPLACE FUNCTION update_last_login()
RETURNS TRIGGER AS $
BEGIN
    NEW.last_login_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$ language 'plpgsql';

-- Create view for user authentication summary
CREATE OR REPLACE VIEW user_auth_summary AS
SELECT 
    u.id,
    u.email,
    u.status,
    u.created_at,
    u.last_login_at,
    up.first_name,
    up.last_name,
    up.city_name,
    COUNT(DISTINCT us.id) as active_sessions
FROM users u
LEFT JOIN user_profiles up ON u.id = up.user_id
LEFT JOIN user_sessions us ON u.id = us.user_id AND us.expires_at > CURRENT_TIMESTAMP
WHERE u.status = 'active'
GROUP BY u.id, u.email, u.status, u.created_at, u.last_login_at, up.first_name, up.last_name, up.city_name;

-- Update the existing user_profile_summary view to include user authentication data
DROP VIEW IF EXISTS user_profile_summary;
CREATE OR REPLACE VIEW user_profile_summary AS
SELECT 
    up.id,
    up.first_name,
    up.last_name,
    up.city_name,
    up.status,
    up.created_at,
    u.email,
    u.last_login_at,
    COUNT(DISTINCT ci.id) as total_clothing_items,
    COUNT(DISTINCT o.id) as total_outfits,
    COUNT(DISTINCT CASE WHEN ci.is_favorite = true THEN ci.id END) as favorite_items,
    COUNT(DISTINCT CASE WHEN o.is_favorite = true THEN o.id END) as favorite_outfits
FROM user_profiles up
LEFT JOIN users u ON up.user_id = u.id
LEFT JOIN clothing_items ci ON up.id = ci.user_id AND ci.status = 'active'
LEFT JOIN outfits o ON up.id = o.user_id
WHERE up.status = 'active' AND (u.status = 'active' OR u.status IS NULL)
GROUP BY up.id, up.first_name, up.last_name, up.city_name, up.status, up.created_at, u.email, u.last_login_at;

-- Log migration execution
INSERT INTO initialization_log (script_name, status) 
VALUES ('001_add_authentication_schema.sql', 'success');