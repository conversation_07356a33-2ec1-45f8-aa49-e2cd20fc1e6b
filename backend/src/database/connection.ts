import { Pool, PoolClient, QueryResult, QueryResultRow } from 'pg';
import { databaseLogger } from '../utils/logger.js';

// Database configuration interface
interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  user: string;
  password: string;
  ssl?: boolean;
  maxConnections?: number;
  idleTimeoutMillis?: number;
  connectionTimeoutMillis?: number;
}

// Database connection class
export class DatabaseConnection {
  private pool: Pool;
  private config: DatabaseConfig;
  private isConnected: boolean = false;

  constructor(config: DatabaseConfig) {
    this.config = config;
    this.pool = new Pool({
      host: config.host,
      port: config.port,
      database: config.database,
      user: config.user,
      password: config.password,
      ssl: config.ssl ? { rejectUnauthorized: false } : false,
      max: config.maxConnections || 20,
      idleTimeoutMillis: config.idleTimeoutMillis || 30000,
      connectionTimeoutMillis: config.connectionTimeoutMillis || 2000,
    });

    // Handle pool events
    this.pool.on('connect', (client) => {
      databaseLogger.debug('New client connected to database', {
        totalCount: this.pool.totalCount,
        idleCount: this.pool.idleCount,
        waitingCount: this.pool.waitingCount
      });
    });

    this.pool.on('error', (err, client) => {
      databaseLogger.error('Unexpected error on idle client', {
        error: err.message,
        stack: err.stack
      }, err);
    });

    this.pool.on('remove', (client) => {
      databaseLogger.debug('Client removed from pool', {
        totalCount: this.pool.totalCount,
        idleCount: this.pool.idleCount
      });
    });
  }

  // Test database connection
  async testConnection(): Promise<boolean> {
    try {
      databaseLogger.info('Testing database connection');
      const client = await this.pool.connect();
      const result = await client.query('SELECT NOW() as current_time, version() as version');
      client.release();
      
      databaseLogger.info('Database connection test successful', {
        currentTime: result.rows[0].current_time,
        version: result.rows[0].version.split(' ')[0]
      });
      
      this.isConnected = true;
      return true;
    } catch (error) {
      databaseLogger.error('Database connection test failed', {
        host: this.config.host,
        port: this.config.port,
        database: this.config.database,
        user: this.config.user
      }, error instanceof Error ? error : undefined);
      
      this.isConnected = false;
      return false;
    }
  }

  // Execute a query
  async query<T extends QueryResultRow = any>(text: string, params?: any[], requestId?: string): Promise<QueryResult<T>> {
    const start = Date.now();
    
    try {
      databaseLogger.debug('Executing database query', {
        query: text,
        params: params ? '[REDACTED]' : undefined,
        paramCount: params?.length || 0
      }, requestId);

      const result = await this.pool.query<T>(text, params);
      const duration = Date.now() - start;
      
      databaseLogger.debug('Query executed successfully', {
        rowCount: result.rowCount,
        duration: `${duration}ms`,
        command: result.command
      }, requestId);

      return result;
    } catch (error) {
      const duration = Date.now() - start;
      databaseLogger.error('Query execution failed', {
        query: text,
        duration: `${duration}ms`,
        error: error instanceof Error ? error.message : 'Unknown error'
      }, error instanceof Error ? error : undefined, requestId);
      
      throw error;
    }
  }

  // Get a client from the pool for transactions
  async getClient(): Promise<PoolClient> {
    try {
      const client = await this.pool.connect();
      databaseLogger.debug('Client acquired from pool', {
        totalCount: this.pool.totalCount,
        idleCount: this.pool.idleCount,
        waitingCount: this.pool.waitingCount
      });
      return client;
    } catch (error) {
      databaseLogger.error('Failed to acquire client from pool', {}, error instanceof Error ? error : undefined);
      throw error;
    }
  }

  // Execute a transaction
  async transaction<T>(callback: (client: PoolClient) => Promise<T>, requestId?: string): Promise<T> {
    const client = await this.getClient();
    
    try {
      databaseLogger.debug('Starting database transaction', {}, requestId);
      await client.query('BEGIN');
      
      const result = await callback(client);
      
      await client.query('COMMIT');
      databaseLogger.debug('Transaction committed successfully', {}, requestId);
      
      return result;
    } catch (error) {
      await client.query('ROLLBACK');
      databaseLogger.error('Transaction rolled back due to error', {
        error: error instanceof Error ? error.message : 'Unknown error'
      }, error instanceof Error ? error : undefined, requestId);
      
      throw error;
    } finally {
      client.release();
      databaseLogger.debug('Transaction client released', {}, requestId);
    }
  }

  // Get connection pool stats
  getPoolStats() {
    return {
      totalCount: this.pool.totalCount,
      idleCount: this.pool.idleCount,
      waitingCount: this.pool.waitingCount,
      isConnected: this.isConnected
    };
  }

  // Close all connections
  async close(): Promise<void> {
    try {
      databaseLogger.info('Closing database connection pool');
      await this.pool.end();
      this.isConnected = false;
      databaseLogger.info('Database connection pool closed successfully');
    } catch (error) {
      databaseLogger.error('Error closing database connection pool', {}, error instanceof Error ? error : undefined);
      throw error;
    }
  }

  // Health check
  async healthCheck(): Promise<{ healthy: boolean; details: any }> {
    try {
      const start = Date.now();
      const result = await this.query('SELECT 1 as health_check');
      const duration = Date.now() - start;
      
      const stats = this.getPoolStats();
      
      return {
        healthy: true,
        details: {
          responseTime: `${duration}ms`,
          poolStats: stats,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      return {
        healthy: false,
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
          poolStats: this.getPoolStats(),
          timestamp: new Date().toISOString()
        }
      };
    }
  }
}

// Create database configuration from environment variables
export function createDatabaseConfig(): DatabaseConfig {
  return {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    database: process.env.DB_NAME || 'closet_glass_chic',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
    ssl: process.env.DB_SSL === 'true',
    maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || '20'),
    idleTimeoutMillis: parseInt(process.env.DB_IDLE_TIMEOUT || '30000'),
    connectionTimeoutMillis: parseInt(process.env.DB_CONNECTION_TIMEOUT || '2000'),
  };
}

// Singleton database instance
let dbInstance: DatabaseConnection | null = null;

export function getDatabase(): DatabaseConnection {
  if (!dbInstance) {
    const config = createDatabaseConfig();
    dbInstance = new DatabaseConnection(config);
  }
  return dbInstance;
}

// Initialize database connection
export async function initializeDatabase(): Promise<DatabaseConnection> {
  const db = getDatabase();
  
  databaseLogger.info('Initializing database connection');
  const isConnected = await db.testConnection();
  
  if (!isConnected) {
    throw new Error('Failed to connect to database');
  }
  
  databaseLogger.info('Database initialized successfully');
  return db;
}
