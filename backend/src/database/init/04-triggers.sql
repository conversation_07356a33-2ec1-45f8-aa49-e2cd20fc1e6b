-- Database triggers and functions
-- This script creates triggers for automatic timestamp updates and other automation

-- Function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- <PERSON>reate triggers to automatically update updated_at
DROP TRIGGER IF EXISTS update_user_profiles_updated_at ON user_profiles;
CREATE TRIGGER update_user_profiles_updated_at 
    BEFORE UPDATE ON user_profiles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_clothing_categories_updated_at ON clothing_categories;
CREATE TRIGGER update_clothing_categories_updated_at 
    BEFORE UPDATE ON clothing_categories 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_clothing_items_updated_at ON clothing_items;
CREATE TRIGGER update_clothing_items_updated_at 
    BEFORE UPDATE ON clothing_items 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_outfits_updated_at ON outfits;
CREATE TRIGGER update_outfits_updated_at 
    BEFORE UPDATE ON outfits 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to clean up expired weather cache
CREATE OR REPLACE FUNCTION cleanup_expired_weather_cache()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM weather_cache WHERE expires_at < CURRENT_TIMESTAMP;
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    IF deleted_count > 0 THEN
        INSERT INTO initialization_log (script_name, status) 
        VALUES ('cleanup_expired_weather_cache', 'cleaned ' || deleted_count || ' records');
    END IF;
    
    RETURN deleted_count;
END;
$$ language 'plpgsql';

-- Function to clean up expired user sessions
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM user_sessions WHERE expires_at < CURRENT_TIMESTAMP;
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    IF deleted_count > 0 THEN
        INSERT INTO initialization_log (script_name, status) 
        VALUES ('cleanup_expired_sessions', 'cleaned ' || deleted_count || ' records');
    END IF;
    
    RETURN deleted_count;
END;
$$ language 'plpgsql';

-- Function to update wear count when outfit is used
CREATE OR REPLACE FUNCTION update_outfit_wear_count()
RETURNS TRIGGER AS $$
BEGIN
    -- Update outfit wear count
    UPDATE outfits 
    SET wear_count = wear_count + 1, 
        last_worn_date = CURRENT_DATE 
    WHERE id = NEW.outfit_id;
    
    -- Update clothing items wear count
    UPDATE clothing_items 
    SET wear_count = wear_count + 1, 
        last_worn_date = CURRENT_DATE 
    WHERE id IN (
        SELECT clothing_item_id 
        FROM outfit_items 
        WHERE outfit_id = NEW.outfit_id
    );
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger for outfit usage tracking (this would be triggered by a separate usage log table)
-- For now, we'll create the function but not the trigger since we don't have a usage table yet

-- Function to validate user profile data
CREATE OR REPLACE FUNCTION validate_user_profile()
RETURNS TRIGGER AS $$
BEGIN
    -- Validate age (must be at least 13)
    IF NEW.date_of_birth > CURRENT_DATE - INTERVAL '13 years' THEN
        RAISE EXCEPTION 'User must be at least 13 years old';
    END IF;
    
    -- Validate coordinates
    IF NEW.latitude < -90 OR NEW.latitude > 90 THEN
        RAISE EXCEPTION 'Latitude must be between -90 and 90';
    END IF;
    
    IF NEW.longitude < -180 OR NEW.longitude > 180 THEN
        RAISE EXCEPTION 'Longitude must be between -180 and 180';
    END IF;
    
    -- Trim whitespace from text fields
    NEW.first_name = TRIM(NEW.first_name);
    NEW.last_name = TRIM(NEW.last_name);
    NEW.city_name = TRIM(NEW.city_name);
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for user profile validation
DROP TRIGGER IF EXISTS validate_user_profile_trigger ON user_profiles;
CREATE TRIGGER validate_user_profile_trigger 
    BEFORE INSERT OR UPDATE ON user_profiles 
    FOR EACH ROW EXECUTE FUNCTION validate_user_profile();

-- Log trigger creation
INSERT INTO initialization_log (script_name, status) 
VALUES ('04-triggers.sql', 'success');

-- Display trigger creation message
DO $$
BEGIN
    RAISE NOTICE 'Database triggers and functions created successfully';
    RAISE NOTICE 'Automatic timestamp updates, validation, and cleanup functions enabled';
END $$;
