-- Seed data for development and testing
-- This script inserts default categories and sample data

-- Insert default clothing categories
INSERT INTO clothing_categories (name, description) VALUES
('Tops', 'Upper body clothing items'),
('Bottoms', 'Lower body clothing items'),
('Dresses', 'One-piece garments'),
('Outerwear', 'Jackets, coats, and outer garments'),
('Shoes', 'Footwear'),
('Accessories', 'Bags, jewelry, and other accessories'),
('Undergarments', 'Underwear and intimate apparel'),
('Activewear', 'Sports and exercise clothing'),
('Sleepwear', 'Pajamas and nightwear'),
('Formal', 'Formal and business attire')
ON CONFLICT (name) DO NOTHING;

-- Insert subcategories
INSERT INTO clothing_categories (name, description, parent_category_id) VALUES
('T-Shirts', 'Casual t-shirts and tees', (SELECT id FROM clothing_categories WHERE name = 'Tops')),
('Blouses', 'Dress shirts and blouses', (SELECT id FROM clothing_categories WHERE name = 'Tops')),
('Sweaters', 'Knitwear and sweaters', (SELECT id FROM clothing_categories WHERE name = 'Tops')),
('Tank Tops', 'Sleeveless tops', (SELECT id FROM clothing_categories WHERE name = 'Tops')),
('Hoodies', 'Hooded sweatshirts', (SELECT id FROM clothing_categories WHERE name = 'Tops')),

('Jeans', 'Denim pants', (SELECT id FROM clothing_categories WHERE name = 'Bottoms')),
('Trousers', 'Dress pants and trousers', (SELECT id FROM clothing_categories WHERE name = 'Bottoms')),
('Skirts', 'Skirts of all lengths', (SELECT id FROM clothing_categories WHERE name = 'Bottoms')),
('Shorts', 'Short pants', (SELECT id FROM clothing_categories WHERE name = 'Bottoms')),
('Leggings', 'Stretchy fitted pants', (SELECT id FROM clothing_categories WHERE name = 'Bottoms')),

('Sneakers', 'Athletic and casual sneakers', (SELECT id FROM clothing_categories WHERE name = 'Shoes')),
('Heels', 'High heels and dress shoes', (SELECT id FROM clothing_categories WHERE name = 'Shoes')),
('Boots', 'Boots of all styles', (SELECT id FROM clothing_categories WHERE name = 'Shoes')),
('Flats', 'Flat shoes and ballet flats', (SELECT id FROM clothing_categories WHERE name = 'Shoes')),
('Sandals', 'Open-toe footwear', (SELECT id FROM clothing_categories WHERE name = 'Shoes')),

('Handbags', 'Purses and handbags', (SELECT id FROM clothing_categories WHERE name = 'Accessories')),
('Jewelry', 'Necklaces, earrings, bracelets', (SELECT id FROM clothing_categories WHERE name = 'Accessories')),
('Belts', 'Waist belts', (SELECT id FROM clothing_categories WHERE name = 'Accessories')),
('Scarves', 'Neck and head scarves', (SELECT id FROM clothing_categories WHERE name = 'Accessories')),
('Hats', 'Caps and hats', (SELECT id FROM clothing_categories WHERE name = 'Accessories'))
ON CONFLICT (name) DO NOTHING;

-- Create view for user profile summary
CREATE OR REPLACE VIEW user_profile_summary AS
SELECT 
    up.id,
    up.first_name,
    up.last_name,
    up.city_name,
    up.status,
    up.created_at,
    COUNT(DISTINCT ci.id) as total_clothing_items,
    COUNT(DISTINCT o.id) as total_outfits,
    COUNT(DISTINCT CASE WHEN ci.is_favorite = true THEN ci.id END) as favorite_items,
    COUNT(DISTINCT CASE WHEN o.is_favorite = true THEN o.id END) as favorite_outfits
FROM user_profiles up
LEFT JOIN clothing_items ci ON up.id = ci.user_id AND ci.status = 'active'
LEFT JOIN outfits o ON up.id = o.user_id
WHERE up.status = 'active'
GROUP BY up.id, up.first_name, up.last_name, up.city_name, up.status, up.created_at;

-- Create view for clothing category hierarchy
CREATE OR REPLACE VIEW clothing_category_hierarchy AS
WITH RECURSIVE category_tree AS (
    -- Base case: top-level categories
    SELECT 
        id,
        name,
        description,
        parent_category_id,
        name::text as full_path,
        0 as level
    FROM clothing_categories 
    WHERE parent_category_id IS NULL
    
    UNION ALL
    
    -- Recursive case: subcategories
    SELECT 
        cc.id,
        cc.name,
        cc.description,
        cc.parent_category_id,
        (ct.full_path || ' > ' || cc.name)::text as full_path,
        ct.level + 1 as level
    FROM clothing_categories cc
    JOIN category_tree ct ON cc.parent_category_id = ct.id
)
SELECT * FROM category_tree ORDER BY full_path;

-- Create view for popular clothing items
CREATE OR REPLACE VIEW popular_clothing_items AS
SELECT 
    ci.id,
    ci.name,
    ci.color,
    ci.brand,
    ci.wear_count,
    ci.is_favorite,
    cc.name as category_name,
    up.first_name || ' ' || up.last_name as owner_name
FROM clothing_items ci
JOIN clothing_categories cc ON ci.category_id = cc.id
JOIN user_profiles up ON ci.user_id = up.id
WHERE ci.status = 'active' AND up.status = 'active'
ORDER BY ci.wear_count DESC, ci.is_favorite DESC;

-- Insert sample development data (only if in development environment)
-- This will be controlled by environment variables in the application

-- Log seed data creation
INSERT INTO initialization_log (script_name, status) 
VALUES ('05-seed-data.sql', 'success');

-- Display seed data message
DO $$
DECLARE
    category_count INTEGER;
    view_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO category_count FROM clothing_categories;
    SELECT COUNT(*) INTO view_count FROM information_schema.views WHERE table_schema = 'public';
    
    RAISE NOTICE 'Seed data inserted successfully';
    RAISE NOTICE 'Clothing categories: %', category_count;
    RAISE NOTICE 'Database views created: %', view_count;
END $$;
