-- Database indexes for performance optimization
-- This script creates all necessary indexes

-- User Profiles Indexes
CREATE INDEX IF NOT EXISTS idx_user_profiles_name ON user_profiles(first_name, last_name);
CREATE INDEX IF NOT EXISTS idx_user_profiles_location ON user_profiles(latitude, longitude);
CREATE INDEX IF NOT EXISTS idx_user_profiles_created_at ON user_profiles(created_at);
CREATE INDEX IF NOT EXISTS idx_user_profiles_status ON user_profiles(status);
CREATE INDEX IF NOT EXISTS idx_user_profiles_city ON user_profiles(city_name);

-- Clothing Categories Indexes
CREATE INDEX IF NOT EXISTS idx_clothing_categories_name ON clothing_categories(name);
CREATE INDEX IF NOT EXISTS idx_clothing_categories_parent ON clothing_categories(parent_category_id);

-- Clothing Items Indexes
CREATE INDEX IF NOT EXISTS idx_clothing_items_user_id ON clothing_items(user_id);
CREATE INDEX IF NOT EXISTS idx_clothing_items_category_id ON clothing_items(category_id);
CREATE INDEX IF NOT EXISTS idx_clothing_items_status ON clothing_items(status);
CREATE INDEX IF NOT EXISTS idx_clothing_items_tags ON clothing_items USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_clothing_items_created_at ON clothing_items(created_at);
CREATE INDEX IF NOT EXISTS idx_clothing_items_favorite ON clothing_items(is_favorite);
CREATE INDEX IF NOT EXISTS idx_clothing_items_color ON clothing_items(color);
CREATE INDEX IF NOT EXISTS idx_clothing_items_brand ON clothing_items(brand);

-- Outfits Indexes
CREATE INDEX IF NOT EXISTS idx_outfits_user_id ON outfits(user_id);
CREATE INDEX IF NOT EXISTS idx_outfits_season ON outfits(season);
CREATE INDEX IF NOT EXISTS idx_outfits_created_at ON outfits(created_at);
CREATE INDEX IF NOT EXISTS idx_outfits_favorite ON outfits(is_favorite);
CREATE INDEX IF NOT EXISTS idx_outfits_occasion ON outfits(occasion);

-- Outfit Items Indexes
CREATE INDEX IF NOT EXISTS idx_outfit_items_outfit_id ON outfit_items(outfit_id);
CREATE INDEX IF NOT EXISTS idx_outfit_items_clothing_item_id ON outfit_items(clothing_item_id);

-- Weather Cache Indexes
CREATE INDEX IF NOT EXISTS idx_weather_cache_location ON weather_cache(latitude, longitude);
CREATE INDEX IF NOT EXISTS idx_weather_cache_expires_at ON weather_cache(expires_at);
CREATE INDEX IF NOT EXISTS idx_weather_cache_cached_at ON weather_cache(cached_at);

-- User Sessions Indexes
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at);

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_clothing_items_user_status ON clothing_items(user_id, status);
CREATE INDEX IF NOT EXISTS idx_outfits_user_season ON outfits(user_id, season);
CREATE INDEX IF NOT EXISTS idx_user_profiles_status_created ON user_profiles(status, created_at);

-- Log index creation
INSERT INTO initialization_log (script_name, status) 
VALUES ('03-indexes.sql', 'success');

-- Display index creation message
DO $$
BEGIN
    RAISE NOTICE 'Database indexes created successfully';
    RAISE NOTICE 'Performance optimization indexes added for all tables';
END $$;
