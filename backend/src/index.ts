import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';
import { weatherRouter } from './routes/weather.js';
import { cityRouter } from './routes/cities.js';
import { userProfileRouter } from './routes/userProfile.js';
import { fileUploadRouter } from './routes/fileUpload.js';
import { authRouter } from './routes/auth.js';
import { clothingRouter } from './routes/clothing.js';
import { outfitsRouter } from './routes/outfits.js';
import scheduleRouter from './routes/schedule.js';
import { errorHandler } from './middleware/errorHandler.js';
import { rateLimiter, apiRateLimiter, uploadRateLimiter } from './middleware/rateLimiter.js';
import { requestIdMiddleware, requestLoggingMiddleware, logger } from './utils/logger.js';
import { initializeDatabase } from './database/connection.js';
import { initializeDatabaseSchema } from './database/migrations.js';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Initialize database and start server
async function startServer() {
  try {
    logger.info('SERVER', 'Starting Closet Glass Chic backend server');

    // Initialize database connection
    logger.info('SERVER', 'Initializing database connection');
    const db = await initializeDatabase();

    // Check if schema exists, skip migrations if already initialized
    logger.info('SERVER', 'Checking database schema');
    try {
      const result = await db.query('SELECT COUNT(*) FROM user_profiles LIMIT 1');
      logger.info('SERVER', 'Database schema already exists, skipping migrations');
    } catch (error) {
      logger.info('SERVER', 'Running database migrations');
      await initializeDatabaseSchema(db);
    }

    logger.info('SERVER', 'Database initialization completed successfully');
  } catch (error) {
    logger.error('SERVER', 'Failed to initialize database', {
      error: error instanceof Error ? error.message : 'Unknown error'
    }, error instanceof Error ? error : undefined);

    process.exit(1);
  }
}

// Request ID middleware (must be first)
app.use(requestIdMiddleware);

// Security middleware with custom configuration for CORS
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));

// CORS configuration with multiple allowed origins
const allowedOrigins = [
  process.env.FRONTEND_URL || 'http://localhost:5173',
  'http://**************:5173',
  'http://127.0.0.1:5173',
  'http://localhost:5173'
];

app.use(cors({
  origin: (origin, callback) => {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);

    if (allowedOrigins.includes(origin)) {
      return callback(null, true);
    }

    // Log the rejected origin for debugging
    logger.warn('CORS', `Blocked request from origin: ${origin}`);
    return callback(new Error('Not allowed by CORS'));
  },
  credentials: true
}));

// Logging middleware
app.use(morgan('combined'));
app.use(requestLoggingMiddleware);

// Body parsing
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Static file serving for uploads with CORS headers
app.use('/uploads', (req, res, next) => {
  const origin = req.headers.origin;

  // Set CORS headers for static files
  if (!origin || allowedOrigins.includes(origin)) {
    res.header('Access-Control-Allow-Origin', origin || '*');
  }
  res.header('Access-Control-Allow-Methods', 'GET');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
  res.header('Access-Control-Allow-Credentials', 'true');
  next();
}, express.static('uploads'));

// Rate limiting
app.use(rateLimiter);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0'
  });
});

// API routes with specific rate limiting
app.use('/api/auth', apiRateLimiter, authRouter);
app.use('/api/weather', apiRateLimiter, weatherRouter);
app.use('/api/cities', apiRateLimiter, cityRouter);
app.use('/api/user-profile', apiRateLimiter, userProfileRouter);
app.use('/api/upload', uploadRateLimiter, fileUploadRouter);
app.use('/api/clothing', uploadRateLimiter, clothingRouter);
app.use('/api/outfits', apiRateLimiter, outfitsRouter);
app.use('/api/schedule', apiRateLimiter, scheduleRouter);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: `Route ${req.originalUrl} not found`,
    timestamp: new Date().toISOString()
  });
});

// Error handling middleware
app.use(errorHandler);

// Start server after database initialization
startServer().then(() => {
  app.listen(PORT, () => {
    logger.info('SERVER', 'Backend server started successfully', {
      port: PORT,
      environment: process.env.NODE_ENV || 'development',
      healthCheck: `http://localhost:${PORT}/health`
    });
  });
}).catch((error) => {
  logger.error('SERVER', 'Failed to start server', {
    error: error instanceof Error ? error.message : 'Unknown error'
  }, error instanceof Error ? error : undefined);

  process.exit(1);
});

export default app;
