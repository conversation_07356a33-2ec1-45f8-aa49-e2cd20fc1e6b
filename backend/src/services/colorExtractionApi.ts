import FormData from 'form-data';
import fetch from 'node-fetch';
import { logger } from '../utils/logger.js';

export interface ColorExtractionResponse {
  color_name: string;
  hex: string;
  rgb: {
    r: number;
    g: number;
    b: number;
  };
}

export interface ColorExtractionError {
  error: string;
  message?: string;
}

export class ColorExtractionService {
  private static baseUrl: string;

  static initialize() {
    const host = process.env.COLOR_EXTRACTION_API_HOST || '**************';
    const port = process.env.COLOR_EXTRACTION_API_PORT || '7563';
    this.baseUrl = `http://${host}:${port}`;
    
    logger.info('COLOR_EXTRACTION_SERVICE', 'Initialized color extraction service', {
      baseUrl: this.baseUrl
    });
  }

  /**
   * Extract dominant color from an image file
   */
  static async extractColor(imageBuffer: Buffer, filename: string): Promise<ColorExtractionResponse> {
    if (!this.baseUrl) {
      this.initialize();
    }

    try {
      const formData = new FormData();
      formData.append('image', imageBuffer, {
        filename: filename,
        contentType: 'image/jpeg'
      });

      const response = await fetch(`${this.baseUrl}/api/color`, {
        method: 'POST',
        body: formData,
        headers: formData.getHeaders()
      });

      if (!response.ok) {
        const errorText = await response.text();
        logger.error('COLOR_EXTRACTION_SERVICE', 'API request failed', {
          status: response.status,
          statusText: response.statusText,
          error: errorText
        });
        
        throw new Error(`Color extraction API failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json() as ColorExtractionResponse;
      
      logger.info('COLOR_EXTRACTION_SERVICE', 'Successfully extracted color', {
        colorName: result.color_name,
        hex: result.hex,
        filename
      });

      return result;
    } catch (error) {
      logger.error('COLOR_EXTRACTION_SERVICE', 'Error extracting color', {
        error: error instanceof Error ? error.message : 'Unknown error',
        filename
      }, error instanceof Error ? error : undefined);

      throw new Error(`Failed to extract color: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Check if the color extraction service is available
   */
  static async healthCheck(): Promise<boolean> {
    if (!this.baseUrl) {
      this.initialize();
    }

    try {
      const response = await fetch(`${this.baseUrl}/health`, {
        method: 'GET'
      });

      return response.ok;
    } catch (error) {
      logger.warn('COLOR_EXTRACTION_SERVICE', 'Health check failed', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  }
}
