import { DatabaseConnection } from '../database/connection.js';
import { databaseLogger } from '../utils/logger.js';
import { v4 as uuidv4 } from 'uuid';

// User profile interfaces
export interface UserProfile {
  id: string;
  firstName: string;
  lastName: string;
  gender: 'Male' | 'Female' | 'Other' | 'Prefer not to say';
  dateOfBirth: string;
  cityName: string;
  latitude: number;
  longitude: number;
  weatherPreferences?: any;
  status?: 'active' | 'inactive' | 'suspended';
  createdAt: string;
  updatedAt: string;
}

export interface CreateUserProfileData {
  userId?: string; // Optional - if not provided, will be generated
  firstName: string;
  lastName: string;
  gender: 'Male' | 'Female' | 'Other' | 'Prefer not to say';
  dateOfBirth: string;
  cityName: string;
  latitude: number;
  longitude: number;
  weatherPreferences?: any;
}

export interface UpdateUserProfileData {
  firstName?: string;
  lastName?: string;
  gender?: 'Male' | 'Female' | 'Other' | 'Prefer not to say';
  dateOfBirth?: string;
  cityName?: string;
  latitude?: number;
  longitude?: number;
  weatherPreferences?: any;
  status?: 'active' | 'inactive' | 'suspended';
}

// Validation result interface
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

// User profile service class
export class UserProfileService {
  private db: DatabaseConnection;

  constructor(db: DatabaseConnection) {
    this.db = db;
  }

  // Validate user profile data
  validateUserProfile(data: CreateUserProfileData): ValidationResult {
    const errors: string[] = [];

    // Validate first name
    if (!data.firstName || data.firstName.trim().length === 0) {
      errors.push('First name is required');
    } else if (data.firstName.trim().length > 50) {
      errors.push('First name must be 50 characters or less');
    }

    // Validate last name
    if (!data.lastName || data.lastName.trim().length === 0) {
      errors.push('Last name is required');
    } else if (data.lastName.trim().length > 50) {
      errors.push('Last name must be 50 characters or less');
    }

    // Validate gender
    const validGenders = ['Male', 'Female', 'Other', 'Prefer not to say'];
    if (!data.gender || !validGenders.includes(data.gender)) {
      errors.push('Valid gender selection is required');
    }

    // Validate date of birth
    if (!data.dateOfBirth) {
      errors.push('Date of birth is required');
    } else {
      const birthDate = new Date(data.dateOfBirth);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      
      if (isNaN(birthDate.getTime())) {
        errors.push('Invalid date of birth format');
      } else if (birthDate > today) {
        errors.push('Date of birth cannot be in the future');
      } else if (age < 13) {
        errors.push('User must be at least 13 years old');
      } else if (age > 120) {
        errors.push('Please enter a valid date of birth');
      }
    }

    // Validate city name
    if (!data.cityName || data.cityName.trim().length === 0) {
      errors.push('City name is required');
    } else if (data.cityName.trim().length > 100) {
      errors.push('City name must be 100 characters or less');
    }

    // Validate coordinates
    if (typeof data.latitude !== 'number' || data.latitude < -90 || data.latitude > 90) {
      errors.push('Valid latitude is required (-90 to 90)');
    }

    if (typeof data.longitude !== 'number' || data.longitude < -180 || data.longitude > 180) {
      errors.push('Valid longitude is required (-180 to 180)');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Create a new user profile
  async createUserProfile(data: CreateUserProfileData, requestId?: string): Promise<UserProfile> {
    databaseLogger.info('Creating user profile', {
      firstName: data.firstName,
      lastName: data.lastName,
      cityName: data.cityName
    }, requestId);

    // Validate input data
    const validation = this.validateUserProfile(data);
    if (!validation.isValid) {
      databaseLogger.error('User profile validation failed', {
        errors: validation.errors
      }, undefined, requestId);
      throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
    }

    try {
      // Use provided userId or generate a new one
      const userId = data.userId || uuidv4();

      const result = await this.db.query<UserProfile>(
        `INSERT INTO user_profiles (
          user_id, first_name, last_name, gender, date_of_birth,
          city_name, latitude, longitude, weather_preferences
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        RETURNING
          id,
          first_name as "firstName",
          last_name as "lastName",
          gender,
          date_of_birth as "dateOfBirth",
          city_name as "cityName",
          latitude,
          longitude,
          weather_preferences as "weatherPreferences",
          status,
          created_at as "createdAt",
          updated_at as "updatedAt"`,
        [
          userId,
          data.firstName.trim(),
          data.lastName.trim(),
          data.gender,
          data.dateOfBirth,
          data.cityName.trim(),
          data.latitude,
          data.longitude,
          JSON.stringify(data.weatherPreferences || {})
        ],
        requestId
      );

      const userProfile = result.rows[0];
      
      databaseLogger.info('User profile created successfully', {
        id: userProfile.id,
        firstName: userProfile.firstName,
        lastName: userProfile.lastName
      }, requestId);

      return userProfile;
    } catch (error) {
      databaseLogger.error('Failed to create user profile', {
        error: error instanceof Error ? error.message : 'Unknown error'
      }, error instanceof Error ? error : undefined, requestId);
      
      throw new Error(`Failed to create user profile: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Get user profile by user ID
  async getUserProfileByUserId(userId: string, requestId?: string): Promise<UserProfile | null> {
    databaseLogger.debug('Fetching user profile by user ID', { userId }, requestId);

    try {
      const result = await this.db.query<UserProfile>(
        `SELECT
          id,
          first_name as "firstName",
          last_name as "lastName",
          gender,
          date_of_birth as "dateOfBirth",
          city_name as "cityName",
          latitude,
          longitude,
          weather_preferences as "weatherPreferences",
          status,
          created_at as "createdAt",
          updated_at as "updatedAt"
        FROM user_profiles
        WHERE user_id = $1 AND status = 'active'`,
        [userId],
        requestId
      );

      if (result.rows.length === 0) {
        databaseLogger.warn('User profile not found for user', { userId }, undefined, requestId);
        return null;
      }

      const userProfile = result.rows[0];
      databaseLogger.debug('User profile fetched successfully', { userId }, requestId);

      return userProfile;
    } catch (error) {
      databaseLogger.error('Failed to fetch user profile by user ID', {
        userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      }, error instanceof Error ? error : undefined, requestId);

      throw new Error(`Failed to fetch user profile: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Get user profile by ID
  async getUserProfileById(id: string, requestId?: string): Promise<UserProfile | null> {
    databaseLogger.debug('Fetching user profile by ID', { id }, requestId);

    try {
      const result = await this.db.query<UserProfile>(
        `SELECT 
          id,
          first_name as "firstName",
          last_name as "lastName",
          gender,
          date_of_birth as "dateOfBirth",
          city_name as "cityName",
          latitude,
          longitude,
          weather_preferences as "weatherPreferences",
          status,
          created_at as "createdAt",
          updated_at as "updatedAt"
        FROM user_profiles 
        WHERE id = $1 AND status = 'active'`,
        [id],
        requestId
      );

      if (result.rows.length === 0) {
        databaseLogger.warn('User profile not found', { id }, undefined, requestId);
        return null;
      }

      const userProfile = result.rows[0];
      databaseLogger.debug('User profile fetched successfully', { id }, requestId);
      
      return userProfile;
    } catch (error) {
      databaseLogger.error('Failed to fetch user profile', {
        id,
        error: error instanceof Error ? error.message : 'Unknown error'
      }, error instanceof Error ? error : undefined, requestId);
      
      throw new Error(`Failed to fetch user profile: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Update user profile
  async updateUserProfile(id: string, data: UpdateUserProfileData, requestId?: string): Promise<UserProfile | null> {
    databaseLogger.info('Updating user profile', { id }, requestId);

    // Build dynamic update query
    const updateFields: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    if (data.firstName !== undefined) {
      updateFields.push(`first_name = $${paramIndex++}`);
      values.push(data.firstName.trim());
    }

    if (data.lastName !== undefined) {
      updateFields.push(`last_name = $${paramIndex++}`);
      values.push(data.lastName.trim());
    }

    if (data.gender !== undefined) {
      updateFields.push(`gender = $${paramIndex++}`);
      values.push(data.gender);
    }

    if (data.dateOfBirth !== undefined) {
      updateFields.push(`date_of_birth = $${paramIndex++}`);
      values.push(data.dateOfBirth);
    }

    if (data.cityName !== undefined) {
      updateFields.push(`city_name = $${paramIndex++}`);
      values.push(data.cityName.trim());
    }

    if (data.latitude !== undefined) {
      updateFields.push(`latitude = $${paramIndex++}`);
      values.push(data.latitude);
    }

    if (data.longitude !== undefined) {
      updateFields.push(`longitude = $${paramIndex++}`);
      values.push(data.longitude);
    }

    if (data.weatherPreferences !== undefined) {
      updateFields.push(`weather_preferences = $${paramIndex++}`);
      values.push(JSON.stringify(data.weatherPreferences));
    }

    if (data.status !== undefined) {
      updateFields.push(`status = $${paramIndex++}`);
      values.push(data.status);
    }

    if (updateFields.length === 0) {
      databaseLogger.warn('No fields to update', { id }, undefined, requestId);
      return await this.getUserProfileById(id, requestId);
    }

    // Add ID parameter
    values.push(id);

    try {
      const result = await this.db.query<UserProfile>(
        `UPDATE user_profiles 
        SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP
        WHERE id = $${paramIndex} AND status != 'suspended'
        RETURNING 
          id,
          first_name as "firstName",
          last_name as "lastName",
          gender,
          date_of_birth as "dateOfBirth",
          city_name as "cityName",
          latitude,
          longitude,
          weather_preferences as "weatherPreferences",
          status,
          created_at as "createdAt",
          updated_at as "updatedAt"`,
        values,
        requestId
      );

      if (result.rows.length === 0) {
        databaseLogger.warn('User profile not found for update', { id }, undefined, requestId);
        return null;
      }

      const userProfile = result.rows[0];
      databaseLogger.info('User profile updated successfully', { id }, requestId);
      
      return userProfile;
    } catch (error) {
      databaseLogger.error('Failed to update user profile', {
        id,
        error: error instanceof Error ? error.message : 'Unknown error'
      }, error instanceof Error ? error : undefined, requestId);
      
      throw new Error(`Failed to update user profile: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Delete user profile (soft delete)
  async deleteUserProfile(id: string, requestId?: string): Promise<boolean> {
    databaseLogger.info('Deleting user profile', { id }, requestId);

    try {
      const result = await this.db.query(
        `UPDATE user_profiles 
        SET status = 'inactive', updated_at = CURRENT_TIMESTAMP
        WHERE id = $1 AND status = 'active'`,
        [id],
        requestId
      );

      const deleted = (result.rowCount || 0) > 0;
      
      if (deleted) {
        databaseLogger.info('User profile deleted successfully', { id }, requestId);
      } else {
        databaseLogger.warn('User profile not found for deletion', { id }, undefined, requestId);
      }
      
      return deleted;
    } catch (error) {
      databaseLogger.error('Failed to delete user profile', {
        id,
        error: error instanceof Error ? error.message : 'Unknown error'
      }, error instanceof Error ? error : undefined, requestId);
      
      throw new Error(`Failed to delete user profile: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Get all user profiles (with pagination)
  async getUserProfiles(limit: number = 50, offset: number = 0, requestId?: string): Promise<{ profiles: UserProfile[]; total: number }> {
    databaseLogger.debug('Fetching user profiles', { limit, offset }, requestId);

    try {
      // Get total count
      const countResult = await this.db.query(
        'SELECT COUNT(*) as total FROM user_profiles WHERE status = \'active\'',
        [],
        requestId
      );

      // Get profiles
      const result = await this.db.query<UserProfile>(
        `SELECT 
          id,
          first_name as "firstName",
          last_name as "lastName",
          gender,
          date_of_birth as "dateOfBirth",
          city_name as "cityName",
          latitude,
          longitude,
          weather_preferences as "weatherPreferences",
          status,
          created_at as "createdAt",
          updated_at as "updatedAt"
        FROM user_profiles 
        WHERE status = 'active'
        ORDER BY created_at DESC
        LIMIT $1 OFFSET $2`,
        [limit, offset],
        requestId
      );

      const total = parseInt(countResult.rows[0].total);
      
      databaseLogger.debug('User profiles fetched successfully', {
        count: result.rows.length,
        total,
        limit,
        offset
      }, requestId);

      return {
        profiles: result.rows,
        total
      };
    } catch (error) {
      databaseLogger.error('Failed to fetch user profiles', {
        error: error instanceof Error ? error.message : 'Unknown error'
      }, error instanceof Error ? error : undefined, requestId);
      
      throw new Error(`Failed to fetch user profiles: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
