import FormData from 'form-data';
import fetch from 'node-fetch';
import { logger } from '../utils/logger.js';

export interface ClothDetection {
  category: string;
  score: number;
}

export interface ClothDetectionResponse {
  detections: ClothDetection[];
  processing_time_sec: number;
}

export interface ClothDetectionError {
  error: string;
  message?: string;
}

export class ClothDetectionService {
  private static baseUrl: string;

  static initialize() {
    const host = process.env.CLOTH_DETECTION_API_HOST || '**************';
    const port = process.env.CLOTH_DETECTION_API_PORT || '9743';
    this.baseUrl = `http://${host}:${port}`;
    
    logger.info('CLOTH_DETECTION_SERVICE', 'Initialized cloth detection service', {
      baseUrl: this.baseUrl
    });
  }

  /**
   * Detect clothing type from an image file
   */
  static async detectClothing(imageBuffer: Buffer, filename: string): Promise<ClothDetectionResponse> {
    if (!this.baseUrl) {
      this.initialize();
    }

    try {
      const formData = new FormData();
      formData.append('file', imageBuffer, {
        filename: filename,
        contentType: 'image/jpeg'
      });

      const response = await fetch(`${this.baseUrl}/api/analyze`, {
        method: 'POST',
        body: formData,
        headers: formData.getHeaders()
      });

      if (!response.ok) {
        const errorText = await response.text();
        logger.error('CLOTH_DETECTION_SERVICE', 'API request failed', {
          status: response.status,
          statusText: response.statusText,
          error: errorText
        });
        
        throw new Error(`Cloth detection API failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json() as ClothDetectionResponse;
      
      logger.info('CLOTH_DETECTION_SERVICE', 'Successfully detected clothing', {
        detectionsCount: result.detections.length,
        topDetection: result.detections[0]?.category,
        topScore: result.detections[0]?.score,
        processingTime: result.processing_time_sec,
        filename
      });

      return result;
    } catch (error) {
      logger.error('CLOTH_DETECTION_SERVICE', 'Error detecting clothing', {
        error: error instanceof Error ? error.message : 'Unknown error',
        filename
      }, error instanceof Error ? error : undefined);

      throw new Error(`Failed to detect clothing: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get the best clothing category from detection results
   */
  static getBestCategory(detections: ClothDetection[]): string {
    if (!detections || detections.length === 0) {
      return 'Unknown';
    }

    // Sort by score descending and return the highest scoring category
    const sortedDetections = detections.sort((a, b) => b.score - a.score);
    return sortedDetections[0].category;
  }

  /**
   * Check if the cloth detection service is available
   */
  static async healthCheck(): Promise<boolean> {
    if (!this.baseUrl) {
      this.initialize();
    }

    try {
      const response = await fetch(`${this.baseUrl}/health`, {
        method: 'GET'
      });

      return response.ok;
    } catch (error) {
      logger.warn('CLOTH_DETECTION_SERVICE', 'Health check failed', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  }
}
