import { Router } from 'express';
import { validateUserProfile } from '../middleware/validation.js';
import { UserProfileService } from '../services/userProfileService.js';
import { getDatabase } from '../database/connection.js';
import { apiLogger } from '../utils/logger.js';

const router = Router();

// Initialize user profile service
const db = getDatabase();
const userProfileService = new UserProfileService(db);

/**
 * POST /api/user-profile
 * Create a new user profile
 * 
 * Body:
 * - firstName: string (required)
 * - lastName: string (required)
 * - gender: string (required)
 * - dateOfBirth: string (required)
 * - cityName: string (required)
 * - latitude: number (required)
 * - longitude: number (required)
 * - weatherPreferences: object (optional)
 * 
 * Response:
 * - 201: User profile created successfully
 * - 400: Invalid profile data
 * - 500: Server error
 */
router.post('/', validateUserProfile, async (req, res, next) => {
  const requestId = req.headers['x-request-id'] as string;

  try {
    apiLogger.info('Creating user profile', {
      firstName: req.body.firstName,
      lastName: req.body.lastName,
      cityName: req.body.cityName
    }, requestId);

    const userProfile = await userProfileService.createUserProfile(req.body, requestId);

    res.status(201).json({
      success: true,
      data: userProfile,
      message: 'User profile created successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    apiLogger.error('User profile creation failed', {
      error: error instanceof Error ? error.message : 'Unknown error'
    }, error instanceof Error ? error : undefined, requestId);

    next(error);
  }
});

/**
 * GET /api/user-profile/:id
 * Get user profile by ID
 * 
 * Path Parameters:
 * - id: string (required) - User profile ID
 * 
 * Response:
 * - 200: User profile data
 * - 404: Profile not found
 * - 500: Server error
 */
router.get('/:id', async (req, res, next) => {
  const requestId = req.headers['x-request-id'] as string;

  try {
    const { id } = req.params;

    apiLogger.debug('Fetching user profile', { id }, requestId);

    const userProfile = await userProfileService.getUserProfileById(id, requestId);

    if (!userProfile) {
      return res.status(404).json({
        success: false,
        error: 'User profile not found',
        timestamp: new Date().toISOString()
      });
    }

    res.json({
      success: true,
      data: userProfile,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    apiLogger.error('Failed to fetch user profile', {
      id: req.params.id,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, error instanceof Error ? error : undefined, requestId);

    next(error);
  }
});

/**
 * GET /api/user-profile/user/:userId
 * Get user profile by user ID
 *
 * Path Parameters:
 * - userId: string (required) - User ID
 *
 * Response:
 * - 200: Profile found
 * - 404: Profile not found
 * - 500: Server error
 */
router.get('/user/:userId', async (req, res, next) => {
  const requestId = req.headers['x-request-id'] as string;

  try {
    const { userId } = req.params;

    apiLogger.debug('Fetching user profile by user ID', { userId }, requestId);

    const userProfile = await userProfileService.getUserProfileByUserId(userId, requestId);

    if (!userProfile) {
      return res.status(404).json({
        success: false,
        error: 'User profile not found',
        message: `No profile found for user ID: ${userId}`,
        timestamp: new Date().toISOString()
      });
    }

    res.json({
      success: true,
      data: userProfile,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    apiLogger.error('Failed to fetch user profile by user ID', {
      userId: req.params.userId,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, error instanceof Error ? error : undefined, requestId);

    next(error);
  }
});

/**
 * PUT /api/user-profile/:id
 * Update user profile
 * 
 * Path Parameters:
 * - id: string (required) - User profile ID
 * 
 * Body: Partial user profile data
 * 
 * Response:
 * - 200: Profile updated successfully
 * - 404: Profile not found
 * - 400: Invalid data
 * - 500: Server error
 */
router.put('/:id', async (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    
    // In a real application, you would update in database
    res.status(501).json({
      success: false,
      error: 'Not implemented',
      message: 'User profile update not yet implemented',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
});

/**
 * DELETE /api/user-profile/:id
 * Delete user profile
 * 
 * Path Parameters:
 * - id: string (required) - User profile ID
 * 
 * Response:
 * - 200: Profile deleted successfully
 * - 404: Profile not found
 * - 500: Server error
 */
router.delete('/:id', async (req, res, next) => {
  try {
    const { id } = req.params;
    
    // In a real application, you would delete from database
    res.status(501).json({
      success: false,
      error: 'Not implemented',
      message: 'User profile deletion not yet implemented',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
});

export { router as userProfileRouter };
