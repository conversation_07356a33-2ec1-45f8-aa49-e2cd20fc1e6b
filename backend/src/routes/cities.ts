import { Router } from 'express';
import { searchCities } from '../services/api.js';
import { validateCityQuery } from '../middleware/validation.js';

const router = Router();

/**
 * GET /api/cities/search
 * Search for cities using Nominatim API
 * 
 * Query Parameters:
 * - q: string (required) - Search query (minimum 2 characters)
 * - limit: number (optional) - Maximum number of results (default: 10, max: 50)
 * 
 * Response:
 * - 200: Array of city search results
 * - 400: Invalid query parameters
 * - 500: Search service error
 */
router.get('/search', validateCityQuery, async (req, res, next) => {
  try {
    const { q: query, limit = '10' } = req.query;
    const searchQuery = query as string;
    const maxResults = Math.min(parseInt(limit as string), 50);

    console.log(`Searching cities for query: "${searchQuery}" (limit: ${maxResults})`);

    const cities = await searchCities(searchQuery);
    
    // Limit results
    const limitedResults = cities.slice(0, maxResults);
    
    res.json({
      success: true,
      data: limitedResults,
      meta: {
        query: searchQuery,
        total: limitedResults.length,
        limit: maxResults
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('City search error:', error);
    next(error);
  }
});

/**
 * GET /api/cities/autocomplete
 * Autocomplete endpoint for city search (alias for search)
 * 
 * Query Parameters:
 * - q: string (required) - Search query
 * - limit: number (optional) - Maximum number of results
 * 
 * Response:
 * - 200: Array of city suggestions
 */
router.get('/autocomplete', validateCityQuery, async (req, res, next) => {
  try {
    const { q: query, limit = '5' } = req.query;
    const searchQuery = query as string;
    const maxResults = Math.min(parseInt(limit as string), 10);

    const cities = await searchCities(searchQuery);
    
    // Format for autocomplete (simplified response)
    const suggestions = cities.slice(0, maxResults).map(city => ({
      id: city.place_id,
      name: city.formattedName,
      display: city.formattedName,
      lat: parseFloat(city.lat),
      lon: parseFloat(city.lon),
      country: city.country
    }));
    
    res.json({
      success: true,
      suggestions,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('City autocomplete error:', error);
    next(error);
  }
});

export { router as cityRouter };
