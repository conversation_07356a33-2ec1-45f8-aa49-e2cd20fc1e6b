import { Router } from 'express';
import { getWeatherData, ApiError } from '../services/api.js';
import { validateCoordinates } from '../middleware/validation.js';

const router = Router();

/**
 * GET /api/weather
 * Get weather data for given coordinates
 * 
 * Query Parameters:
 * - lat: number (required) - Latitude
 * - lon: number (required) - Longitude
 * 
 * Response:
 * - 200: Weather data object
 * - 400: Invalid coordinates
 * - 500: Weather service error
 */
router.get('/', validateCoordinates, async (req, res, next) => {
  try {
    const { lat, lon } = req.query;
    const latitude = parseFloat(lat as string);
    const longitude = parseFloat(lon as string);

    console.log(`Fetching weather for coordinates: ${latitude}, ${longitude}`);

    const weatherData = await getWeatherData(latitude, longitude);
    
    res.json({
      success: true,
      data: weatherData,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Weather API error:', error);
    
    if (error instanceof ApiError) {
      return res.status(error.status || 500).json({
        success: false,
        error: error.message,
        code: error.code,
        timestamp: new Date().toISOString()
      });
    }
    
    next(error);
  }
});

/**
 * GET /api/weather/current/:cityName
 * Get weather data for a city by name
 * 
 * Path Parameters:
 * - cityName: string (required) - Name of the city
 * 
 * Response:
 * - 200: Weather data object
 * - 404: City not found
 * - 500: Weather service error
 */
router.get('/current/:cityName', async (req, res, next) => {
  try {
    const { cityName } = req.params;
    
    // This would require geocoding the city name first
    // For now, return an error suggesting to use coordinates
    res.status(400).json({
      success: false,
      error: 'City name lookup not implemented. Please use coordinates instead.',
      suggestion: 'Use GET /api/weather?lat={latitude}&lon={longitude}',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
});

export { router as weatherRouter };
