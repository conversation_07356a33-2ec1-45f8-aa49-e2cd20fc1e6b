import express from 'express';
import { initializeDatabase } from '../database/connection.js';
import { logger } from '../utils/logger';

const router = express.Router();

// POST /api/outfits - Create a new outfit with clothing items
router.post('/', async (req, res) => {
  const requestId = req.headers['x-request-id'] as string;

  try {
    const {
      userId,
      name,
      description,
      occasion,
      season,
      clothingItemIds,
      notes
    } = req.body;

    // Validate required fields
    if (!userId || !clothingItemIds || !Array.isArray(clothingItemIds) || clothingItemIds.length === 0) {
      return res.status(400).json({
        error: 'Missing required fields: userId, clothingItemIds (must be non-empty array)',
        timestamp: new Date().toISOString()
      });
    }

    const db = await initializeDatabase();

    // Use proper transaction method
    const outfit = await db.transaction(async (client) => {
      // Create the outfit record
      const outfitResult = await client.query(`
        INSERT INTO outfits (
          user_id, name, description, occasion, season, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING *
      `, [
        userId,
        name || `Outfit ${new Date().toLocaleDateString()}`,
        description || notes,
        occasion,
        season || 'all'
      ]);

      const outfit = outfitResult.rows[0];

      // Create outfit_items records for each clothing item
      const outfitItemPromises = clothingItemIds.map(clothingItemId =>
        client.query(`
          INSERT INTO outfit_items (outfit_id, clothing_item_id, created_at)
          VALUES ($1, $2, CURRENT_TIMESTAMP)
          RETURNING *
        `, [outfit.id, clothingItemId])
      );

      await Promise.all(outfitItemPromises);

      return outfit;
    }, requestId);

    logger.info('OUTFIT_CREATE', 'Successfully created outfit', {
      requestId,
      outfitId: outfit.id,
      userId,
      clothingItemCount: clothingItemIds.length
    });

    res.status(201).json({
      success: true,
      data: outfit,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('OUTFIT_CREATE_ERROR', 'Failed to create outfit', {
      requestId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    res.status(500).json({
      error: 'Failed to create outfit',
      timestamp: new Date().toISOString()
    });
  }
});

// GET /api/outfits/:userId - Get all outfits for a user
router.get('/:userId', async (req, res) => {
  const requestId = req.headers['x-request-id'] as string;
  const { userId } = req.params;
  
  try {
    const db = await initializeDatabase();

    const result = await db.query(`
      SELECT 
        o.*,
        COALESCE(
          json_agg(
            json_build_object(
              'id', ci.id,
              'name', ci.name,
              'color', ci.color,
              'brand', ci.brand,
              'image_url', ci.image_url,
              'category_name', cc.name
            )
          ) FILTER (WHERE ci.id IS NOT NULL), 
          '[]'::json
        ) as clothing_items
      FROM outfits o
      LEFT JOIN outfit_items oi ON o.id = oi.outfit_id
      LEFT JOIN clothing_items ci ON oi.clothing_item_id = ci.id
      LEFT JOIN clothing_categories cc ON ci.category_id = cc.id
      WHERE o.user_id = $1
      GROUP BY o.id
      ORDER BY o.created_at DESC
    `, [userId]);

    logger.info('OUTFITS_GET', 'Retrieved outfits', {
      requestId,
      userId,
      count: result.rows.length
    });

    res.json({
      success: true,
      data: result.rows,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('OUTFITS_GET_ERROR', 'Failed to retrieve outfits', {
      requestId,
      userId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    res.status(500).json({
      error: 'Failed to retrieve outfits',
      timestamp: new Date().toISOString()
    });
  }
});

// GET /api/outfits/detail/:outfitId - Get a specific outfit with clothing items
router.get('/detail/:outfitId', async (req, res) => {
  const requestId = req.headers['x-request-id'] as string;
  const { outfitId } = req.params;
  
  try {
    const db = await initializeDatabase();

    const result = await db.query(`
      SELECT 
        o.*,
        COALESCE(
          json_agg(
            json_build_object(
              'id', ci.id,
              'name', ci.name,
              'color', ci.color,
              'brand', ci.brand,
              'image_url', ci.image_url,
              'category_name', cc.name
            )
          ) FILTER (WHERE ci.id IS NOT NULL), 
          '[]'::json
        ) as clothing_items
      FROM outfits o
      LEFT JOIN outfit_items oi ON o.id = oi.outfit_id
      LEFT JOIN clothing_items ci ON oi.clothing_item_id = ci.id
      LEFT JOIN clothing_categories cc ON ci.category_id = cc.id
      WHERE o.id = $1
      GROUP BY o.id
    `, [outfitId]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        error: 'Outfit not found',
        timestamp: new Date().toISOString()
      });
    }

    logger.info('OUTFIT_DETAIL_GET', 'Retrieved outfit detail', {
      requestId,
      outfitId
    });

    res.json({
      success: true,
      data: result.rows[0],
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('OUTFIT_DETAIL_GET_ERROR', 'Failed to retrieve outfit detail', {
      requestId,
      outfitId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    res.status(500).json({
      error: 'Failed to retrieve outfit detail',
      timestamp: new Date().toISOString()
    });
  }
});

// DELETE /api/outfits/:outfitId - Delete an outfit
router.delete('/:outfitId', async (req, res) => {
  const requestId = req.headers['x-request-id'] as string;
  const { outfitId } = req.params;
  
  try {
    const db = await initializeDatabase();

    const result = await db.query(
      'DELETE FROM outfits WHERE id = $1 RETURNING *',
      [outfitId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        error: 'Outfit not found',
        timestamp: new Date().toISOString()
      });
    }

    logger.info('OUTFIT_DELETE', 'Successfully deleted outfit', {
      requestId,
      outfitId
    });

    res.json({
      success: true,
      message: 'Outfit deleted successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('OUTFIT_DELETE_ERROR', 'Failed to delete outfit', {
      requestId,
      outfitId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    res.status(500).json({
      error: 'Failed to delete outfit',
      timestamp: new Date().toISOString()
    });
  }
});

export { router as outfitsRouter };
