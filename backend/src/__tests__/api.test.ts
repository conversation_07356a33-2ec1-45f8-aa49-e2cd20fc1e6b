import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import request from 'supertest';
import app from '../index.js';

describe('API Endpoints', () => {
  describe('Health Check', () => {
    it('should return health status', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      expect(response.body).toMatchObject({
        status: 'healthy',
        timestamp: expect.any(String),
        version: expect.any(String)
      });
    });
  });

  describe('Weather API', () => {
    it('should validate coordinates', async () => {
      const response = await request(app)
        .get('/api/weather')
        .query({ lat: 'invalid', lon: 'invalid' })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Invalid coordinates');
    });

    it('should accept valid coordinates', async () => {
      const response = await request(app)
        .get('/api/weather')
        .query({ lat: 40.7128, lon: -74.0060 });

      // Note: This might fail if API key is not configured
      // In that case, we expect a specific error
      if (response.status === 500) {
        expect(response.body.error).toContain('API key');
      } else {
        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
      }
    });
  });

  describe('City Search API', () => {
    it('should validate search query', async () => {
      const response = await request(app)
        .get('/api/cities/search')
        .query({ q: 'a' }) // Too short
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Invalid search query');
    });

    it('should search cities with valid query', async () => {
      const response = await request(app)
        .get('/api/cities/search')
        .query({ q: 'New York', limit: 5 });

      // This should work as Nominatim doesn't require API key
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
    });

    it('should provide autocomplete suggestions', async () => {
      const response = await request(app)
        .get('/api/cities/autocomplete')
        .query({ q: 'London' });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.suggestions)).toBe(true);
    });
  });

  describe('User Profile API', () => {
    it('should validate user profile data', async () => {
      const invalidProfile = {
        firstName: '', // Invalid: empty
        lastName: 'Doe',
        gender: 'Invalid', // Invalid: not in allowed values
        dateOfBirth: '2030-01-01', // Invalid: future date
        cityName: 'A', // Invalid: too short
        latitude: 91, // Invalid: out of range
        longitude: -181 // Invalid: out of range
      };

      const response = await request(app)
        .post('/api/user-profile')
        .send(invalidProfile)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Invalid user profile data');
      expect(Array.isArray(response.body.details)).toBe(true);
    });

    it('should create user profile with valid data', async () => {
      const validProfile = {
        firstName: 'John',
        lastName: 'Doe',
        gender: 'Male',
        dateOfBirth: '1990-01-01',
        cityName: 'New York, NY, United States',
        latitude: 40.7127753,
        longitude: -74.0059728,
        weatherPreferences: {
          temperatureUnit: 'celsius',
          showHumidity: true
        }
      };

      const response = await request(app)
        .post('/api/user-profile')
        .send(validProfile)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toMatchObject({
        id: expect.any(String),
        firstName: 'John',
        lastName: 'Doe',
        gender: 'Male',
        createdAt: expect.any(String),
        updatedAt: expect.any(String)
      });
    });
  });

  describe('File Upload API', () => {
    it('should reject request without file', async () => {
      const response = await request(app)
        .post('/api/upload/image')
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('No file uploaded');
    });

    // Note: Testing actual file upload would require creating test files
    // This is a basic validation test
  });

  describe('Rate Limiting', () => {
    it('should apply rate limiting', async () => {
      // Make multiple requests quickly to test rate limiting
      const requests = Array(5).fill(null).map(() => 
        request(app).get('/health')
      );

      const responses = await Promise.all(requests);
      
      // All should succeed for health endpoint (high limit)
      responses.forEach(response => {
        expect(response.status).toBe(200);
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle 404 for unknown routes', async () => {
      const response = await request(app)
        .get('/api/unknown-endpoint')
        .expect(404);

      expect(response.body.error).toBe('Not Found');
    });

    it('should handle invalid JSON', async () => {
      const response = await request(app)
        .post('/api/user-profile')
        .set('Content-Type', 'application/json')
        .send('invalid json')
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Invalid JSON in request body');
    });
  });
});
