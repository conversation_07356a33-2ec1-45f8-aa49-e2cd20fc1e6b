import { describe, it, expect, beforeEach, afterEach, vi, Mock } from 'vitest';
import { AuthService } from '../services/authService.js';
import { DatabaseConnection } from '../database/connection.js';
import { databaseLogger } from '../utils/logger.js';

// Mock the logger
vi.mock('../utils/logger.js');

describe('Email Validation Backend Tests', () => {
  let authService: AuthService;
  let mockDb: Partial<DatabaseConnection>;
  let databaseLoggerDebugSpy: Mock;
  let databaseLoggerWarnSpy: Mock;
  let databaseLoggerErrorSpy: Mock;

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();

    // Mock database connection
    mockDb = {
      query: vi.fn(),
    };

    // Mock logger functions
    databaseLoggerDebugSpy = vi.fn();
    databaseLoggerWarnSpy = vi.fn();
    databaseLoggerErrorSpy = vi.fn();

    // Setup logger mocks
    (databaseLogger.debug as Mock) = databaseLoggerDebugSpy;
    (databaseLogger.warn as Mock) = databaseLoggerWarnSpy;
    (databaseLogger.error as Mock) = databaseLoggerErrorSpy;

    // Create AuthService instance with mocked database
    authService = new AuthService(mockDb as DatabaseConnection);
    
    // Mock the getUserByEmail method to control its behavior
    vi.spyOn(authService, 'getUserByEmail');
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('validateEmailAvailability method', () => {
    describe('Email format validation', () => {
      it('should return error for invalid email format', async () => {
        const result = await authService.validateEmailAvailability('invalid-email');

        expect(result).toEqual({
          available: false,
          message: 'Invalid email format'
        });

        expect(databaseLoggerDebugSpy).toHaveBeenCalledWith(
          'Email format validation failed',
          { email: 'invalid-email' },
          undefined
        );

        // Should not call database for invalid format
        expect(mockDb.query).not.toHaveBeenCalled();
      });

      it('should accept valid email formats', async () => {
        const validEmails = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>'
        ];

        // Mock getUserByEmail to return no existing user
        (authService.getUserByEmail as Mock).mockResolvedValue(null);

        for (const email of validEmails) {
          const result = await authService.validateEmailAvailability(email);
          
          expect(result).toEqual({
            available: true,
            message: 'Email is available'
          });

          expect(databaseLoggerDebugSpy).toHaveBeenCalledWith(
            'Validating email availability',
            { email },
            undefined
          );
        }
      });
    });

    describe('Email existence checking', () => {
      it('should return available=true for non-existent email', async () => {
        // Mock getUserByEmail to return no existing user
        (authService.getUserByEmail as Mock).mockResolvedValue(null);

        const result = await authService.validateEmailAvailability('<EMAIL>');

        expect(result).toEqual({
          available: true,
          message: 'Email is available'
        });

        expect(authService.getUserByEmail).toHaveBeenCalledWith('<EMAIL>', undefined);

        expect(databaseLoggerDebugSpy).toHaveBeenCalledWith(
          'Email is available for registration',
          { email: '<EMAIL>' },
          undefined
        );
      });

      it('should return available=false for existing email', async () => {
        // Mock getUserByEmail to return existing user
        const existingUser = {
          id: '123',
          email: '<EMAIL>',
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
          status: 'active'
        };
        (authService.getUserByEmail as Mock).mockResolvedValue(existingUser);

        const result = await authService.validateEmailAvailability('<EMAIL>');

        expect(result).toEqual({
          available: false,
          message: 'Email is already registered'
        });

        expect(databaseLoggerDebugSpy).toHaveBeenCalledWith(
          'Email already exists in database',
          { email: '<EMAIL>' },
          undefined
        );
      });
    });

    describe('Database error handling', () => {
      it('should handle database connection errors gracefully', async () => {
        // Mock getUserByEmail to throw connection error
        const connectionError = new Error('connection refused');
        connectionError.message = 'ECONNREFUSED: connection refused';
        (authService.getUserByEmail as Mock).mockRejectedValue(connectionError);

        const result = await authService.validateEmailAvailability('<EMAIL>');

        expect(result).toEqual({
          available: true,
          message: 'Database temporarily unavailable. You may proceed with signup.'
        });

        expect(databaseLoggerErrorSpy).toHaveBeenCalledWith(
          'Database connection error during email validation',
          {
            email: '<EMAIL>',
            error: 'ECONNREFUSED: connection refused'
          },
          connectionError,
          undefined
        );
      });

      it('should handle database timeout errors', async () => {
        // Mock getUserByEmail to throw timeout error
        const timeoutError = new Error('Database query timeout');
        (authService.getUserByEmail as Mock).mockRejectedValue(timeoutError);

        const result = await authService.validateEmailAvailability('<EMAIL>');

        expect(result).toEqual({
          available: true,
          message: 'Validation timed out. You may proceed with signup.'
        });

        expect(databaseLoggerWarnSpy).toHaveBeenCalledWith(
          'Email validation timed out at database level',
          { email: '<EMAIL>' },
          undefined
        );
      });

      it('should handle generic database errors', async () => {
        // Mock getUserByEmail to throw generic error
        const genericError = new Error('Database error');
        (authService.getUserByEmail as Mock).mockRejectedValue(genericError);

        const result = await authService.validateEmailAvailability('<EMAIL>');

        expect(result).toEqual({
          available: true,
          message: 'Unable to verify email availability. You may proceed with signup.'
        });

        expect(databaseLoggerErrorSpy).toHaveBeenCalledWith(
          'Email availability validation failed',
          {
            email: '<EMAIL>',
            error: 'Database error'
          },
          genericError,
          undefined
        );
      });

      it('should handle non-Error exceptions', async () => {
        // Mock getUserByEmail to throw non-Error object
        (authService.getUserByEmail as Mock).mockRejectedValue('String error');

        const result = await authService.validateEmailAvailability('<EMAIL>');

        expect(result).toEqual({
          available: true,
          message: 'Unable to verify email availability. You may proceed with signup.'
        });

        expect(databaseLoggerErrorSpy).toHaveBeenCalledWith(
          'Email availability validation failed',
          {
            email: '<EMAIL>',
            error: 'Unknown error'
          },
          undefined,
          undefined
        );
      });
    });

    describe('Request ID handling', () => {
      it('should pass request ID through to logging', async () => {
        (authService.getUserByEmail as Mock).mockResolvedValue(null);

        const requestId = 'test-request-123';
        const result = await authService.validateEmailAvailability('<EMAIL>', requestId);

        expect(result).toEqual({
          available: true,
          message: 'Email is available'
        });

        expect(databaseLoggerDebugSpy).toHaveBeenCalledWith(
          'Validating email availability',
          { email: '<EMAIL>' },
          requestId
        );
      });
    });

    describe('Response format validation', () => {
      it('should always return consistent response structure', async () => {
        (authService.getUserByEmail as Mock).mockResolvedValue(null);

        const result = await authService.validateEmailAvailability('<EMAIL>');

        // Verify response has all required fields
        expect(result).toHaveProperty('available');
        expect(result).toHaveProperty('message');
        
        expect(typeof result.available).toBe('boolean');
        expect(typeof result.message).toBe('string');
      });

      it('should return consistent response structure for errors', async () => {
        const result = await authService.validateEmailAvailability('invalid-email');

        // Verify error response has all required fields
        expect(result).toHaveProperty('available');
        expect(result).toHaveProperty('message');
        
        expect(result.available).toBe(false);
        expect(typeof result.message).toBe('string');
      });
    });

    describe('Edge cases', () => {
      it('should handle empty string email', async () => {
        const result = await authService.validateEmailAvailability('');

        expect(result).toEqual({
          available: false,
          message: 'Invalid email format'
        });

        // Should not call database for empty email
        expect(mockDb.query).not.toHaveBeenCalled();
      });

      it('should handle whitespace-only email', async () => {
        const result = await authService.validateEmailAvailability('   ');

        // Should be treated as invalid format after trimming
        expect(result).toEqual({
          available: false,
          message: 'Invalid email format'
        });

        // Should not call database for whitespace-only email
        expect(mockDb.query).not.toHaveBeenCalled();
      });

      it('should handle very long email addresses', async () => {
        const longEmail = 'a'.repeat(250) + '@example.com';
        (authService.getUserByEmail as Mock).mockResolvedValue(null);

        const result = await authService.validateEmailAvailability(longEmail);

        expect(result.available).toBe(true);
        expect(result.message).toBe('Email is available');
      });

      it('should handle special characters in email', async () => {
        const specialEmail = '<EMAIL>';
        (authService.getUserByEmail as Mock).mockResolvedValue(null);

        const result = await authService.validateEmailAvailability(specialEmail);

        expect(result).toEqual({
          available: true,
          message: 'Email is available'
        });
      });

      it('should handle timeout mechanism', async () => {
        // Create a promise that resolves after 4 seconds (longer than 3s timeout)
        const slowQuery = new Promise((resolve) => {
          setTimeout(() => resolve(null), 4000);
        });
        
        (authService.getUserByEmail as Mock).mockReturnValue(slowQuery);

        const startTime = Date.now();
        const result = await authService.validateEmailAvailability('<EMAIL>');
        const endTime = Date.now();

        // Should timeout and return within reasonable time (not wait 4 seconds)
        expect(endTime - startTime).toBeLessThan(3500);
        
        expect(result).toEqual({
          available: true,
          message: 'Validation timed out. You may proceed with signup.'
        });
      });
    });
  });
});