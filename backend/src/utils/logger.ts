// Backend Logger Utility
import { Request, Response } from 'express';

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

interface LogEntry {
  timestamp: string;
  level: LogLevel;
  category: string;
  message: string;
  data?: any;
  error?: Error;
  requestId?: string;
}

class BackendLogger {
  private logLevel: LogLevel;
  private logs: LogEntry[] = [];
  private maxLogs = 10000;

  constructor() {
    this.logLevel = process.env.NODE_ENV === 'development' ? LogLevel.DEBUG : LogLevel.INFO;
  }

  private formatTimestamp(): string {
    return new Date().toISOString();
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.logLevel;
  }

  private getLogLevelName(level: LogLevel): string {
    switch (level) {
      case LogLevel.DEBUG: return 'DEBUG';
      case LogLevel.INFO: return 'INFO';
      case LogLevel.WARN: return 'WARN';
      case LogLevel.ERROR: return 'ERROR';
      default: return 'UNKNOWN';
    }
  }

  private getLogColor(level: LogLevel): string {
    switch (level) {
      case LogLevel.DEBUG: return '\x1b[36m'; // Cyan
      case LogLevel.INFO: return '\x1b[32m';  // Green
      case LogLevel.WARN: return '\x1b[33m';  // Yellow
      case LogLevel.ERROR: return '\x1b[31m'; // Red
      default: return '\x1b[0m';
    }
  }

  private formatConsoleMessage(entry: LogEntry): string {
    const color = this.getLogColor(entry.level);
    const reset = '\x1b[0m';
    const requestId = entry.requestId ? ` [${entry.requestId}]` : '';
    
    return `${color}[${entry.timestamp}] [${this.getLogLevelName(entry.level)}] [${entry.category}]${requestId} ${entry.message}${reset}`;
  }

  private createLogEntry(
    level: LogLevel, 
    category: string, 
    message: string, 
    data?: any, 
    error?: Error,
    requestId?: string
  ): LogEntry {
    return {
      timestamp: this.formatTimestamp(),
      level,
      category,
      message,
      data,
      error,
      requestId,
    };
  }

  private addToHistory(entry: LogEntry): void {
    this.logs.push(entry);
    if (this.logs.length > this.maxLogs) {
      this.logs.shift();
    }
  }

  private logToConsole(entry: LogEntry): void {
    const message = this.formatConsoleMessage(entry);
    
    if (entry.data) {
      console.log(message);
      console.log('  Data:', JSON.stringify(entry.data, null, 2));
    } else {
      console.log(message);
    }
    
    if (entry.error) {
      console.log('  Error:', entry.error.message);
      if (entry.error.stack) {
        console.log('  Stack:', entry.error.stack);
      }
    }
  }

  debug(category: string, message: string, data?: any, requestId?: string): void {
    if (!this.shouldLog(LogLevel.DEBUG)) return;
    const entry = this.createLogEntry(LogLevel.DEBUG, category, message, data, undefined, requestId);
    this.addToHistory(entry);
    this.logToConsole(entry);
  }

  info(category: string, message: string, data?: any, requestId?: string): void {
    if (!this.shouldLog(LogLevel.INFO)) return;
    const entry = this.createLogEntry(LogLevel.INFO, category, message, data, undefined, requestId);
    this.addToHistory(entry);
    this.logToConsole(entry);
  }

  warn(category: string, message: string, data?: any, error?: Error, requestId?: string): void {
    if (!this.shouldLog(LogLevel.WARN)) return;
    const entry = this.createLogEntry(LogLevel.WARN, category, message, data, error, requestId);
    this.addToHistory(entry);
    this.logToConsole(entry);
  }

  error(category: string, message: string, data?: any, error?: Error, requestId?: string): void {
    if (!this.shouldLog(LogLevel.ERROR)) return;
    const entry = this.createLogEntry(LogLevel.ERROR, category, message, data, error, requestId);
    this.addToHistory(entry);
    this.logToConsole(entry);
  }

  // Request logging helper
  logRequest(req: Request, res: Response, duration?: number): void {
    const requestId = req.headers['x-request-id'] as string || Math.random().toString(36).substr(2, 9);
    
    this.info('HTTP', `${req.method} ${req.path}`, {
      method: req.method,
      path: req.path,
      query: req.query,
      body: req.method !== 'GET' ? req.body : undefined,
      headers: {
        'user-agent': req.headers['user-agent'],
        'content-type': req.headers['content-type'],
        'authorization': req.headers.authorization ? '[REDACTED]' : undefined,
      },
      ip: req.ip,
      duration: duration ? `${duration}ms` : undefined,
      statusCode: res.statusCode,
    }, requestId);
  }

  // Database operation logging
  logDatabaseOperation(operation: string, table: string, data?: any, error?: Error, requestId?: string): void {
    if (error) {
      this.error('DATABASE', `${operation} failed on ${table}`, data, error, requestId);
    } else {
      this.debug('DATABASE', `${operation} on ${table}`, data, requestId);
    }
  }

  // Get log history
  getHistory(): LogEntry[] {
    return [...this.logs];
  }

  // Export logs
  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2);
  }

  // Clear history
  clearHistory(): void {
    this.logs = [];
  }
}

// Create singleton instance
export const logger = new BackendLogger();

// Convenience functions for common categories
export const apiLogger = {
  debug: (message: string, data?: any, requestId?: string) => logger.debug('API', message, data, requestId),
  info: (message: string, data?: any, requestId?: string) => logger.info('API', message, data, requestId),
  warn: (message: string, data?: any, error?: Error, requestId?: string) => logger.warn('API', message, data, error, requestId),
  error: (message: string, data?: any, error?: Error, requestId?: string) => logger.error('API', message, data, error, requestId),
};

export const databaseLogger = {
  debug: (message: string, data?: any, requestId?: string) => logger.debug('DATABASE', message, data, requestId),
  info: (message: string, data?: any, requestId?: string) => logger.info('DATABASE', message, data, requestId),
  warn: (message: string, data?: any, error?: Error, requestId?: string) => logger.warn('DATABASE', message, data, error, requestId),
  error: (message: string, data?: any, error?: Error, requestId?: string) => logger.error('DATABASE', message, data, error, requestId),
};

export const weatherLogger = {
  debug: (message: string, data?: any, requestId?: string) => logger.debug('WEATHER', message, data, requestId),
  info: (message: string, data?: any, requestId?: string) => logger.info('WEATHER', message, data, requestId),
  warn: (message: string, data?: any, error?: Error, requestId?: string) => logger.warn('WEATHER', message, data, error, requestId),
  error: (message: string, data?: any, error?: Error, requestId?: string) => logger.error('WEATHER', message, data, error, requestId),
};

// Request ID middleware
export const requestIdMiddleware = (req: Request, res: Response, next: Function) => {
  const requestId = req.headers['x-request-id'] as string || Math.random().toString(36).substr(2, 9);
  req.headers['x-request-id'] = requestId;
  res.setHeader('x-request-id', requestId);
  next();
};

// Request logging middleware
export const requestLoggingMiddleware = (req: Request, res: Response, next: Function) => {
  const startTime = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    logger.logRequest(req, res, duration);
  });
  
  next();
};
