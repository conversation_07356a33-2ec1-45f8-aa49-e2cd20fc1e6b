#!/bin/bash

# DEPRECATED: Setup script for Python clothing analysis environment
# This script is no longer needed as the application now uses external APIs for:
# - Color extraction: http://**************:7563/api/color
# - Cloth detection: http://**************:9743/api/analyze

set -e  # Exit on any error

echo "⚠️  DEPRECATED: This Python setup script is no longer needed."
echo "The application now uses external APIs for clothing analysis."
echo "Exiting without making changes."
exit 0

# Legacy code below (no longer executed)
echo "🐍 Setting up Python environment for clothing analysis..."

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.8 or higher."
    exit 1
fi

# Check Python version
PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
REQUIRED_VERSION="3.8"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$PYTHON_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    echo "❌ Python $PYTHON_VERSION is installed, but Python $REQUIRED_VERSION or higher is required."
    exit 1
fi

echo "✅ Python $PYTHON_VERSION detected"

# Navigate to the python directory
cd "$(dirname "$0")/python"

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "📦 Creating Python virtual environment..."
    python3 -m venv venv
else
    echo "✅ Virtual environment already exists"
fi

# Activate virtual environment
echo "🔄 Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
echo "⬆️ Upgrading pip..."
pip install --upgrade pip

# Install requirements
echo "📥 Installing Python dependencies..."
pip install -r requirements.txt

echo "✅ Python environment setup complete!"
echo ""
echo "To activate the environment manually, run:"
echo "  cd backend/python"
echo "  source venv/bin/activate"
echo ""
echo "To test the clothing analyzer, run:"
echo "  python clothing_analyzer.py <image_path> <output_dir>"
echo ""
echo "🎉 Setup complete! The clothing analysis system is ready to use."
