// Simple test script for email validation endpoint
import http from 'http';

function testEmailValidation(email, expectedAvailable) {
  const postData = JSON.stringify({ email });
  
  const options = {
    hostname: 'localhost',
    port: 3001,
    path: '/api/auth/validate-email',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData),
      'x-request-id': 'test-' + Date.now()
    }
  };

  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          console.log(`Test: ${email}`);
          console.log(`Expected available: ${expectedAvailable}, Got: ${response.available}`);
          console.log(`Message: ${response.message}`);
          console.log(`Status: ${res.statusCode}`);
          console.log('---');
          resolve(response);
        } catch (error) {
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.write(postData);
    req.end();
  });
}

async function runTests() {
  console.log('Testing email validation endpoint...\n');
  
  try {
    // Test invalid email format
    await testEmailValidation('invalid-email', false);
    
    // Test valid email format (should be available)
    await testEmailValidation('<EMAIL>', true);
    
    // Test empty email
    await testEmailValidation('', false);
    
    // Test null email
    await testEmailValidation(null, false);
    
  } catch (error) {
    console.error('Test failed:', error.message);
  }
}

// Wait a moment for server to start, then run tests
setTimeout(runTests, 2000);