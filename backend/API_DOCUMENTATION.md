# Closet Glass Chic API Documentation

## Overview

The Closet Glass Chic API provides backend services for the fashion wardrobe management application. It includes endpoints for weather data, city search, user profile management, and file uploads.

**Base URL:** `http://localhost:3001/api`

## Authentication

Currently, the API does not require authentication. This will be added in future versions.

## Rate Limiting

- **General endpoints:** 100 requests per minute per IP
- **API endpoints:** 60 requests per minute per IP  
- **Upload endpoints:** 10 requests per minute per IP

When rate limit is exceeded, the API returns a `429 Too Many Requests` status with a `Retry-After` header.

## Error Handling

All API responses follow a consistent format:

### Success Response
```json
{
  "success": true,
  "data": { ... },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Error Response
```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Endpoints

### Health Check

#### GET /health
Check API health status.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "version": "1.0.0"
}
```

---

## Weather API

### GET /api/weather

Get weather data for given coordinates using OpenWeatherMap API.

**Query Parameters:**
- `lat` (number, required): Latitude (-90 to 90)
- `lon` (number, required): Longitude (-180 to 180)

**Example Request:**
```
GET /api/weather?lat=40.7128&lon=-74.0060
```

**Success Response (200):**
```json
{
  "success": true,
  "data": {
    "coord": { "lon": -74.006, "lat": 40.7128 },
    "weather": [
      {
        "id": 800,
        "main": "Clear",
        "description": "clear sky",
        "icon": "01d"
      }
    ],
    "main": {
      "temp": 22.5,
      "feels_like": 23.1,
      "temp_min": 20.0,
      "temp_max": 25.0,
      "pressure": 1013,
      "humidity": 65
    },
    "name": "New York",
    "sys": { "country": "US" }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**Error Responses:**
- `400`: Invalid coordinates
- `401`: Invalid API key
- `404`: Location not found
- `500`: Weather service error

---

## City Search API

### GET /api/cities/search

Search for cities using Nominatim API (OpenStreetMap).

**Query Parameters:**
- `q` (string, required): Search query (minimum 2 characters)
- `limit` (number, optional): Maximum results (default: 10, max: 50)

**Example Request:**
```
GET /api/cities/search?q=New York&limit=5
```

**Success Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "place_id": 123456,
      "lat": "40.7127753",
      "lon": "-74.0059728",
      "display_name": "New York, NY, United States",
      "formattedName": "New York, NY, United States",
      "city": "New York",
      "state": "NY",
      "country": "United States"
    }
  ],
  "meta": {
    "query": "New York",
    "total": 1,
    "limit": 5
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### GET /api/cities/autocomplete

Simplified autocomplete endpoint for city search.

**Query Parameters:**
- `q` (string, required): Search query
- `limit` (number, optional): Maximum results (default: 5, max: 10)

**Success Response (200):**
```json
{
  "success": true,
  "suggestions": [
    {
      "id": 123456,
      "name": "New York, NY, United States",
      "display": "New York, NY, United States",
      "lat": 40.7127753,
      "lon": -74.0059728,
      "country": "United States"
    }
  ],
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

---

## User Profile API

### POST /api/user-profile

Create a new user profile.

**Request Body:**
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "gender": "Male",
  "dateOfBirth": "1990-01-01",
  "cityName": "New York, NY, United States",
  "latitude": 40.7127753,
  "longitude": -74.0059728,
  "weatherPreferences": {
    "temperatureUnit": "celsius",
    "showHumidity": true,
    "showWindSpeed": false
  }
}
```

**Success Response (201):**
```json
{
  "success": true,
  "data": {
    "id": "user_1234567890",
    "firstName": "John",
    "lastName": "Doe",
    "gender": "Male",
    "dateOfBirth": "1990-01-01",
    "cityName": "New York, NY, United States",
    "latitude": 40.7127753,
    "longitude": -74.0059728,
    "weatherPreferences": { ... },
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  },
  "message": "User profile created successfully",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**Validation Rules:**
- `firstName`: 1-50 characters, required
- `lastName`: 1-50 characters, required
- `gender`: Must be one of: "Male", "Female", "Other", "Prefer not to say"
- `dateOfBirth`: Valid date, not in the future
- `cityName`: 2-100 characters, required
- `latitude`: -90 to 90, required
- `longitude`: -180 to 180, required

### GET /api/user-profile/:id
### PUT /api/user-profile/:id  
### DELETE /api/user-profile/:id

*Note: These endpoints return 501 Not Implemented currently.*

---

## File Upload API

### POST /api/upload/image

Upload and process an image file for clothing items.

**Request:** `multipart/form-data`
- `file` (File, required): Image file (JPEG, PNG, WebP, HEIC)
- `category` (string, optional): Clothing category
- `name` (string, optional): Item name

**File Constraints:**
- Maximum size: 5MB
- Allowed types: JPEG, PNG, WebP, HEIC
- Images are automatically resized to max 800x800px
- Output format: WebP with 80% quality

**Success Response (200):**
```json
{
  "success": true,
  "data": {
    "id": "img_1234567890",
    "filename": "clothing_1234567890.webp",
    "originalName": "shirt.jpg",
    "mimetype": "image/webp",
    "size": 45678,
    "url": "/uploads/clothing_1234567890.webp",
    "metadata": {
      "width": 800,
      "height": 600,
      "format": "webp"
    },
    "category": "tops",
    "name": "Blue Shirt",
    "uploadedAt": "2024-01-01T00:00:00.000Z"
  },
  "message": "File uploaded and processed successfully",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### GET /api/upload/images
### DELETE /api/upload/image/:id

*Note: These endpoints return 501 Not Implemented currently.*

---

## Error Codes

| Code | Description |
|------|-------------|
| `INVALID_COORDINATES` | Latitude or longitude out of valid range |
| `INVALID_QUERY` | Search query too short or invalid |
| `WEATHER_API_ERROR` | OpenWeatherMap API error |
| `CITY_SEARCH_ERROR` | Nominatim API error |
| `VALIDATION_ERROR` | Request validation failed |
| `FILE_TOO_LARGE` | Uploaded file exceeds size limit |
| `INVALID_FILE_TYPE` | Unsupported file format |
| `RATE_LIMIT_EXCEEDED` | Too many requests |

## Environment Variables

Required environment variables for the backend:

```bash
# Server Configuration
PORT=3001
NODE_ENV=development
FRONTEND_URL=http://localhost:5173

# API Keys
VITE_OPENWEATHER_API_KEY=your_openweather_api_key
VITE_OPENWEATHER_API_URL=https://api.openweathermap.org/data/2.5
VITE_NOMINATIM_API_URL=https://nominatim.openstreetmap.org

# Rate Limiting
VITE_API_RATE_LIMIT_REQUESTS=60
VITE_API_RATE_LIMIT_WINDOW=60000
VITE_API_CACHE_DURATION=300000
```

## Testing

### Manual Testing with curl

Test the health endpoint:
```bash
curl http://localhost:3001/health
```

Test weather API:
```bash
curl "http://localhost:3001/api/weather?lat=40.7128&lon=-74.0060"
```

Test city search:
```bash
curl "http://localhost:3001/api/cities/search?q=New%20York&limit=5"
```

Test user profile creation:
```bash
curl -X POST http://localhost:3001/api/user-profile \
  -H "Content-Type: application/json" \
  -d '{
    "firstName": "John",
    "lastName": "Doe",
    "gender": "Male",
    "dateOfBirth": "1990-01-01",
    "cityName": "New York, NY, United States",
    "latitude": 40.7127753,
    "longitude": -74.0059728
  }'
```

Test file upload:
```bash
curl -X POST http://localhost:3001/api/upload/image \
  -F "file=@/path/to/image.jpg" \
  -F "category=tops" \
  -F "name=Blue Shirt"
```

### Automated Testing

Use the provided test suite to verify API functionality:

```bash
cd backend
npm test
```

## Development

Start the development server:

```bash
cd backend
npm run dev
```

The API will be available at `http://localhost:3001`

## Production Deployment

Build and start the production server:

```bash
cd backend
npm run build
npm start
```
