import type { Config } from "tailwindcss";
import tailwindcssAnimate from 'tailwindcss-animate';

export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'2xl': '1400px'
			}
		},
		extend: {
			colors: {
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))'
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
				sidebar: {
					DEFAULT: 'hsl(var(--sidebar-background))',
					foreground: 'hsl(var(--sidebar-foreground))',
					primary: 'hsl(var(--sidebar-primary))',
					'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
					accent: 'hsl(var(--sidebar-accent))',
					'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
					border: 'hsl(var(--sidebar-border))',
					ring: 'hsl(var(--sidebar-ring))'
				},
				zara: {
					white: 'hsl(var(--zara-white))',
					black: 'hsl(var(--zara-black))',
					charcoal: 'hsl(var(--zara-charcoal))',
					'light-gray': 'hsl(var(--zara-light-gray))',
					'medium-gray': 'hsl(var(--zara-medium-gray))',
					'dark-gray': 'hsl(var(--zara-dark-gray))'
				}
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			keyframes: {
				'accordion-down': {
					from: {
						height: '0'
					},
					to: {
						height: 'var(--radix-accordion-content-height)'
					}
				},
				'accordion-up': {
					from: {
						height: 'var(--radix-accordion-content-height)'
					},
					to: {
						height: '0'
					}
				},
				'glass-fade-in': {
					from: {
						opacity: '0',
						backdropFilter: 'blur(0)'
					},
					to: {
						opacity: '1',
						backdropFilter: 'blur(16px)'
					}
				},
				'glass-scale-in': {
					from: {
						opacity: '0',
						transform: 'scale(0.95)',
						backdropFilter: 'blur(0)'
					},
					to: {
						opacity: '1',
						transform: 'scale(1)',
						backdropFilter: 'blur(16px)'
					}
				},
				'glass-slide-up': {
					from: {
						opacity: '0',
						transform: 'translateY(20px)',
						backdropFilter: 'blur(0)'
					},
					to: {
						opacity: '1',
						transform: 'translateY(0)',
						backdropFilter: 'blur(16px)'
					}
				},
				'liquid-morph': {
					'0%': {
						backdropFilter: 'blur(8px)',
						background: 'rgba(255, 255, 255, 0.1)'
					},
					'50%': {
						backdropFilter: 'blur(32px)',
						background: 'rgba(255, 255, 255, 0.4)',
						transform: 'scale(1.05)'
					},
					'100%': {
						backdropFilter: 'blur(16px)',
						background: 'rgba(255, 255, 255, 0.2)',
						transform: 'scale(1)'
					}
				}
			},
			animation: {
				'accordion-down': 'accordion-down 0.2s ease-out',
				'accordion-up': 'accordion-up 0.2s ease-out',
				'fade-in': 'fade-in 0.3s ease-out',
				'scale-in': 'scale-in 0.2s ease-out',
				'glass-fade-in': 'glass-fade-in 0.5s cubic-bezier(0.4, 0, 0.2, 1)',
				'glass-scale-in': 'glass-scale-in 0.3s cubic-bezier(0.23, 1, 0.32, 1)',
				'glass-slide-up': 'glass-slide-up 0.5s cubic-bezier(0.16, 1, 0.3, 1)',
				'liquid-morph': 'liquid-morph 1.2s cubic-bezier(0.16, 1, 0.3, 1)'
			},
			fontFamily: {
				'sans': ['-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif']
			},
			spacing: {
				'xs': 'var(--space-xs)',
				'sm': 'var(--space-sm)',
				'md': 'var(--space-md)',
				'lg': 'var(--space-lg)',
				'xl': 'var(--space-xl)',
				'2xl': 'var(--space-2xl)',
				'3xl': 'var(--space-3xl)'
			},
			transitionTimingFunction: {
				'glass': 'var(--ease-glass)',
				'bounce': 'var(--ease-bounce)',
				'smooth': 'var(--ease-smooth)',
				'premium': 'var(--ease-premium)',
				'liquid': 'var(--ease-liquid)'
			},
			transitionDuration: {
				'instant': 'var(--timing-instant)',
				'fast': 'var(--timing-fast)',
				'normal': 'var(--timing-normal)',
				'slow': 'var(--timing-slow)',
				'extra-slow': 'var(--timing-extra-slow)',
				'luxurious': 'var(--timing-luxurious)'
			}
		}
	},
	plugins: [tailwindcssAnimate],
} satisfies Config;
