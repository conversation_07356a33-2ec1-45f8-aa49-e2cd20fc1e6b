services:
  # PostgreSQL Database
  database:
    image: postgres:15-alpine
    container_name: closet-database
    environment:
      - POSTGRES_DB=closet_glass_chic
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=closet_password_2024
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/src/database/init:/docker-entrypoint-initdb.d
    ports:
      - "5433:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d closet_glass_chic"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - closet-network

  # Backend API
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
      target: development
    container_name: closet-backend
    ports:
      - "${BACKEND_PORT:-3001}:3001"
    volumes:
      - ./backend:/app
      - /app/node_modules
      - uploads:/app/uploads
    user: "1001:1001"
    environment:
      - NODE_ENV=development
      - PORT=3001
      - FRONTEND_URL=http://${BASE_URL:-**************}:${FRONTEND_PORT:-5173}
      - DB_HOST=database
      - DB_PORT=5432
      - DB_NAME=closet_glass_chic
      - DB_USER=postgres
      - DB_PASSWORD=closet_password_2024
      - DB_SSL=false
    env_file:
      - .env
    depends_on:
      database:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - closet-network

  # Frontend Application
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
      target: development
    container_name: closet-frontend
    ports:
      - "${FRONTEND_PORT:-5173}:8080"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - VITE_HOST=0.0.0.0
      - VITE_PORT=8080
      - VITE_BASE_URL=${BASE_URL:-**************}
      - VITE_FRONTEND_PORT=${FRONTEND_PORT:-5173}
      - VITE_BACKEND_PORT=${BACKEND_PORT:-3001}
    env_file:
      - .env
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - closet-network

volumes:
  uploads:
    driver: local
    driver_opts:
      type: none
      device: ${PWD}/backend/uploads
      o: bind
  postgres_data:
    driver: local

networks:
  closet-network:
    driver: bridge
