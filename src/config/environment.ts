// Environment configuration utility
// Centralizes all environment variable access and provides type safety

export interface EnvironmentConfig {
  // Application
  appName: string;
  appVersion: string;
  nodeEnv: string;

  // Network Configuration
  baseUrl: string;
  frontendPort: number;
  backendPort: number;

  // Computed URLs (derived from base URL and ports)
  apiBaseUrl: string;
  backendBaseUrl: string;
  frontendUrl: string;

  // API Configuration
  apiTimeout: number;

  // Development Server
  host: string;
  
  // External APIs
  openWeatherApiKey: string;
  openWeatherApiUrl: string;
  nominatimApiUrl: string;
  
  // Rate Limiting
  apiRateLimitRequests: number;
  apiRateLimitWindow: number;
  apiCacheDuration: number;
  
  // Database
  dbName: string;
  dbVersion: number;
  
  // File Upload
  maxFileSize: number;
  allowedFileTypes: string[];
  maxImageWidth: number;
  maxImageHeight: number;
  
  // Feature Flags
  enableAnalytics: boolean;
  enableDebug: boolean;
  enableWeatherIntegration: boolean;
  enableCityAutocomplete: boolean;
}

/**
 * Parse environment variable as boolean
 */
function parseBoolean(value: string | undefined, defaultValue: boolean = false): boolean {
  if (!value) return defaultValue;
  return value.toLowerCase() === 'true' || value === '1';
}

/**
 * Parse environment variable as number
 */
function parseNumber(value: string | undefined, defaultValue: number): number {
  if (!value) return defaultValue;
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? defaultValue : parsed;
}

/**
 * Parse environment variable as string array
 */
function parseStringArray(value: string | undefined, defaultValue: string[] = []): string[] {
  if (!value) return defaultValue;
  return value.split(',').map(item => item.trim()).filter(Boolean);
}

/**
 * Get environment configuration
 */
export function getEnvironmentConfig(): EnvironmentConfig {
  // Get base configuration (using VITE_ prefix for frontend access)
  const baseUrl = import.meta.env.VITE_BASE_URL || '192.168.29.105';
  const frontendPort = parseNumber(import.meta.env.VITE_FRONTEND_PORT, 5173);
  const backendPort = parseNumber(import.meta.env.VITE_BACKEND_PORT, 3001);

  // Compute URLs from base URL and ports
  const backendBaseUrl = `http://${baseUrl}:${backendPort}`;
  const apiBaseUrl = `${backendBaseUrl}/api`;
  const frontendUrl = `http://${baseUrl}:${frontendPort}`;

  return {
    // Application
    appName: import.meta.env.VITE_APP_NAME || 'Closet Glass Chic',
    appVersion: import.meta.env.VITE_APP_VERSION || '1.0.0',
    nodeEnv: import.meta.env.NODE_ENV || 'development',

    // Network Configuration
    baseUrl,
    frontendPort,
    backendPort,

    // Computed URLs
    apiBaseUrl,
    backendBaseUrl,
    frontendUrl,

    // API Configuration
    apiTimeout: parseNumber(import.meta.env.VITE_API_TIMEOUT, 10000),

    // Development Server
    host: import.meta.env.VITE_HOST || '0.0.0.0',
    
    // External APIs
    openWeatherApiKey: import.meta.env.VITE_OPENWEATHER_API_KEY || '',
    openWeatherApiUrl: import.meta.env.VITE_OPENWEATHER_API_URL || 'https://api.openweathermap.org/data/2.5',
    nominatimApiUrl: import.meta.env.VITE_NOMINATIM_API_URL || 'https://nominatim.openstreetmap.org',
    
    // Rate Limiting
    apiRateLimitRequests: parseNumber(import.meta.env.VITE_API_RATE_LIMIT_REQUESTS, 60),
    apiRateLimitWindow: parseNumber(import.meta.env.VITE_API_RATE_LIMIT_WINDOW, 60000),
    apiCacheDuration: parseNumber(import.meta.env.VITE_API_CACHE_DURATION, 300000),
    
    // Database
    dbName: import.meta.env.VITE_DB_NAME || 'closet_glass_chic',
    dbVersion: parseNumber(import.meta.env.VITE_DB_VERSION, 1),
    
    // File Upload
    maxFileSize: parseNumber(import.meta.env.VITE_MAX_FILE_SIZE, 5242880), // 5MB
    allowedFileTypes: parseStringArray(
      import.meta.env.VITE_ALLOWED_FILE_TYPES,
      ['image/jpeg', 'image/png', 'image/webp', 'image/heic']
    ),
    maxImageWidth: parseNumber(import.meta.env.VITE_MAX_IMAGE_WIDTH, 2048),
    maxImageHeight: parseNumber(import.meta.env.VITE_MAX_IMAGE_HEIGHT, 2048),
    
    // Feature Flags
    enableAnalytics: parseBoolean(import.meta.env.VITE_ENABLE_ANALYTICS, false),
    enableDebug: parseBoolean(import.meta.env.VITE_ENABLE_DEBUG, false),
    enableWeatherIntegration: parseBoolean(import.meta.env.VITE_ENABLE_WEATHER_INTEGRATION, true),
    enableCityAutocomplete: parseBoolean(import.meta.env.VITE_ENABLE_CITY_AUTOCOMPLETE, true),
  };
}

// Export singleton instance
export const env = getEnvironmentConfig();

// Export individual config sections for convenience
export const apiConfig = {
  baseUrl: env.apiBaseUrl,
  backendBaseUrl: env.backendBaseUrl,
  frontendUrl: env.frontendUrl,
  timeout: env.apiTimeout,
};

export const networkConfig = {
  baseUrl: env.baseUrl,
  frontendPort: env.frontendPort,
  backendPort: env.backendPort,
};

export const featureFlags = {
  analytics: env.enableAnalytics,
  debug: env.enableDebug,
  weatherIntegration: env.enableWeatherIntegration,
  cityAutocomplete: env.enableCityAutocomplete,
};

export const fileUploadConfig = {
  maxSize: env.maxFileSize,
  allowedTypes: env.allowedFileTypes,
  maxWidth: env.maxImageWidth,
  maxHeight: env.maxImageHeight,
};

export default env;
