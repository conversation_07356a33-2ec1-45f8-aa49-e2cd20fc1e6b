import { describe, it, expect, beforeEach, vi } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { ReactNode } from 'react';
import { OnboardingProvider } from '../../contexts/OnboardingContext';
import { useOnboarding } from '../useOnboarding';

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Test wrapper component
const TestWrapper = ({ children }: { children: ReactNode }) => (
  <OnboardingProvider>{children}</OnboardingProvider>
);

describe('useOnboarding', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  describe('State properties', () => {
    it('should provide initial state properties', () => {
      const { result } = renderHook(() => useOnboarding(), {
        wrapper: TestWrapper,
      });

      expect(result.current.isCompleted).toBe(false);
      expect(result.current.currentStep).toBe(0);
      expect(result.current.skippedSteps).toEqual([]);
      expect(result.current.completedAt).toBeUndefined();
      expect(result.current.totalSteps).toBe(4);
    });

    it('should provide user preferences', () => {
      const { result } = renderHook(() => useOnboarding(), {
        wrapper: TestWrapper,
      });

      expect(result.current.userPreferences).toEqual({
        enableNotifications: false,
        preferredStyle: 'casual',
        weatherLocation: '',
      });
    });
  });

  describe('Current step information', () => {
    it('should provide current step data', () => {
      const { result } = renderHook(() => useOnboarding(), {
        wrapper: TestWrapper,
      });

      expect(result.current.currentStepData?.id).toBe('welcome');
      expect(result.current.currentStepData?.title).toBe('Welcome to Your Fashion Assistant');
      expect(result.current.currentStepData?.canSkip).toBe(false);
    });

    it('should calculate progress correctly', () => {
      const { result } = renderHook(() => useOnboarding(), {
        wrapper: TestWrapper,
      });

      expect(result.current.progress).toBe(25); // Step 1 of 4 = 25%

      act(() => {
        result.current.nextStep();
      });

      expect(result.current.progress).toBe(50); // Step 2 of 4 = 50%
    });

    it('should identify first and last steps', () => {
      const { result } = renderHook(() => useOnboarding(), {
        wrapper: TestWrapper,
      });

      expect(result.current.isFirstStep).toBe(true);
      expect(result.current.isLastStep).toBe(false);

      // Navigate to last step
      act(() => {
        result.current.nextStep();
        result.current.nextStep();
        result.current.nextStep();
      });

      expect(result.current.isFirstStep).toBe(false);
      expect(result.current.isLastStep).toBe(true);
    });

    it('should check if current step can be skipped', () => {
      const { result } = renderHook(() => useOnboarding(), {
        wrapper: TestWrapper,
      });

      expect(result.current.canSkipCurrentStep).toBe(false); // Welcome step cannot be skipped

      act(() => {
        result.current.nextStep();
      });

      expect(result.current.canSkipCurrentStep).toBe(true); // Closet management can be skipped
    });
  });

  describe('Navigation functions', () => {
    it('should provide navigation capabilities', () => {
      const { result } = renderHook(() => useOnboarding(), {
        wrapper: TestWrapper,
      });

      expect(result.current.canGoNext).toBe(true);
      expect(result.current.canGoPrevious).toBe(false);

      act(() => {
        result.current.nextStep();
      });

      expect(result.current.currentStep).toBe(1);
      expect(result.current.canGoNext).toBe(true);
      expect(result.current.canGoPrevious).toBe(true);

      act(() => {
        result.current.previousStep();
      });

      expect(result.current.currentStep).toBe(0);
    });

    it('should handle step skipping', () => {
      const { result } = renderHook(() => useOnboarding(), {
        wrapper: TestWrapper,
      });

      // Go to a skippable step
      act(() => {
        result.current.nextStep();
      });

      expect(result.current.canSkipCurrentStep).toBe(true);

      act(() => {
        result.current.skipStep();
      });

      expect(result.current.skippedSteps).toContain('closet-management');
      expect(result.current.currentStep).toBe(2);
    });
  });

  describe('Completion functions', () => {
    it('should complete onboarding', () => {
      const { result } = renderHook(() => useOnboarding(), {
        wrapper: TestWrapper,
      });

      act(() => {
        result.current.completeOnboarding();
      });

      expect(result.current.isCompleted).toBe(true);
      expect(result.current.completedAt).toBeInstanceOf(Date);
    });

    it('should reset onboarding', () => {
      const { result } = renderHook(() => useOnboarding(), {
        wrapper: TestWrapper,
      });

      // Set some state
      act(() => {
        result.current.nextStep();
        result.current.updateUserPreferences({ enableNotifications: true });
        result.current.completeOnboarding();
      });

      expect(result.current.isCompleted).toBe(true);
      expect(result.current.currentStep).toBe(1);

      act(() => {
        result.current.resetOnboarding();
      });

      expect(result.current.isCompleted).toBe(false);
      expect(result.current.currentStep).toBe(0);
      expect(result.current.userPreferences.enableNotifications).toBe(true); // Preserved
    });
  });

  describe('User preferences', () => {
    it('should update user preferences', () => {
      const { result } = renderHook(() => useOnboarding(), {
        wrapper: TestWrapper,
      });

      act(() => {
        result.current.updateUserPreferences({
          enableNotifications: true,
          preferredStyle: 'formal',
          weatherLocation: 'New York',
        });
      });

      expect(result.current.userPreferences).toEqual({
        enableNotifications: true,
        preferredStyle: 'formal',
        weatherLocation: 'New York',
      });
    });

    it('should partially update user preferences', () => {
      const { result } = renderHook(() => useOnboarding(), {
        wrapper: TestWrapper,
      });

      act(() => {
        result.current.updateUserPreferences({
          enableNotifications: true,
        });
      });

      expect(result.current.userPreferences).toEqual({
        enableNotifications: true,
        preferredStyle: 'casual', // Unchanged
        weatherLocation: '', // Unchanged
      });
    });
  });

  describe('Integration with context', () => {
    it('should reflect context state changes', () => {
      const { result } = renderHook(() => useOnboarding(), {
        wrapper: TestWrapper,
      });

      // Simulate external state change
      act(() => {
        result.current.nextStep();
        result.current.nextStep();
      });

      expect(result.current.currentStep).toBe(2);
      expect(result.current.progress).toBe(75);
      expect(result.current.currentStepData?.id).toBe('outfit-planning');
      expect(result.current.isFirstStep).toBe(false);
      expect(result.current.isLastStep).toBe(false);
    });
  });

  describe('Edge cases', () => {
    it('should handle completion at last step', () => {
      const { result } = renderHook(() => useOnboarding(), {
        wrapper: TestWrapper,
      });

      // Navigate to last step
      act(() => {
        result.current.nextStep();
        result.current.nextStep();
        result.current.nextStep();
      });

      expect(result.current.isLastStep).toBe(true);
      expect(result.current.canGoNext).toBe(false);

      // Try to go next from last step should complete onboarding
      act(() => {
        result.current.nextStep();
      });

      expect(result.current.isCompleted).toBe(true);
    });

    it('should handle empty or undefined step data gracefully', () => {
      const { result } = renderHook(() => useOnboarding(), {
        wrapper: TestWrapper,
      });

      // Current step should always exist in normal flow
      expect(result.current.currentStepData).toBeDefined();
      expect(result.current.canSkipCurrentStep).toBe(false);
    });
  });
});