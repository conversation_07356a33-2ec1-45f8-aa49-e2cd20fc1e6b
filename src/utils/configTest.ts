// Configuration test utility
// Use this to verify that environment variables are being loaded correctly

import { env, apiConfig, featureFlags } from '@/config/environment';

export interface ConfigTestResult {
  success: boolean;
  message: string;
  details?: any;
}

/**
 * Test that all required environment variables are loaded
 */
export function testEnvironmentConfig(): ConfigTestResult[] {
  const results: ConfigTestResult[] = [];

  // Test network configuration
  results.push({
    success: !!env.baseUrl && env.baseUrl !== 'localhost',
    message: 'Base URL is configured (not localhost)',
    details: { baseUrl: env.baseUrl }
  });

  results.push({
    success: env.frontendPort > 0 && env.frontendPort < 65536,
    message: 'Frontend port is valid',
    details: { frontendPort: env.frontendPort }
  });

  results.push({
    success: env.backendPort > 0 && env.backendPort < 65536,
    message: 'Backend port is valid',
    details: { backendPort: env.backendPort }
  });

  // Test computed URLs
  results.push({
    success: !!env.apiBaseUrl && !env.apiBaseUrl.includes('undefined'),
    message: 'API Base URL is properly computed',
    details: { apiBaseUrl: env.apiBaseUrl }
  });

  results.push({
    success: !!env.backendBaseUrl && !env.backendBaseUrl.includes('undefined'),
    message: 'Backend Base URL is properly computed',
    details: { backendBaseUrl: env.backendBaseUrl }
  });

  results.push({
    success: !!env.frontendUrl && !env.frontendUrl.includes('undefined'),
    message: 'Frontend URL is properly computed',
    details: { frontendUrl: env.frontendUrl }
  });

  // Test external API configuration
  results.push({
    success: !!env.openWeatherApiKey && env.openWeatherApiKey !== 'your_openweather_api_key_here',
    message: 'OpenWeather API key is configured',
    details: { hasKey: !!env.openWeatherApiKey, isPlaceholder: env.openWeatherApiKey === 'your_openweather_api_key_here' }
  });

  results.push({
    success: !!env.nominatimApiUrl,
    message: 'Nominatim API URL is configured',
    details: { nominatimApiUrl: env.nominatimApiUrl }
  });

  // Test feature flags
  results.push({
    success: typeof env.enableDebug === 'boolean',
    message: 'Debug feature flag is properly configured',
    details: { enableDebug: env.enableDebug }
  });

  return results;
}

/**
 * Test API connectivity using the configured URLs
 */
export async function testApiConnectivity(): Promise<ConfigTestResult[]> {
  const results: ConfigTestResult[] = [];

  // Test backend health endpoint
  try {
    const healthUrl = `${env.backendBaseUrl}/health`;
    const response = await fetch(healthUrl);
    const data = await response.json();
    
    results.push({
      success: response.ok && data.status === 'healthy',
      message: 'Backend health check',
      details: { url: healthUrl, status: response.status, data }
    });
  } catch (error) {
    results.push({
      success: false,
      message: 'Backend health check failed',
      details: { error: error instanceof Error ? error.message : 'Unknown error' }
    });
  }

  // Test API endpoint
  try {
    const apiUrl = `${env.apiBaseUrl}/auth/validate-email`;
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email: '<EMAIL>' })
    });
    const data = await response.json();
    
    results.push({
      success: response.ok && data.success,
      message: 'API endpoint connectivity',
      details: { url: apiUrl, status: response.status, data }
    });
  } catch (error) {
    results.push({
      success: false,
      message: 'API endpoint connectivity failed',
      details: { error: error instanceof Error ? error.message : 'Unknown error' }
    });
  }

  return results;
}

/**
 * Run all configuration tests
 */
export async function runAllConfigTests(): Promise<{
  environmentTests: ConfigTestResult[];
  connectivityTests: ConfigTestResult[];
  summary: {
    totalTests: number;
    passedTests: number;
    failedTests: number;
    successRate: number;
  };
}> {
  console.log('🧪 Running configuration tests...');

  const environmentTests = testEnvironmentConfig();
  const connectivityTests = await testApiConnectivity();

  const allTests = [...environmentTests, ...connectivityTests];
  const passedTests = allTests.filter(test => test.success).length;
  const failedTests = allTests.length - passedTests;

  const summary = {
    totalTests: allTests.length,
    passedTests,
    failedTests,
    successRate: Math.round((passedTests / allTests.length) * 100)
  };

  console.log('📊 Configuration Test Results:');
  console.log(`✅ Passed: ${passedTests}/${allTests.length} (${summary.successRate}%)`);
  
  if (failedTests > 0) {
    console.log(`❌ Failed: ${failedTests}`);
    allTests.filter(test => !test.success).forEach(test => {
      console.log(`  - ${test.message}:`, test.details);
    });
  }

  return {
    environmentTests,
    connectivityTests,
    summary
  };
}

// Make available in browser console for testing
if (typeof window !== 'undefined') {
  (window as any).testConfig = {
    runAllConfigTests,
    testEnvironmentConfig,
    testApiConnectivity,
    env,
    apiConfig,
    featureFlags
  };
}
