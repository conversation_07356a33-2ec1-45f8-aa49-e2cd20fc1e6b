// Debug utility to check environment configuration in the browser
import { env } from '@/config/environment';

/**
 * Debug function to log all environment configuration
 * Call this in the browser console to check if environment variables are working
 */
export function debugEnvironmentConfig() {
  console.group('🔧 Environment Configuration Debug');
  
  console.log('📋 Raw Environment Variables:');
  console.log('  VITE_BASE_URL:', import.meta.env.VITE_BASE_URL);
  console.log('  VITE_FRONTEND_PORT:', import.meta.env.VITE_FRONTEND_PORT);
  console.log('  VITE_BACKEND_PORT:', import.meta.env.VITE_BACKEND_PORT);
  
  console.log('\n🌐 Computed Configuration:');
  console.log('  Base URL:', env.baseUrl);
  console.log('  Frontend Port:', env.frontendPort);
  console.log('  Backend Port:', env.backendPort);
  
  console.log('\n🔗 Computed URLs:');
  console.log('  API Base URL:', env.apiBaseUrl);
  console.log('  Backend Base URL:', env.backendBaseUrl);
  console.log('  Frontend URL:', env.frontendUrl);
  
  console.log('\n🧪 Test URLs:');
  console.log('  Health Check:', `${env.backendBaseUrl}/health`);
  console.log('  Auth API:', `${env.apiBaseUrl}/auth/validate-email`);
  
  console.groupEnd();
  
  return {
    raw: {
      baseUrl: import.meta.env.VITE_BASE_URL,
      frontendPort: import.meta.env.VITE_FRONTEND_PORT,
      backendPort: import.meta.env.VITE_BACKEND_PORT,
    },
    computed: {
      baseUrl: env.baseUrl,
      frontendPort: env.frontendPort,
      backendPort: env.backendPort,
      apiBaseUrl: env.apiBaseUrl,
      backendBaseUrl: env.backendBaseUrl,
      frontendUrl: env.frontendUrl,
    }
  };
}

/**
 * Test API connectivity using the computed URLs
 */
export async function testApiConnectivity() {
  console.group('🧪 API Connectivity Test');
  
  try {
    // Test health endpoint
    console.log('Testing health endpoint...');
    const healthUrl = `${env.backendBaseUrl}/health`;
    console.log('Health URL:', healthUrl);
    
    const healthResponse = await fetch(healthUrl);
    const healthData = await healthResponse.json();
    
    if (healthResponse.ok) {
      console.log('✅ Health check passed:', healthData);
    } else {
      console.log('❌ Health check failed:', healthResponse.status, healthData);
    }
    
    // Test API endpoint
    console.log('\nTesting API endpoint...');
    const apiUrl = `${env.apiBaseUrl}/auth/validate-email`;
    console.log('API URL:', apiUrl);
    
    const apiResponse = await fetch(apiUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email: '<EMAIL>' })
    });
    const apiData = await apiResponse.json();
    
    if (apiResponse.ok) {
      console.log('✅ API test passed:', apiData);
    } else {
      console.log('❌ API test failed:', apiResponse.status, apiData);
    }
    
  } catch (error) {
    console.error('❌ Connectivity test failed:', error);
  }
  
  console.groupEnd();
}

// Make functions available in browser console
if (typeof window !== 'undefined') {
  (window as any).debugConfig = debugEnvironmentConfig;
  (window as any).testApiConnectivity = testApiConnectivity;
  
  // Auto-run debug on load if debug is enabled
  if (env.enableDebug) {
    console.log('🔧 Debug mode enabled. Use debugConfig() or testApiConnectivity() in console to test configuration.');
  }
}
