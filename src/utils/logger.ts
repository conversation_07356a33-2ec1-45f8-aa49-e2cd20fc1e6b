// Frontend Logger Utility
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

interface LogEntry {
  timestamp: string;
  level: LogLevel;
  category: string;
  message: string;
  data?: any;
  error?: Error;
}

class Logger {
  private logLevel: LogLevel;
  private logs: LogEntry[] = [];
  private maxLogs = 1000;

  constructor() {
    this.logLevel = import.meta.env.DEV ? LogLevel.DEBUG : LogLevel.INFO;
  }

  private formatTimestamp(): string {
    return new Date().toISOString();
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.logLevel;
  }

  private createLogEntry(level: LogLevel, category: string, message: string, data?: any, error?: Error): LogEntry {
    return {
      timestamp: this.formatTimestamp(),
      level,
      category,
      message,
      data,
      error,
    };
  }

  private addToHistory(entry: LogEntry): void {
    this.logs.push(entry);
    if (this.logs.length > this.maxLogs) {
      this.logs.shift();
    }
  }

  private getLogLevelName(level: LogLevel): string {
    switch (level) {
      case LogLevel.DEBUG: return 'DEBUG';
      case LogLevel.INFO: return 'INFO';
      case LogLevel.WARN: return 'WARN';
      case LogLevel.ERROR: return 'ERROR';
      default: return 'UNKNOWN';
    }
  }

  private formatConsoleMessage(entry: LogEntry): string {
    return `[${entry.timestamp}] [${this.getLogLevelName(entry.level)}] [${entry.category}] ${entry.message}`;
  }

  private logToConsole(entry: LogEntry): void {
    const message = this.formatConsoleMessage(entry);
    
    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(message, entry.data || '');
        break;
      case LogLevel.INFO:
        console.info(message, entry.data || '');
        break;
      case LogLevel.WARN:
        console.warn(message, entry.data || '', entry.error || '');
        break;
      case LogLevel.ERROR:
        console.error(message, entry.data || '', entry.error || '');
        break;
    }
  }

  debug(category: string, message: string, data?: any): void {
    if (!this.shouldLog(LogLevel.DEBUG)) return;
    const entry = this.createLogEntry(LogLevel.DEBUG, category, message, data);
    this.addToHistory(entry);
    this.logToConsole(entry);
  }

  info(category: string, message: string, data?: any): void {
    if (!this.shouldLog(LogLevel.INFO)) return;
    const entry = this.createLogEntry(LogLevel.INFO, category, message, data);
    this.addToHistory(entry);
    this.logToConsole(entry);
  }

  warn(category: string, message: string, data?: any, error?: Error): void {
    if (!this.shouldLog(LogLevel.WARN)) return;
    const entry = this.createLogEntry(LogLevel.WARN, category, message, data, error);
    this.addToHistory(entry);
    this.logToConsole(entry);
  }

  error(category: string, message: string, data?: any, error?: Error): void {
    if (!this.shouldLog(LogLevel.ERROR)) return;
    const entry = this.createLogEntry(LogLevel.ERROR, category, message, data, error);
    this.addToHistory(entry);
    this.logToConsole(entry);
  }

  // Get log history for debugging
  getHistory(): LogEntry[] {
    return [...this.logs];
  }

  // Export logs for debugging
  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2);
  }

  // Clear log history
  clearHistory(): void {
    this.logs = [];
  }
}

// Create singleton instance
export const logger = new Logger();

// Convenience functions for common categories
export const onboardingLogger = {
  debug: (message: string, data?: any) => logger.debug('ONBOARDING', message, data),
  info: (message: string, data?: any) => logger.info('ONBOARDING', message, data),
  warn: (message: string, data?: any, error?: Error) => logger.warn('ONBOARDING', message, data, error),
  error: (message: string, data?: any, error?: Error) => logger.error('ONBOARDING', message, data, error),
};

export const databaseLogger = {
  debug: (message: string, data?: any) => logger.debug('DATABASE', message, data),
  info: (message: string, data?: any) => logger.info('DATABASE', message, data),
  warn: (message: string, data?: any, error?: Error) => logger.warn('DATABASE', message, data, error),
  error: (message: string, data?: any, error?: Error) => logger.error('DATABASE', message, data, error),
};

export const apiLogger = {
  debug: (message: string, data?: any) => logger.debug('API', message, data),
  info: (message: string, data?: any) => logger.info('API', message, data),
  warn: (message: string, data?: any, error?: Error) => logger.warn('API', message, data, error),
  error: (message: string, data?: any, error?: Error) => logger.error('API', message, data, error),
};

export const weatherLogger = {
  debug: (message: string, data?: any) => logger.debug('WEATHER', message, data),
  info: (message: string, data?: any) => logger.info('WEATHER', message, data),
  warn: (message: string, data?: any, error?: Error) => logger.warn('WEATHER', message, data, error),
  error: (message: string, data?: any, error?: Error) => logger.error('WEATHER', message, data, error),
};

// Global error handler
window.addEventListener('error', (event) => {
  logger.error('GLOBAL', 'Unhandled error', {
    message: event.message,
    filename: event.filename,
    lineno: event.lineno,
    colno: event.colno,
  }, event.error);
});

window.addEventListener('unhandledrejection', (event) => {
  logger.error('GLOBAL', 'Unhandled promise rejection', {
    reason: event.reason,
  });
});

// Make logger available globally for debugging
if (import.meta.env.DEV) {
  (window as any).logger = logger;
}
