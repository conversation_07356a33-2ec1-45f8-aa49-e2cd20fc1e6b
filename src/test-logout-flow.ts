/**
 * Test script to verify the logout-signup flow works correctly
 * This script tests the complete flow: Login → Logout → Signup
 */

import PersistentAuthService from './services/persistentAuth';

export async function testLogoutSignupFlow(): Promise<boolean> {
  try {
    console.log('🧪 Starting logout-signup flow test...');

    // Step 1: Check initial state
    console.log('1️⃣ Checking initial state...');
    const initialUser = await PersistentAuthService.getCurrentUser();
    console.log('Initial user:', initialUser ? 'User found' : 'No user');

    // Step 2: Simulate logout
    console.log('2️⃣ Testing logout process...');
    PersistentAuthService.signOut();
    
    // Verify logout cleared everything
    const userAfterLogout = await PersistentAuthService.getCurrentUser();
    const profileAfterLogout = PersistentAuthService.getCurrentUserProfile();
    const onboardingState = localStorage.getItem('onboarding-state');
    
    console.log('After logout:');
    console.log('- User:', userAfterLogout ? 'Still exists (❌)' : 'Cleared (✅)');
    console.log('- Profile:', profileAfterLogout ? 'Still exists (❌)' : 'Cleared (✅)');
    console.log('- Onboarding state:', onboardingState ? 'Still exists (❌)' : 'Cleared (✅)');

    // Step 3: Check localStorage is clean
    console.log('3️⃣ Checking localStorage cleanup...');
    const authRelatedKeys = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (
        key.startsWith('user-') ||
        key.startsWith('auth-') ||
        key.startsWith('profile-') ||
        key.startsWith('onboarding-') ||
        key.includes('session') ||
        key.includes('token')
      )) {
        authRelatedKeys.push(key);
      }
    }
    
    console.log('Remaining auth-related keys:', authRelatedKeys.length === 0 ? 'None (✅)' : authRelatedKeys);

    // Step 4: Test that signup flow can start fresh
    console.log('4️⃣ Testing fresh signup state...');
    
    // Simulate what happens when user clicks signup
    const canStartSignup = !userAfterLogout && !profileAfterLogout && !onboardingState;
    console.log('Can start fresh signup:', canStartSignup ? 'Yes (✅)' : 'No (❌)');

    // Step 5: Verify no conflicts
    console.log('5️⃣ Checking for potential conflicts...');
    const hasConflicts = authRelatedKeys.length > 0 || userAfterLogout || profileAfterLogout || onboardingState;
    console.log('Has conflicts:', hasConflicts ? 'Yes (❌)' : 'No (✅)');

    const testPassed = !hasConflicts && canStartSignup;
    
    if (testPassed) {
      console.log('🎉 Logout-signup flow test PASSED!');
      console.log('✅ Users can logout and signup again without issues');
    } else {
      console.log('❌ Logout-signup flow test FAILED!');
      console.log('⚠️ There may be blank screen issues during signup');
    }

    return testPassed;

  } catch (error) {
    console.error('❌ Test failed with error:', error);
    return false;
  }
}

// Function to run the test from browser console
(window as any).testLogoutSignupFlow = testLogoutSignupFlow;

console.log('🧪 Logout-signup flow test loaded. Run testLogoutSignupFlow() in console to test.');
