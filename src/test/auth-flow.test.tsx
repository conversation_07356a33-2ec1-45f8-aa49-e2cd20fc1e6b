import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { AuthService } from '@/services/auth';
import { SimpleLandingPage } from '@/pages/SimpleLandingPage';
import { LoginPage } from '@/pages/LoginPage';
import { AuthGuard } from '@/components/AuthGuard';

// Mock the AuthService
vi.mock('@/services/auth', () => ({
  AuthService: {
    getCurrentUser: vi.fn(),
    signIn: vi.fn(),
    signOut: vi.fn(),
    setCurrentUser: vi.fn(),
  }
}));

// Mock the onboarding hook
vi.mock('@/hooks/useOnboarding', () => ({
  useOnboarding: () => ({
    isCompleted: true,
    resetOnboarding: vi.fn(),
    updateUserProfile: vi.fn(),
  })
}));

describe('Authentication Flow', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorage.clear();
  });

  describe('SimpleLandingPage', () => {
    it('renders landing page with sign up and sign in buttons', () => {
      const mockOnSignUp = vi.fn();
      const mockOnSignIn = vi.fn();

      render(
        <SimpleLandingPage 
          onSignUp={mockOnSignUp}
          onSignIn={mockOnSignIn}
        />
      );

      expect(screen.getByText('Closet Glass Chic')).toBeInTheDocument();
      expect(screen.getByText('Your Digital Closet,')).toBeInTheDocument();
      expect(screen.getByText('Reimagined')).toBeInTheDocument();
      
      const signUpButton = screen.getByRole('button', { name: /sign up & get started/i });
      const signInButton = screen.getByRole('button', { name: /sign in/i });
      
      expect(signUpButton).toBeInTheDocument();
      expect(signInButton).toBeInTheDocument();

      fireEvent.click(signUpButton);
      expect(mockOnSignUp).toHaveBeenCalledTimes(1);

      fireEvent.click(signInButton);
      expect(mockOnSignIn).toHaveBeenCalledTimes(1);
    });

    it('displays key features', () => {
      const mockOnSignUp = vi.fn();
      const mockOnSignIn = vi.fn();

      render(
        <SimpleLandingPage 
          onSignUp={mockOnSignUp}
          onSignIn={mockOnSignIn}
        />
      );

      expect(screen.getByText('Smart Wardrobe')).toBeInTheDocument();
      expect(screen.getByText('Weather Outfits')).toBeInTheDocument();
      expect(screen.getByText('Outfit Planning')).toBeInTheDocument();
      expect(screen.getByText('Style Analytics')).toBeInTheDocument();
    });
  });

  describe('LoginPage', () => {
    it('renders login form with email and password fields', () => {
      const mockOnBack = vi.fn();
      const mockOnSignUp = vi.fn();
      const mockOnSuccess = vi.fn();

      render(
        <LoginPage
          onBack={mockOnBack}
          onSignUp={mockOnSignUp}
          onSuccess={mockOnSuccess}
        />
      );

      expect(screen.getByText('Welcome Back')).toBeInTheDocument();
      expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
    });

    it('validates form fields', async () => {
      const mockOnBack = vi.fn();
      const mockOnSignUp = vi.fn();
      const mockOnSuccess = vi.fn();

      render(
        <LoginPage
          onBack={mockOnBack}
          onSignUp={mockOnSignUp}
          onSuccess={mockOnSuccess}
        />
      );

      const submitButton = screen.getByRole('button', { name: /sign in/i });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Email is required')).toBeInTheDocument();
        expect(screen.getByText('Password is required')).toBeInTheDocument();
      });
    });

    it('calls AuthService.signIn on form submission', async () => {
      const mockSignIn = vi.mocked(AuthService.signIn);
      mockSignIn.mockResolvedValue({ success: true, user: { id: '1', email: '<EMAIL>' } as any });

      const mockOnBack = vi.fn();
      const mockOnSignUp = vi.fn();
      const mockOnSuccess = vi.fn();

      render(
        <LoginPage
          onBack={mockOnBack}
          onSignUp={mockOnSignUp}
          onSuccess={mockOnSuccess}
        />
      );

      const emailInput = screen.getByLabelText(/email address/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'password123' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockSignIn).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'password123'
        });
        expect(mockOnSuccess).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe('AuthGuard', () => {
    it('renders children when user is authenticated', async () => {
      const mockGetCurrentUser = vi.mocked(AuthService.getCurrentUser);
      mockGetCurrentUser.mockReturnValue({ id: '1', email: '<EMAIL>' } as any);

      render(
        <AuthGuard>
          <div>Protected Content</div>
        </AuthGuard>
      );

      await waitFor(() => {
        expect(screen.getByText('Protected Content')).toBeInTheDocument();
      });
    });

    it('renders fallback when user is not authenticated', async () => {
      const mockGetCurrentUser = vi.mocked(AuthService.getCurrentUser);
      mockGetCurrentUser.mockReturnValue(null);

      render(
        <AuthGuard fallback={<div>Please sign in</div>}>
          <div>Protected Content</div>
        </AuthGuard>
      );

      await waitFor(() => {
        expect(screen.getByText('Please sign in')).toBeInTheDocument();
        expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
      });
    });

    it('shows loading state initially', () => {
      const mockGetCurrentUser = vi.mocked(AuthService.getCurrentUser);
      mockGetCurrentUser.mockReturnValue(null);

      render(
        <AuthGuard>
          <div>Protected Content</div>
        </AuthGuard>
      );

      expect(screen.getByText('Checking authentication...')).toBeInTheDocument();
    });
  });
});
