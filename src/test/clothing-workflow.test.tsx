import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { AddClothingModal } from '@/components/AddClothingModal';
import { ClothingApiService } from '@/services/clothingApi';

// Mock the API service
vi.mock('@/services/clothingApi', () => ({
  ClothingApiService: {
    uploadAndAnalyzeImage: vi.fn(),
    saveClothingItem: vi.fn(),
    getClothingItems: vi.fn(),
  },
}));

// Mock file picker service
vi.mock('@/services/filePicker', () => ({
  FilePickerService: {
    captureFromCamera: vi.fn(),
    selectFromGallery: vi.fn(),
    browseFiles: vi.fn(),
  },
  hasCamera: vi.fn(() => true),
}));

describe('Clothing Workflow Integration Tests', () => {
  const mockOnClose = vi.fn();
  const mockOnSave = vi.fn();
  const mockUserId = 'test-user-123';

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render the upload modal correctly', () => {
    render(
      <AddClothingModal
        isOpen={true}
        onClose={mockOnClose}
        onSave={mockOnSave}
        userId={mockUserId}
      />
    );

    expect(screen.getByText('Add New Clothing Item')).toBeInTheDocument();
    expect(screen.getByText('Choose how to add your item')).toBeInTheDocument();
    expect(screen.getByText('Take Photo')).toBeInTheDocument();
    expect(screen.getByText('Upload Photo')).toBeInTheDocument();
    expect(screen.getByText('Browse Files')).toBeInTheDocument();
  });

  it('should handle successful image upload and analysis', async () => {
    const mockAnalysisResult = {
      success: true,
      data: {
        originalImage: '/uploads/original.jpg',
        processedImage: '/uploads/processed.png',
        analysis: {
          category: 'T-Shirts',
          color: 'Blue',
        },
      },
    };

    vi.mocked(ClothingApiService.uploadAndAnalyzeImage).mockResolvedValue(mockAnalysisResult);

    render(
      <AddClothingModal
        isOpen={true}
        onClose={mockOnClose}
        onSave={mockOnSave}
        userId={mockUserId}
      />
    );

    // Simulate file upload (this would normally be triggered by file picker)
    const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    
    // We can't easily test the file picker interaction, but we can test the API call
    await ClothingApiService.uploadAndAnalyzeImage(mockFile);
    
    expect(ClothingApiService.uploadAndAnalyzeImage).toHaveBeenCalledWith(mockFile);
  });

  it('should handle image analysis errors gracefully', async () => {
    const mockError = new Error('Failed to analyze image');
    vi.mocked(ClothingApiService.uploadAndAnalyzeImage).mockRejectedValue(mockError);

    const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    
    await expect(ClothingApiService.uploadAndAnalyzeImage(mockFile)).rejects.toThrow('Failed to analyze image');
  });

  it('should save clothing item with correct data', async () => {
    const mockSavedItem = {
      id: 'item-123',
      user_id: mockUserId,
      name: 'Blue T-Shirt',
      category_id: 'cat-123',
      color: 'Blue',
      brand: 'TestBrand',
      size: 'M',
      image_url: '/uploads/processed.png',
      tags: ['Summer'],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      category_name: 'T-Shirts',
    };

    vi.mocked(ClothingApiService.saveClothingItem).mockResolvedValue(mockSavedItem);

    const itemData = {
      userId: mockUserId,
      name: 'Blue T-Shirt',
      category: 'T-Shirts',
      color: 'Blue',
      brand: 'TestBrand',
      size: 'M',
      season: 'Summer',
      processedImageUrl: '/uploads/processed.png',
    };

    const result = await ClothingApiService.saveClothingItem(itemData);

    expect(ClothingApiService.saveClothingItem).toHaveBeenCalledWith(itemData);
    expect(result).toEqual(mockSavedItem);
  });

  it('should fetch clothing items for a user', async () => {
    const mockClothingItems = [
      {
        id: 'item-1',
        user_id: mockUserId,
        name: 'Blue T-Shirt',
        color: 'Blue',
        category_name: 'T-Shirts',
        brand: 'TestBrand',
        size: 'M',
        image_url: '/uploads/item1.png',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        id: 'item-2',
        user_id: mockUserId,
        name: 'Black Jeans',
        color: 'Black',
        category_name: 'Jeans',
        brand: 'DenimCo',
        size: '32',
        image_url: '/uploads/item2.png',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
    ];

    vi.mocked(ClothingApiService.getClothingItems).mockResolvedValue(mockClothingItems);

    const result = await ClothingApiService.getClothingItems(mockUserId);

    expect(ClothingApiService.getClothingItems).toHaveBeenCalledWith(mockUserId);
    expect(result).toEqual(mockClothingItems);
    expect(result).toHaveLength(2);
  });

  it('should handle API errors when fetching clothing items', async () => {
    const mockError = new Error('Failed to fetch clothing items');
    vi.mocked(ClothingApiService.getClothingItems).mockRejectedValue(mockError);

    await expect(ClothingApiService.getClothingItems(mockUserId)).rejects.toThrow('Failed to fetch clothing items');
  });

  it('should validate required fields before saving', async () => {
    const incompleteData = {
      userId: mockUserId,
      // Missing required fields: category, color
      brand: 'TestBrand',
      size: 'M',
    };

    // The API should reject incomplete data
    const mockError = new Error('Missing required fields: category, color');
    vi.mocked(ClothingApiService.saveClothingItem).mockRejectedValue(mockError);

    await expect(ClothingApiService.saveClothingItem(incompleteData as any)).rejects.toThrow('Missing required fields');
  });
});

describe('Clothing API Service Unit Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Mock fetch globally
    global.fetch = vi.fn();
  });

  it('should make correct API call for image upload', async () => {
    const mockResponse = {
      ok: true,
      json: () => Promise.resolve({
        success: true,
        data: {
          originalImage: '/uploads/original.jpg',
          processedImage: '/uploads/processed.png',
          analysis: { category: 'T-Shirts', color: 'Blue' },
        },
      }),
    };

    vi.mocked(fetch).mockResolvedValue(mockResponse as any);

    const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const result = await ClothingApiService.uploadAndAnalyzeImage(mockFile);

    expect(fetch).toHaveBeenCalledWith(
      expect.stringContaining('/api/clothing/upload'),
      expect.objectContaining({
        method: 'POST',
        body: expect.any(FormData),
      })
    );

    expect(result.success).toBe(true);
    expect(result.data.analysis.category).toBe('T-Shirts');
  });

  it('should handle HTTP errors correctly', async () => {
    const mockErrorResponse = {
      ok: false,
      status: 500,
      json: () => Promise.resolve({ error: 'Internal server error' }),
    };

    vi.mocked(fetch).mockResolvedValue(mockErrorResponse as any);

    const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });

    await expect(ClothingApiService.uploadAndAnalyzeImage(mockFile)).rejects.toThrow();
  });
});
