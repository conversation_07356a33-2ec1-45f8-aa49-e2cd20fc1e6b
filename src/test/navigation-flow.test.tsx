import { render, screen, fireEvent, waitFor, cleanup } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { SignUpPage } from '@/pages/SignUpPage';
import { LoginPage } from '@/pages/LoginPage';
import { authApi } from '@/services/backendApi';

// Mock the backend API
vi.mock('@/services/backendApi', () => ({
  authApi: {
    validateEmail: vi.fn(),
  },
  BackendApiError: class BackendApiError extends Error {
    constructor(message: string, public status: number, public code?: string) {
      super(message);
      this.name = 'BackendApiError';
    }
  },
}));

// Mock PersistentAuthService
vi.mock('@/services/persistentAuth', () => ({
  default: {
    signIn: vi.fn(),
  },
}));

describe('Navigation Flow Integration', () => {
  const mockSignUpProps = {
    onBack: vi.fn(),
    onSignIn: vi.fn(),
    onSuccess: vi.fn(),
  };

  const mockLoginProps = {
    onBack: vi.fn(),
    onSignUp: vi.fn(),
    onSuccess: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    cleanup();
  });

  it('should call onSignIn with email when clicking login suggestion after email validation', async () => {
    const mockValidateEmail = vi.mocked(authApi.validateEmail);
    mockValidateEmail.mockResolvedValue({ 
      available: false, 
      message: 'This email is already registered' 
    });

    const testEmail = '<EMAIL>';

    render(<SignUpPage {...mockSignUpProps} />);
    
    const emailInput = screen.getByPlaceholderText('Enter your email');
    
    // Type email and blur to trigger validation
    fireEvent.change(emailInput, { target: { value: testEmail } });
    fireEvent.blur(emailInput);

    // Wait for validation to complete and show login suggestion
    await waitFor(() => {
      expect(screen.getByText('Sign in instead')).toBeInTheDocument();
    });

    // Click the login suggestion
    const signInButton = screen.getByText('Sign in instead');
    fireEvent.click(signInButton);

    // Verify onSignIn was called with the email
    expect(mockSignUpProps.onSignIn).toHaveBeenCalledWith(testEmail);
  });

  it('should call onSignIn with email when clicking bottom Sign In Instead button', () => {
    const testEmail = '<EMAIL>';

    render(<SignUpPage {...mockSignUpProps} />);
    
    const emailInput = screen.getByPlaceholderText('Enter your email');
    
    // Type email
    fireEvent.change(emailInput, { target: { value: testEmail } });
    
    // Click the "Sign In Instead" button at the bottom
    const signInButton = screen.getByRole('button', { name: /sign in instead/i });
    fireEvent.click(signInButton);

    // Verify onSignIn was called with the email
    expect(mockSignUpProps.onSignIn).toHaveBeenCalledWith(testEmail);
  });

  it('should call onSignIn with empty string when no email is entered', () => {
    render(<SignUpPage {...mockSignUpProps} />);
    
    // Click the "Sign In Instead" button without entering email
    const signInButton = screen.getByRole('button', { name: /sign in instead/i });
    fireEvent.click(signInButton);

    // Verify onSignIn was called with empty email
    expect(mockSignUpProps.onSignIn).toHaveBeenCalledWith('');
  });

  it('should pre-populate email field in LoginPage when initialEmail is provided', () => {
    const testEmail = '<EMAIL>';
    
    render(<LoginPage {...mockLoginProps} initialEmail={testEmail} />);
    
    // Verify the email field is pre-populated
    const loginEmailInput = screen.getByDisplayValue(testEmail);
    expect(loginEmailInput).toBeInTheDocument();
  });

  it('should have empty email field in LoginPage when no initialEmail is provided', () => {
    render(<LoginPage {...mockLoginProps} />);
    
    // Verify the email field is empty
    const loginEmailInput = screen.getByPlaceholderText('Enter your email') as HTMLInputElement;
    expect(loginEmailInput.value).toBe('');
  });
});