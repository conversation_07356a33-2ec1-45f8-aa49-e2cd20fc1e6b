import { render, screen, fireEvent, waitFor, cleanup } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { SignUpPage } from '@/pages/SignUpPage';
import { LoginPage } from '@/pages/LoginPage';
import { authApi, BackendApiError } from '@/services/backendApi';
import PersistentAuthService from '@/services/persistentAuth';

// Mock the backend API
vi.mock('@/services/backendApi', () => ({
  authApi: {
    validateEmail: vi.fn(),
    register: vi.fn(),
  },
  BackendApiError: class BackendApiError extends Error {
    constructor(message: string, public status?: number, public code?: string) {
      super(message);
      this.name = 'BackendApiError';
    }
  },
}));

// Mock PersistentAuthService
vi.mock('@/services/persistentAuth', () => ({
  default: {
    signIn: vi.fn(),
    signUp: vi.fn(),
  },
}));

describe('Email Validation Integration Tests', () => {
  const mockSignUpProps = {
    onBack: vi.fn(),
    onSignIn: vi.fn(),
    onSuccess: vi.fn(),
  };

  const mockLoginProps = {
    onBack: vi.fn(),
    onSignUp: vi.fn(),
    onSuccess: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    cleanup();
  });

  describe('End-to-End Email Validation Flow', () => {
    it('should complete full validation cycle from frontend to database for available email', async () => {
      // Mock successful email validation
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      mockValidateEmail.mockResolvedValue({
        available: true,
        message: 'Email is available'
      });

      const testEmail = '<EMAIL>';

      render(<SignUpPage {...mockSignUpProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      const submitButton = screen.getByRole('button', { name: /create account/i });
      
      // Fill out complete form
      fireEvent.change(screen.getByPlaceholderText('First name'), { target: { value: 'John' } });
      fireEvent.change(screen.getByPlaceholderText('Last name'), { target: { value: 'Doe' } });
      fireEvent.change(emailInput, { target: { value: testEmail } });
      fireEvent.change(screen.getByPlaceholderText('Create a password'), { target: { value: 'Password123' } });
      fireEvent.change(screen.getByPlaceholderText('Confirm your password'), { target: { value: 'Password123' } });
      
      // Trigger email validation
      fireEvent.blur(emailInput);

      // Should show loading indicator during validation
      await waitFor(() => {
        expect(document.querySelector('.animate-spin')).toBeInTheDocument();
      });

      // Wait for validation to complete
      await waitFor(() => {
        expect(mockValidateEmail).toHaveBeenCalledWith(testEmail);
        expect(document.querySelector('.animate-spin')).not.toBeInTheDocument();
      });

      // Should not show any error messages
      expect(screen.queryByText(/already registered/i)).not.toBeInTheDocument();
      expect(screen.queryByText(/error/i)).not.toBeInTheDocument();

      // Submit button should remain enabled
      expect(submitButton).not.toBeDisabled();

      // Should be able to submit the form
      fireEvent.click(submitButton);

      // Should call onSuccess with form data
      await waitFor(() => {
        expect(mockSignUpProps.onSuccess).toHaveBeenCalledWith({
          firstName: 'John',
          lastName: 'Doe',
          email: testEmail,
          password: 'Password123',
          confirmPassword: 'Password123'
        });
      }, { timeout: 2000 });
    });

    it('should complete full validation cycle from frontend to database for taken email', async () => {
      // Mock email already exists
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      mockValidateEmail.mockResolvedValue({
        available: false,
        message: 'This email is already registered'
      });

      const testEmail = '<EMAIL>';

      render(<SignUpPage {...mockSignUpProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      const submitButton = screen.getByRole('button', { name: /create account/i });
      
      // Fill out complete form
      fireEvent.change(screen.getByPlaceholderText('First name'), { target: { value: 'Jane' } });
      fireEvent.change(screen.getByPlaceholderText('Last name'), { target: { value: 'Smith' } });
      fireEvent.change(emailInput, { target: { value: testEmail } });
      fireEvent.change(screen.getByPlaceholderText('Create a password'), { target: { value: 'Password123' } });
      fireEvent.change(screen.getByPlaceholderText('Confirm your password'), { target: { value: 'Password123' } });
      
      // Trigger email validation
      fireEvent.blur(emailInput);

      // Should show loading indicator during validation
      await waitFor(() => {
        expect(document.querySelector('.animate-spin')).toBeInTheDocument();
      });

      // Wait for validation to complete
      await waitFor(() => {
        expect(mockValidateEmail).toHaveBeenCalledWith(testEmail);
        expect(document.querySelector('.animate-spin')).not.toBeInTheDocument();
      });

      // Should show error message and login suggestion
      await waitFor(() => {
        expect(screen.getByText('This email is already registered.')).toBeInTheDocument();
        expect(screen.getByText('Sign in instead')).toBeInTheDocument();
      });

      // Submit button should be disabled
      expect(submitButton).toBeDisabled();

      // Should not be able to submit the form
      fireEvent.click(submitButton);
      expect(mockSignUpProps.onSuccess).not.toHaveBeenCalled();
    });

    it('should handle database connection errors gracefully in end-to-end flow', async () => {
      // Mock database connection error
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      mockValidateEmail.mockRejectedValue(
        new BackendApiError('Database connection failed', 0, 'NETWORK_ERROR')
      );

      const testEmail = '<EMAIL>';

      render(<SignUpPage {...mockSignUpProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      const submitButton = screen.getByRole('button', { name: /create account/i });
      
      // Fill out complete form
      fireEvent.change(screen.getByPlaceholderText('First name'), { target: { value: 'John' } });
      fireEvent.change(screen.getByPlaceholderText('Last name'), { target: { value: 'Doe' } });
      fireEvent.change(emailInput, { target: { value: testEmail } });
      fireEvent.change(screen.getByPlaceholderText('Create a password'), { target: { value: 'Password123' } });
      fireEvent.change(screen.getByPlaceholderText('Confirm your password'), { target: { value: 'Password123' } });
      
      // Trigger email validation
      fireEvent.blur(emailInput);

      // Wait for validation to complete with error
      await waitFor(() => {
        expect(mockValidateEmail).toHaveBeenCalledWith(testEmail);
        expect(screen.getByText(/Database connection failed/i)).toBeInTheDocument();
      });

      // Submit button should remain enabled (graceful degradation)
      expect(submitButton).not.toBeDisabled();

      // Should be able to submit the form despite validation error
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockSignUpProps.onSuccess).toHaveBeenCalledWith({
          firstName: 'John',
          lastName: 'Doe',
          email: testEmail,
          password: 'Password123',
          confirmPassword: 'Password123'
        });
      }, { timeout: 2000 });
    });
  });

  describe('Signup Prevention When Email Exists', () => {
    it('should prevent signup when email validation confirms email exists', async () => {
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      mockValidateEmail.mockResolvedValue({
        available: false,
        message: 'Email already exists in database'
      });

      const existingEmail = '<EMAIL>';

      render(<SignUpPage {...mockSignUpProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      const submitButton = screen.getByRole('button', { name: /create account/i });
      
      // Fill out all required fields
      fireEvent.change(screen.getByPlaceholderText('First name'), { target: { value: 'John' } });
      fireEvent.change(screen.getByPlaceholderText('Last name'), { target: { value: 'Doe' } });
      fireEvent.change(emailInput, { target: { value: existingEmail } });
      fireEvent.change(screen.getByPlaceholderText('Create a password'), { target: { value: 'ValidPass123' } });
      fireEvent.change(screen.getByPlaceholderText('Confirm your password'), { target: { value: 'ValidPass123' } });
      
      // Initially button should be enabled
      expect(submitButton).not.toBeDisabled();
      
      // Trigger email validation
      fireEvent.blur(emailInput);

      // Wait for validation to complete
      await waitFor(() => {
        expect(mockValidateEmail).toHaveBeenCalledWith(existingEmail);
      });

      // Button should now be disabled
      await waitFor(() => {
        expect(submitButton).toBeDisabled();
      });

      // Should show appropriate error message
      expect(screen.getByText('This email is already registered.')).toBeInTheDocument();

      // Attempting to click submit should not trigger onSuccess
      fireEvent.click(submitButton);
      expect(mockSignUpProps.onSuccess).not.toHaveBeenCalled();

      // Should show login suggestion
      expect(screen.getByText('Sign in instead')).toBeInTheDocument();
    });

    it('should re-enable signup when switching from taken email to available email', async () => {
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      
      render(<SignUpPage {...mockSignUpProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      const submitButton = screen.getByRole('button', { name: /create account/i });
      
      // Fill out all required fields
      fireEvent.change(screen.getByPlaceholderText('First name'), { target: { value: 'John' } });
      fireEvent.change(screen.getByPlaceholderText('Last name'), { target: { value: 'Doe' } });
      fireEvent.change(screen.getByPlaceholderText('Create a password'), { target: { value: 'ValidPass123' } });
      fireEvent.change(screen.getByPlaceholderText('Confirm your password'), { target: { value: 'ValidPass123' } });
      
      // First validation - email taken
      mockValidateEmail.mockResolvedValueOnce({
        available: false,
        message: 'Email already exists'
      });
      
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.blur(emailInput);

      await waitFor(() => {
        expect(submitButton).toBeDisabled();
      });

      // Second validation - email available
      mockValidateEmail.mockResolvedValueOnce({
        available: true,
        message: 'Email is available'
      });
      
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.blur(emailInput);

      await waitFor(() => {
        expect(submitButton).not.toBeDisabled();
      });

      // Should be able to submit now
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockSignUpProps.onSuccess).toHaveBeenCalled();
      }, { timeout: 2000 });
    });

    it('should maintain signup prevention across form interactions when email is taken', async () => {
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      mockValidateEmail.mockResolvedValue({
        available: false,
        message: 'Email already exists'
      });

      render(<SignUpPage {...mockSignUpProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      const submitButton = screen.getByRole('button', { name: /create account/i });
      const firstNameInput = screen.getByPlaceholderText('First name');
      
      // Fill out form with taken email
      fireEvent.change(firstNameInput, { target: { value: 'John' } });
      fireEvent.change(screen.getByPlaceholderText('Last name'), { target: { value: 'Doe' } });
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(screen.getByPlaceholderText('Create a password'), { target: { value: 'ValidPass123' } });
      fireEvent.change(screen.getByPlaceholderText('Confirm your password'), { target: { value: 'ValidPass123' } });
      
      // Trigger validation
      fireEvent.blur(emailInput);

      await waitFor(() => {
        expect(submitButton).toBeDisabled();
      });

      // Modify other fields - button should remain disabled
      fireEvent.change(firstNameInput, { target: { value: 'Jane' } });
      expect(submitButton).toBeDisabled();

      fireEvent.change(screen.getByPlaceholderText('Create a password'), { target: { value: 'NewPassword123' } });
      expect(submitButton).toBeDisabled();

      // Only changing email should reset the validation state
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      expect(submitButton).not.toBeDisabled(); // Should be enabled immediately when email changes
    });
  });

  describe('Successful Signup Flow When Email Available', () => {
    it('should complete successful signup flow when email is available', async () => {
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      mockValidateEmail.mockResolvedValue({
        available: true,
        message: 'Email is available for registration'
      });

      const availableEmail = '<EMAIL>';
      const formData = {
        firstName: 'Alice',
        lastName: 'Johnson',
        email: availableEmail,
        password: 'SecurePass123',
        confirmPassword: 'SecurePass123'
      };

      render(<SignUpPage {...mockSignUpProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      const submitButton = screen.getByRole('button', { name: /create account/i });
      
      // Fill out complete form
      fireEvent.change(screen.getByPlaceholderText('First name'), { target: { value: formData.firstName } });
      fireEvent.change(screen.getByPlaceholderText('Last name'), { target: { value: formData.lastName } });
      fireEvent.change(emailInput, { target: { value: formData.email } });
      fireEvent.change(screen.getByPlaceholderText('Create a password'), { target: { value: formData.password } });
      fireEvent.change(screen.getByPlaceholderText('Confirm your password'), { target: { value: formData.confirmPassword } });
      
      // Trigger email validation
      fireEvent.blur(emailInput);

      // Wait for validation to complete successfully
      await waitFor(() => {
        expect(mockValidateEmail).toHaveBeenCalledWith(availableEmail);
      });

      // Should not show any error messages
      expect(screen.queryByText(/already registered/i)).not.toBeInTheDocument();
      expect(screen.queryByText(/error/i)).not.toBeInTheDocument();

      // Submit button should be enabled
      expect(submitButton).not.toBeDisabled();

      // Submit the form
      fireEvent.click(submitButton);

      // Should show loading state
      await waitFor(() => {
        expect(screen.getByText('Creating Account...')).toBeInTheDocument();
        expect(submitButton).toBeDisabled();
      });

      // Should call onSuccess with correct form data after timeout
      await waitFor(() => {
        expect(mockSignUpProps.onSuccess).toHaveBeenCalledWith(formData);
      }, { timeout: 2000 });
    });

    it('should allow signup even when email validation fails due to network issues', async () => {
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      mockValidateEmail.mockRejectedValue(
        new BackendApiError('Network connection issue. You can retry by clicking the email field again, or proceed with signup.', 0, 'NETWORK_ERROR')
      );

      const testEmail = '<EMAIL>';
      const formData = {
        firstName: 'Bob',
        lastName: 'Wilson',
        email: testEmail,
        password: 'NetworkTest123',
        confirmPassword: 'NetworkTest123'
      };

      render(<SignUpPage {...mockSignUpProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      const submitButton = screen.getByRole('button', { name: /create account/i });
      
      // Fill out complete form
      fireEvent.change(screen.getByPlaceholderText('First name'), { target: { value: formData.firstName } });
      fireEvent.change(screen.getByPlaceholderText('Last name'), { target: { value: formData.lastName } });
      fireEvent.change(emailInput, { target: { value: formData.email } });
      fireEvent.change(screen.getByPlaceholderText('Create a password'), { target: { value: formData.password } });
      fireEvent.change(screen.getByPlaceholderText('Confirm your password'), { target: { value: formData.confirmPassword } });
      
      // Trigger email validation
      fireEvent.blur(emailInput);

      // Wait for validation to fail
      await waitFor(() => {
        expect(mockValidateEmail).toHaveBeenCalledWith(testEmail);
        expect(screen.getByText(/Network connection issue/i)).toBeInTheDocument();
      });

      // Submit button should remain enabled (graceful degradation)
      expect(submitButton).not.toBeDisabled();

      // Should be able to submit despite validation error
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockSignUpProps.onSuccess).toHaveBeenCalledWith(formData);
      }, { timeout: 2000 });
    });

    it('should handle multiple validation attempts before successful signup', async () => {
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      
      // First attempt fails, second succeeds
      mockValidateEmail
        .mockRejectedValueOnce(new BackendApiError('Temporary server error', 500))
        .mockResolvedValueOnce({ available: true, message: 'Email is available' });

      const testEmail = '<EMAIL>';

      render(<SignUpPage {...mockSignUpProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      const submitButton = screen.getByRole('button', { name: /create account/i });
      
      // Fill out form
      fireEvent.change(screen.getByPlaceholderText('First name'), { target: { value: 'Retry' } });
      fireEvent.change(screen.getByPlaceholderText('Last name'), { target: { value: 'User' } });
      fireEvent.change(emailInput, { target: { value: testEmail } });
      fireEvent.change(screen.getByPlaceholderText('Create a password'), { target: { value: 'RetryPass123' } });
      fireEvent.change(screen.getByPlaceholderText('Confirm your password'), { target: { value: 'RetryPass123' } });
      
      // First validation attempt
      fireEvent.blur(emailInput);

      await waitFor(() => {
        expect(screen.getByText(/server error/i)).toBeInTheDocument();
        expect(screen.getByText('Try again')).toBeInTheDocument();
      });

      // Retry validation
      const retryButton = screen.getByText('Try again');
      fireEvent.click(retryButton);

      // Wait for second validation to succeed
      await waitFor(() => {
        expect(mockValidateEmail).toHaveBeenCalledTimes(2);
        expect(screen.queryByText(/server error/i)).not.toBeInTheDocument();
        expect(screen.queryByText('Try again')).not.toBeInTheDocument();
      });

      // Should be able to submit successfully
      expect(submitButton).not.toBeDisabled();
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockSignUpProps.onSuccess).toHaveBeenCalled();
      }, { timeout: 2000 });
    });
  });

  describe('Navigation to Login Page with Pre-populated Email', () => {
    it('should navigate to login page with pre-populated email when clicking login suggestion', async () => {
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      mockValidateEmail.mockResolvedValue({
        available: false,
        message: 'Email already exists'
      });

      const existingEmail = '<EMAIL>';

      render(<SignUpPage {...mockSignUpProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      
      // Enter existing email and trigger validation
      fireEvent.change(emailInput, { target: { value: existingEmail } });
      fireEvent.blur(emailInput);

      // Wait for validation and login suggestion
      await waitFor(() => {
        expect(screen.getByText('Sign in instead')).toBeInTheDocument();
      });

      // Click login suggestion
      const loginSuggestion = screen.getByText('Sign in instead');
      fireEvent.click(loginSuggestion);

      // Should call onSignIn with the email
      expect(mockSignUpProps.onSignIn).toHaveBeenCalledWith(existingEmail);
    });

    it('should navigate to login page with pre-populated email when clicking bottom Sign In button', () => {
      const testEmail = '<EMAIL>';

      render(<SignUpPage {...mockSignUpProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      
      // Enter email
      fireEvent.change(emailInput, { target: { value: testEmail } });
      
      // Click bottom "Sign In Instead" button
      const signInButton = screen.getByRole('button', { name: /sign in instead/i });
      fireEvent.click(signInButton);

      // Should call onSignIn with the email
      expect(mockSignUpProps.onSignIn).toHaveBeenCalledWith(testEmail);
    });

    it('should complete full navigation flow from signup to login with email pre-population', async () => {
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      mockValidateEmail.mockResolvedValue({
        available: false,
        message: 'Email already exists'
      });

      const existingEmail = '<EMAIL>';

      // Render SignUpPage
      const { rerender } = render(<SignUpPage {...mockSignUpProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      
      // Enter existing email and trigger validation
      fireEvent.change(emailInput, { target: { value: existingEmail } });
      fireEvent.blur(emailInput);

      // Wait for validation and click login suggestion
      await waitFor(() => {
        expect(screen.getByText('Sign in instead')).toBeInTheDocument();
      });

      const loginSuggestion = screen.getByText('Sign in instead');
      fireEvent.click(loginSuggestion);

      // Verify onSignIn was called with email
      expect(mockSignUpProps.onSignIn).toHaveBeenCalledWith(existingEmail);

      // Simulate navigation to LoginPage with pre-populated email
      rerender(<LoginPage {...mockLoginProps} initialEmail={existingEmail} />);

      // Verify email is pre-populated in login form
      const loginEmailInput = screen.getByDisplayValue(existingEmail);
      expect(loginEmailInput).toBeInTheDocument();

      // Verify user can complete login flow
      const passwordInput = screen.getByPlaceholderText('Enter your password');
      const loginButton = screen.getByRole('button', { name: /sign in/i });

      fireEvent.change(passwordInput, { target: { value: 'password123' } });
      
      // Mock successful login
      const mockSignIn = vi.mocked(PersistentAuthService.signIn);
      mockSignIn.mockResolvedValue({ success: true });

      fireEvent.click(loginButton);

      await waitFor(() => {
        expect(mockSignIn).toHaveBeenCalledWith({
          email: existingEmail,
          password: 'password123'
        });
      });
    });

    it('should handle navigation with empty email gracefully', () => {
      render(<SignUpPage {...mockSignUpProps} />);
      
      // Click "Sign In Instead" without entering email
      const signInButton = screen.getByRole('button', { name: /sign in instead/i });
      fireEvent.click(signInButton);

      // Should call onSignIn with empty string
      expect(mockSignUpProps.onSignIn).toHaveBeenCalledWith('');

      // Simulate navigation to LoginPage without initialEmail
      cleanup(); // Clean up the previous render
      render(<LoginPage {...mockLoginProps} />);

      // Email field should be empty
      const loginEmailInput = screen.getByPlaceholderText('Enter your email') as HTMLInputElement;
      expect(loginEmailInput.value).toBe('');
    });

    it('should preserve email during multiple navigation attempts', async () => {
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      mockValidateEmail.mockResolvedValue({
        available: false,
        message: 'Email already exists'
      });

      const persistentEmail = '<EMAIL>';

      render(<SignUpPage {...mockSignUpProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      
      // Enter email and trigger validation
      fireEvent.change(emailInput, { target: { value: persistentEmail } });
      fireEvent.blur(emailInput);

      await waitFor(() => {
        expect(screen.getByText('Sign in instead')).toBeInTheDocument();
      });

      // Click login suggestion multiple times
      const loginSuggestion = screen.getByText('Sign in instead');
      fireEvent.click(loginSuggestion);
      fireEvent.click(loginSuggestion);

      // Should call onSignIn with same email each time
      expect(mockSignUpProps.onSignIn).toHaveBeenCalledTimes(2);
      expect(mockSignUpProps.onSignIn).toHaveBeenNthCalledWith(1, persistentEmail);
      expect(mockSignUpProps.onSignIn).toHaveBeenNthCalledWith(2, persistentEmail);

      // Also test bottom button (get all buttons and find the one that's not the inline suggestion)
      const signInButtons = screen.getAllByRole('button', { name: /sign in instead/i });
      const bottomSignInButton = signInButtons.find(button => 
        button.textContent === 'Sign In Instead'
      );
      fireEvent.click(bottomSignInButton!);

      expect(mockSignUpProps.onSignIn).toHaveBeenCalledTimes(3);
      expect(mockSignUpProps.onSignIn).toHaveBeenNthCalledWith(3, persistentEmail);
    });
  });

  describe('Complex Integration Scenarios', () => {
    it('should handle rapid email changes with validation and navigation', async () => {
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      
      render(<SignUpPage {...mockSignUpProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      
      // Rapidly change emails
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      
      // Mock validation for final email
      mockValidateEmail.mockResolvedValue({
        available: false,
        message: 'Email already exists'
      });
      
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.blur(emailInput);

      await waitFor(() => {
        expect(mockValidateEmail).toHaveBeenCalledWith('<EMAIL>');
        expect(screen.getByText('Sign in instead')).toBeInTheDocument();
      });

      // Navigate with final email
      const loginSuggestion = screen.getByText('Sign in instead');
      fireEvent.click(loginSuggestion);

      expect(mockSignUpProps.onSignIn).toHaveBeenCalledWith('<EMAIL>');
    });

    it('should handle validation errors followed by successful navigation', async () => {
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      
      // First validation fails with network error
      mockValidateEmail.mockRejectedValueOnce(
        new BackendApiError('Network error', 0, 'NETWORK_ERROR')
      );
      
      // Second validation succeeds but email is taken
      mockValidateEmail.mockResolvedValueOnce({
        available: false,
        message: 'Email already exists'
      });

      const testEmail = '<EMAIL>';

      render(<SignUpPage {...mockSignUpProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      
      // First validation attempt
      fireEvent.change(emailInput, { target: { value: testEmail } });
      fireEvent.blur(emailInput);

      await waitFor(() => {
        expect(screen.getByText(/Network error/i)).toBeInTheDocument();
        expect(screen.getByText('Try again')).toBeInTheDocument();
      });

      // Retry validation
      const retryButton = screen.getByText('Try again');
      fireEvent.click(retryButton);

      // Wait for second validation to complete
      await waitFor(() => {
        expect(mockValidateEmail).toHaveBeenCalledTimes(2);
        expect(screen.getByText('Sign in instead')).toBeInTheDocument();
      });

      // Navigate to login
      const loginSuggestion = screen.getByText('Sign in instead');
      fireEvent.click(loginSuggestion);

      expect(mockSignUpProps.onSignIn).toHaveBeenCalledWith(testEmail);
    });

    it('should maintain form state during validation and navigation cycles', async () => {
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      mockValidateEmail.mockResolvedValue({
        available: false,
        message: 'Email already exists'
      });

      const formData = {
        firstName: 'Persistent',
        lastName: 'User',
        email: '<EMAIL>',
        password: 'PersistentPass123',
        confirmPassword: 'PersistentPass123'
      };

      render(<SignUpPage {...mockSignUpProps} />);
      
      // Fill out complete form
      fireEvent.change(screen.getByPlaceholderText('First name'), { target: { value: formData.firstName } });
      fireEvent.change(screen.getByPlaceholderText('Last name'), { target: { value: formData.lastName } });
      fireEvent.change(screen.getByPlaceholderText('Enter your email'), { target: { value: formData.email } });
      fireEvent.change(screen.getByPlaceholderText('Create a password'), { target: { value: formData.password } });
      fireEvent.change(screen.getByPlaceholderText('Confirm your password'), { target: { value: formData.confirmPassword } });
      
      // Trigger validation
      fireEvent.blur(screen.getByPlaceholderText('Enter your email'));

      await waitFor(() => {
        expect(screen.getByText('Sign in instead')).toBeInTheDocument();
      });

      // Verify all form fields maintain their values
      expect((screen.getByPlaceholderText('First name') as HTMLInputElement).value).toBe(formData.firstName);
      expect((screen.getByPlaceholderText('Last name') as HTMLInputElement).value).toBe(formData.lastName);
      expect((screen.getByPlaceholderText('Enter your email') as HTMLInputElement).value).toBe(formData.email);
      expect((screen.getByPlaceholderText('Create a password') as HTMLInputElement).value).toBe(formData.password);
      expect((screen.getByPlaceholderText('Confirm your password') as HTMLInputElement).value).toBe(formData.confirmPassword);

      // Navigate to login
      const loginSuggestion = screen.getByText('Sign in instead');
      fireEvent.click(loginSuggestion);

      expect(mockSignUpProps.onSignIn).toHaveBeenCalledWith(formData.email);
    });
  });
});