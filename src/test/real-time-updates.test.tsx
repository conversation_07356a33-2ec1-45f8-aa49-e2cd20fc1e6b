import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { Closet } from '@/pages/Closet';
import { ClothingApiService } from '@/services/clothingApi';
import { useUserProfile } from '@/hooks/useUserProfile';

// Mock the dependencies
vi.mock('@/services/clothingApi');
vi.mock('@/hooks/useUserProfile');
vi.mock('@/components/ui/sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

// Mock the AddClothingModal component
vi.mock('@/components/AddClothingModal', () => ({
  AddClothingModal: ({ isOpen, onSave, onClose }: any) => {
    if (!isOpen) return null;
    
    return (
      <div data-testid="add-clothing-modal">
        <button
          data-testid="save-item-button"
          onClick={() => {
            // Simulate form data
            const mockFormData = {
              category: 'Shirt',
              color: 'Blue',
              brand: 'Test Brand',
              size: 'M',
              season: 'Summer',
              processedImageUrl: 'https://example.com/image.jpg'
            };
            onSave(mockFormData);
          }}
        >
          Save Item
        </button>
        <button data-testid="close-modal-button" onClick={onClose}>
          Close
        </button>
      </div>
    );
  },
}));

const mockUserProfile = {
  id: 'test-user-123',
  email: '<EMAIL>',
  name: 'Test User',
};

const mockClothingItems = [
  {
    id: 'existing-item-1',
    name: 'Existing Shirt',
    category: 'Shirt',
    color: 'Red',
    brand: 'Existing Brand',
    size: 'L',
    season: 'Winter',
    image_url: 'https://example.com/existing.jpg',
    user_id: 'test-user-123',
    category_id: '1',
    tags: ['Winter'],
    status: 'active',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    category_name: 'Shirt'
  }
];

const mockSavedItem = {
  id: 'new-item-123',
  name: 'Test Brand Shirt',
  category: 'Shirt',
  color: 'Blue',
  brand: 'Test Brand',
  size: 'M',
  season: 'Summer',
  image_url: 'https://example.com/image.jpg',
  user_id: 'test-user-123',
  category_id: '1',
  tags: ['Summer'],
  status: 'active',
  created_at: '2024-01-02T00:00:00Z',
  updated_at: '2024-01-02T00:00:00Z',
  category_name: 'Shirt'
};

describe('Real-time Updates', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock useUserProfile hook
    (useUserProfile as any).mockReturnValue({
      userProfile: mockUserProfile,
    });

    // Mock ClothingApiService methods
    (ClothingApiService.getClothingItems as any).mockResolvedValue(mockClothingItems);
    (ClothingApiService.saveClothingItem as any).mockResolvedValue(mockSavedItem);
  });

  it('should display items immediately after adding without page refresh', async () => {
    render(<Closet />);

    // Wait for initial items to load
    await waitFor(() => {
      expect(screen.getByText('Existing Shirt')).toBeInTheDocument();
    });

    // Verify only existing item is present initially
    expect(screen.getByText('Existing Shirt')).toBeInTheDocument();
    expect(screen.queryByText('Test Brand Shirt')).not.toBeInTheDocument();

    // Open the add modal
    const addButton = screen.getByRole('button', { name: /add new clothing item/i });
    fireEvent.click(addButton);

    // Wait for modal to appear
    await waitFor(() => {
      expect(screen.getByTestId('add-clothing-modal')).toBeInTheDocument();
    });

    // Save a new item
    const saveButton = screen.getByTestId('save-item-button');
    fireEvent.click(saveButton);

    // Verify optimistic update - item should appear immediately
    await waitFor(() => {
      expect(screen.getByText('Test Brand Shirt')).toBeInTheDocument();
    });

    // Verify the item appears with loading state initially
    const newItemCard = screen.getByText('Test Brand Shirt').closest('[data-testid]') || 
                       screen.getByText('Test Brand Shirt').closest('.glass-morphing');
    
    // The item should be visible immediately (optimistic update)
    expect(screen.getByText('Test Brand Shirt')).toBeInTheDocument();
    expect(screen.getByText('Blue')).toBeInTheDocument(); // Color
    expect(screen.getByText('Test Brand')).toBeInTheDocument(); // Brand

    // Verify API was called
    expect(ClothingApiService.saveClothingItem).toHaveBeenCalledWith({
      userId: 'test-user-123',
      name: 'Test Brand Shirt',
      category: 'Shirt',
      color: 'Blue',
      brand: 'Test Brand',
      size: 'M',
      season: 'Summer',
      processedImageUrl: 'https://example.com/image.jpg'
    });

    // Wait for API call to complete and optimistic item to be replaced with real item
    await waitFor(() => {
      // The item should still be there but now with the real ID
      expect(screen.getByText('Test Brand Shirt')).toBeInTheDocument();
    });

    // Verify both items are now present
    expect(screen.getByText('Existing Shirt')).toBeInTheDocument();
    expect(screen.getByText('Test Brand Shirt')).toBeInTheDocument();

    // Verify modal is closed
    expect(screen.queryByTestId('add-clothing-modal')).not.toBeInTheDocument();
  });

  it('should handle API errors gracefully with rollback', async () => {
    // Mock API to fail
    (ClothingApiService.saveClothingItem as any).mockRejectedValue(new Error('Network error'));

    render(<Closet />);

    // Wait for initial items to load
    await waitFor(() => {
      expect(screen.getByText('Existing Shirt')).toBeInTheDocument();
    });

    // Open the add modal
    const addButton = screen.getByRole('button', { name: /add new clothing item/i });
    fireEvent.click(addButton);

    // Wait for modal to appear
    await waitFor(() => {
      expect(screen.getByTestId('add-clothing-modal')).toBeInTheDocument();
    });

    // Save a new item (this will fail)
    const saveButton = screen.getByTestId('save-item-button');
    fireEvent.click(saveButton);

    // Verify optimistic update happens first
    await waitFor(() => {
      expect(screen.getByText('Test Brand Shirt')).toBeInTheDocument();
    });

    // Wait for API error and rollback
    await waitFor(() => {
      // Item should be removed after API failure
      expect(screen.queryByText('Test Brand Shirt')).not.toBeInTheDocument();
    });

    // Verify modal is reopened for retry
    await waitFor(() => {
      expect(screen.getByTestId('add-clothing-modal')).toBeInTheDocument();
    });

    // Only existing item should remain
    expect(screen.getByText('Existing Shirt')).toBeInTheDocument();
    expect(screen.queryByText('Test Brand Shirt')).not.toBeInTheDocument();
  });

  it('should show loading state for optimistic items', async () => {
    // Delay the API response to test loading state
    (ClothingApiService.saveClothingItem as any).mockImplementation(
      () => new Promise(resolve => setTimeout(() => resolve(mockSavedItem), 1000))
    );

    render(<Closet />);

    // Wait for initial load
    await waitFor(() => {
      expect(screen.getByText('Existing Shirt')).toBeInTheDocument();
    });

    // Open modal and save item
    const addButton = screen.getByRole('button', { name: /add new clothing item/i });
    fireEvent.click(addButton);

    await waitFor(() => {
      expect(screen.getByTestId('add-clothing-modal')).toBeInTheDocument();
    });

    const saveButton = screen.getByTestId('save-item-button');
    fireEvent.click(saveButton);

    // Verify optimistic item appears with loading state
    await waitFor(() => {
      expect(screen.getByText('Test Brand Shirt')).toBeInTheDocument();
    });

    // Check for loading spinner (the optimistic item should have reduced opacity)
    const itemCard = screen.getByText('Test Brand Shirt').closest('.glass-morphing');
    expect(itemCard).toHaveClass('opacity-75'); // Optimistic items have reduced opacity
  });
});
