import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { SignUpPage } from '@/pages/SignUpPage';
import { authApi, BackendApiError } from '@/services/backendApi';

// Mock the authApi
vi.mock('@/services/backendApi', () => ({
  authApi: {
    validateEmail: vi.fn()
  },
  BackendApiError: class extends Error {
    constructor(message: string, public status?: number, public code?: string) {
      super(message);
      this.name = 'BackendApiError';
    }
  }
}));

describe('Email Validation Error Handling Integration', () => {
  const mockProps = {
    onBack: vi.fn(),
    onSignIn: vi.fn(),
    onSuccess: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should handle complete error recovery flow', async () => {
    const mockValidateEmail = vi.mocked(authApi.validateEmail);
    
    // First call fails with network error, second succeeds
    mockValidateEmail
      .mockRejectedValueOnce(new BackendApiError('Network connection issue. You can retry by clicking the email field again, or proceed with signup.', 0, 'NETWORK_ERROR'))
      .mockResolvedValueOnce({ available: true, message: 'Email is available' });

    render(<SignUpPage {...mockProps} />);
    
    const emailInput = screen.getByPlaceholderText('Enter your email');
    const submitButton = screen.getByRole('button', { name: /create account/i });
    
    // Fill out the form completely
    fireEvent.change(screen.getByPlaceholderText('First name'), { target: { value: 'John' } });
    fireEvent.change(screen.getByPlaceholderText('Last name'), { target: { value: 'Doe' } });
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(screen.getByPlaceholderText('Create a password'), { target: { value: 'Password123' } });
    fireEvent.change(screen.getByPlaceholderText('Confirm your password'), { target: { value: 'Password123' } });
    
    // Trigger validation
    fireEvent.blur(emailInput);

    // Should show network error but allow signup
    await waitFor(() => {
      expect(screen.getByText(/Network connection issue/)).toBeInTheDocument();
    });

    // Signup button should remain enabled (graceful degradation)
    expect(submitButton).not.toBeDisabled();

    // Should show retry button
    expect(screen.getByText('Try again')).toBeInTheDocument();

    // Click retry button
    const retryButton = screen.getByText('Try again');
    fireEvent.click(retryButton);

    // Should call validateEmail again and succeed
    await waitFor(() => {
      expect(mockValidateEmail).toHaveBeenCalledTimes(2);
    });

    // Error message should be cleared
    await waitFor(() => {
      expect(screen.queryByText(/Network connection issue/)).not.toBeInTheDocument();
      expect(screen.queryByText('Try again')).not.toBeInTheDocument();
    });

    // Signup button should still be enabled
    expect(submitButton).not.toBeDisabled();
  });

  it('should handle timeout errors with graceful degradation', async () => {
    const mockValidateEmail = vi.mocked(authApi.validateEmail);
    mockValidateEmail.mockRejectedValue(new BackendApiError('Validation timed out. You can retry by clicking the email field again, or proceed with signup.', 0, 'VALIDATION_TIMEOUT'));

    render(<SignUpPage {...mockProps} />);
    
    const emailInput = screen.getByPlaceholderText('Enter your email');
    const submitButton = screen.getByRole('button', { name: /create account/i });
    
    // Fill out the form completely
    fireEvent.change(screen.getByPlaceholderText('First name'), { target: { value: 'John' } });
    fireEvent.change(screen.getByPlaceholderText('Last name'), { target: { value: 'Doe' } });
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(screen.getByPlaceholderText('Create a password'), { target: { value: 'Password123' } });
    fireEvent.change(screen.getByPlaceholderText('Confirm your password'), { target: { value: 'Password123' } });
    
    // Trigger validation
    fireEvent.blur(emailInput);

    // Should show timeout error but allow signup
    await waitFor(() => {
      expect(screen.getByText(/timed out/i)).toBeInTheDocument();
    });

    // Signup button should remain enabled (graceful degradation)
    expect(submitButton).not.toBeDisabled();

    // Should be able to submit the form despite the timeout
    // The button should be clickable (not disabled)
    expect(submitButton).not.toBeDisabled();
    
    // Verify that the form can be submitted by checking the button is enabled
    fireEvent.click(submitButton);
    
    // The form should start processing (button becomes disabled during loading)
    await waitFor(() => {
      expect(submitButton).toBeDisabled();
    });
  });

  it('should handle server errors with graceful degradation', async () => {
    const mockValidateEmail = vi.mocked(authApi.validateEmail);
    mockValidateEmail.mockRejectedValue(new BackendApiError('Validation service temporarily unavailable. You can retry by clicking the email field again, or proceed with signup.', 500));

    render(<SignUpPage {...mockProps} />);
    
    const emailInput = screen.getByPlaceholderText('Enter your email');
    const submitButton = screen.getByRole('button', { name: /create account/i });
    
    // Fill out the form completely
    fireEvent.change(screen.getByPlaceholderText('First name'), { target: { value: 'John' } });
    fireEvent.change(screen.getByPlaceholderText('Last name'), { target: { value: 'Doe' } });
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(screen.getByPlaceholderText('Create a password'), { target: { value: 'Password123' } });
    fireEvent.change(screen.getByPlaceholderText('Confirm your password'), { target: { value: 'Password123' } });
    
    // Trigger validation
    fireEvent.blur(emailInput);

    // Should show server error but allow signup
    await waitFor(() => {
      expect(screen.getByText(/temporarily unavailable/i)).toBeInTheDocument();
    });

    // Signup button should remain enabled (graceful degradation)
    expect(submitButton).not.toBeDisabled();

    // Should be able to submit the form despite the server error
    // The button should be clickable (not disabled)
    expect(submitButton).not.toBeDisabled();
    
    // Verify that the form can be submitted by checking the button is enabled
    // and the error doesn't prevent form submission
    fireEvent.click(submitButton);
    
    // The form should start processing (button becomes disabled during loading)
    await waitFor(() => {
      expect(submitButton).toBeDisabled();
    });
  });

  it('should allow retry by re-focusing email field after error', async () => {
    const mockValidateEmail = vi.mocked(authApi.validateEmail);
    
    // First call fails, second succeeds
    mockValidateEmail
      .mockRejectedValueOnce(new BackendApiError('Network connection issue. You can retry by clicking the email field again, or proceed with signup.', 0, 'NETWORK_ERROR'))
      .mockResolvedValueOnce({ available: true, message: 'Email is available' });

    render(<SignUpPage {...mockProps} />);
    
    const emailInput = screen.getByPlaceholderText('Enter your email');
    
    // Type email and blur
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.blur(emailInput);

    // Wait for error message
    await waitFor(() => {
      expect(screen.getByText(/Network connection issue/)).toBeInTheDocument();
    });

    // Re-focus the email field (simulating user clicking on it again)
    fireEvent.focus(emailInput);

    // Error message should be cleared
    await waitFor(() => {
      expect(screen.queryByText(/Network connection issue/)).not.toBeInTheDocument();
    });

    // Blur again to trigger retry
    fireEvent.blur(emailInput);

    // Should call validateEmail again and succeed
    await waitFor(() => {
      expect(mockValidateEmail).toHaveBeenCalledTimes(2);
    });

    // No error message should be shown
    expect(screen.queryByText(/Network connection issue/)).not.toBeInTheDocument();
  });
});