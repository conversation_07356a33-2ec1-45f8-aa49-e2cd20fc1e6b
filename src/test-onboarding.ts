/**
 * Test script to validate the complete onboarding flow
 * This can be run in the browser console to test the functionality
 */

import PersistentAuthService from './services/persistentAuth';
import { checkBackendHealth } from './services/backendApi';

export async function testOnboardingFlow() {
  console.log('🧪 Starting onboarding flow test...');

  try {
    // Initialize database
    console.log('📦 Initializing database...');
    await initializeDatabase();
    console.log('✅ Database initialized');

    // Test data
    const testUser = {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      password: 'TestPassword123!',
      gender: 'Male' as const,
      dateOfBirth: '1990-01-01',
      cityName: 'New York, NY, USA',
      latitude: 40.7128,
      longitude: -74.0060,
      weatherPreferences: {}
    };

    // Test email validation
    console.log('📧 Testing email validation...');
    const validEmail = AuthService.validateEmail(testUser.email);
    console.log(`Email validation result: ${validEmail ? '✅' : '❌'}`);

    // Test password validation
    console.log('🔒 Testing password validation...');
    const passwordValidation = AuthService.validatePassword(testUser.password);
    console.log(`Password validation result: ${passwordValidation.isValid ? '✅' : '❌'}`);
    if (!passwordValidation.isValid) {
      console.log('Password errors:', passwordValidation.errors);
    }

    // Test user registration
    console.log('👤 Testing user registration...');
    const registrationResult = await AuthService.register(
      { email: testUser.email, password: testUser.password },
      {
        firstName: testUser.firstName,
        lastName: testUser.lastName,
        gender: testUser.gender,
        dateOfBirth: testUser.dateOfBirth,
        cityName: testUser.cityName,
        latitude: testUser.latitude,
        longitude: testUser.longitude,
        weatherPreferences: testUser.weatherPreferences
      }
    );

    if (registrationResult.success) {
      console.log('✅ User registration successful');
      console.log('User ID:', registrationResult.user?.id);
    } else {
      console.log('❌ User registration failed:', registrationResult.error);
      return;
    }

    // Test user sign in
    console.log('🔑 Testing user sign in...');
    const signInResult = await AuthService.signIn({
      email: testUser.email,
      password: testUser.password
    });

    if (signInResult.success) {
      console.log('✅ User sign in successful');
      console.log('User:', signInResult.user?.firstName, signInResult.user?.lastName);
    } else {
      console.log('❌ User sign in failed:', signInResult.error);
      return;
    }

    // Test getting current user
    console.log('👥 Testing get current user...');
    if (signInResult.user) {
      AuthService.setCurrentUser(signInResult.user);
    }
    const currentUser = await AuthService.getCurrentUser();
    
    if (currentUser) {
      console.log('✅ Current user retrieved successfully');
      console.log('Current user:', currentUser.firstName, currentUser.lastName);
    } else {
      console.log('❌ Failed to retrieve current user');
    }

    // Test profile data persistence
    console.log('💾 Testing profile data persistence...');
    const retrievedProfile = await UserProfileService.getCurrentUserProfile();
    
    if (retrievedProfile) {
      console.log('✅ Profile data persisted successfully');
      console.log('Profile:', {
        name: `${retrievedProfile.firstName} ${retrievedProfile.lastName}`,
        email: retrievedProfile.email,
        city: retrievedProfile.cityName,
        gender: retrievedProfile.gender
      });
    } else {
      console.log('❌ Failed to retrieve profile data');
    }

    // Test sign out
    console.log('🚪 Testing user sign out...');
    await AuthService.signOut();
    const userAfterSignOut = await AuthService.getCurrentUser();
    
    if (!userAfterSignOut) {
      console.log('✅ User sign out successful');
    } else {
      console.log('❌ User sign out failed - user still logged in');
    }

    console.log('🎉 All tests completed successfully!');
    return true;

  } catch (error) {
    console.error('❌ Test failed with error:', error);
    return false;
  }
}

// Test individual components
export async function testPasswordValidation() {
  console.log('🔒 Testing password validation...');
  
  const testPasswords = [
    'weak',
    'StrongerPassword',
    'StrongPassword123',
    'VeryStrongPassword123!',
    'short'
  ];

  testPasswords.forEach(password => {
    const result = AuthService.validatePassword(password);
    console.log(`Password: "${password}" - ${result.isValid ? '✅' : '❌'}`);
    if (!result.isValid) {
      console.log('  Errors:', result.errors);
    }
  });
}

export async function testEmailValidation() {
  console.log('📧 Testing email validation...');
  
  const testEmails = [
    '<EMAIL>',
    'invalid.email',
    '<EMAIL>',
    '@invalid.com',
    '<EMAIL>'
  ];

  testEmails.forEach(email => {
    const result = PersistentAuthService.validateEmail(email);
    console.log(`Email: "${email}" - ${result ? '✅' : '❌'}`);
  });
}

// Make functions available globally for browser console testing
if (typeof window !== 'undefined') {
  (window as any).testOnboardingFlow = testOnboardingFlow;
  (window as any).testPasswordValidation = testPasswordValidation;
  (window as any).testEmailValidation = testEmailValidation;
}
