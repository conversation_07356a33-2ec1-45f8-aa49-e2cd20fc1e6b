// User Profile Types
export interface UserProfile {
  id?: string;
  firstName: string;
  lastName: string;
  gender: 'Male' | 'Female' | 'Other' | 'Prefer not to say';
  dateOfBirth: string; // ISO date string
  cityName: string;
  latitude: number;
  longitude: number;
  weatherPreferences?: WeatherPreferences;
  createdAt?: string;
  updatedAt?: string;
}

export interface WeatherPreferences {
  temperatureUnit: 'celsius' | 'fahrenheit';
  showHumidity: boolean;
  showWindSpeed: boolean;
  preferredWeatherSource: string;
}

// Form validation types
export interface UserProfileFormData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
  gender: string;
  dateOfBirth: string;
  cityName: string;
  selectedCity?: CitySearchResult;
}

export interface UserProfileFormErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  password?: string;
  confirmPassword?: string;
  gender?: string;
  dateOfBirth?: string;
  cityName?: string;
  general?: string;
}

// API Response Types
export interface CitySearchResult {
  place_id: number;
  licence: string;
  osm_type: string;
  osm_id: number;
  boundingbox: string[];
  lat: string;
  lon: string;
  display_name: string;
  class: string;
  type: string;
  importance: number;
  icon?: string;
  // Parsed fields for easier use
  city: string;
  state?: string;
  country: string;
  formattedName: string;
}

export interface WeatherData {
  coord: {
    lon: number;
    lat: number;
  };
  weather: Array<{
    id: number;
    main: string;
    description: string;
    icon: string;
  }>;
  base: string;
  main: {
    temp: number;
    feels_like: number;
    temp_min: number;
    temp_max: number;
    pressure: number;
    humidity: number;
  };
  visibility: number;
  wind: {
    speed: number;
    deg: number;
    gust?: number;
  };
  clouds: {
    all: number;
  };
  dt: number;
  sys: {
    type: number;
    id: number;
    country: string;
    sunrise: number;
    sunset: number;
  };
  timezone: number;
  id: number;
  name: string;
  cod: number;
}

// Database Schema Types
export interface UserProfileTable {
  id?: string | number; // Dexie auto-increment returns number, but we convert to string
  firstName: string;
  lastName: string;
  email: string;
  passwordHash: string;
  gender: string;
  dateOfBirth: string;
  cityName: string;
  latitude: number;
  longitude: number;
  weatherPreferences: string; // JSON string
  createdAt: string;
  updatedAt: string;
}

// API Error Types
export interface ApiError {
  message: string;
  code?: string;
  status?: number;
  details?: any;
}

// Authentication Types
export interface AuthCredentials {
  email: string;
  password: string;
}

export interface AuthResult {
  success: boolean;
  user?: UserProfile;
  error?: string;
}

export interface PasswordValidation {
  isValid: boolean;
  errors: string[];
}

// File Upload Types
export interface FileUploadResult {
  success: boolean;
  file?: File;
  url?: string;
  error?: string;
}

export interface FileValidationOptions {
  maxSize: number; // in bytes
  allowedTypes: string[];
  maxWidth?: number;
  maxHeight?: number;
}
