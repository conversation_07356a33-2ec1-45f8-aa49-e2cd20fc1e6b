import { getApiConfig } from './backendApi';

export interface ClothingItemSummary {
  id: string;
  name: string;
  color: string;
  brand?: string;
  image_url?: string;
  category_name?: string;
}

export interface ScheduledOutfit {
  id: string;
  user_id: string;
  outfit_id?: string;
  scheduled_date: string;
  notes?: string;
  weather_forecast?: any;
  is_worn: boolean;
  worn_at?: string;
  created_at: string;
  updated_at: string;
  outfit_name?: string;
  outfit_description?: string;
  outfit_image_url?: string;
  clothing_items?: ClothingItemSummary[];
}

export interface CreateScheduledOutfitRequest {
  userId: string;
  outfitId?: string;
  scheduledDate: string;
  notes?: string;
  weatherForecast?: any;
}

export class ScheduleApiService {
  private static getBaseUrl() {
    return getApiConfig().baseUrl;
  }

  /**
   * Get scheduled outfits for a user
   */
  static async getScheduledOutfits(
    userId: string,
    startDate?: string,
    endDate?: string
  ): Promise<ScheduledOutfit[]> {
    try {
      let url = `${this.getBaseUrl()}/schedule/${userId}`;

      if (startDate && endDate) {
        url += `?startDate=${startDate}&endDate=${endDate}`;
      }

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('Error fetching scheduled outfits:', error);
      throw error;
    }
  }

  /**
   * Create or update a scheduled outfit
   */
  static async saveScheduledOutfit(request: CreateScheduledOutfitRequest): Promise<ScheduledOutfit> {
    try {
      const response = await fetch(`${this.getBaseUrl()}/schedule`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('Error saving scheduled outfit:', error);
      throw error;
    }
  }

  /**
   * Delete a scheduled outfit
   */
  static async deleteScheduledOutfit(id: string): Promise<void> {
    try {
      const response = await fetch(`${this.getBaseUrl()}/schedule/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }
    } catch (error) {
      console.error('Error deleting scheduled outfit:', error);
      throw error;
    }
  }

  /**
   * Mark a scheduled outfit as worn/unworn
   */
  static async updateWornStatus(id: string, isWorn: boolean): Promise<ScheduledOutfit> {
    try {
      const response = await fetch(`${this.getBaseUrl()}/schedule/${id}/worn`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isWorn }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('Error updating worn status:', error);
      throw error;
    }
  }

  /**
   * Get scheduled outfits for a specific date range (helper method)
   */
  static async getScheduledOutfitsForMonth(userId: string, year: number, month: number): Promise<ScheduledOutfit[]> {
    const startDate = new Date(year, month, 1).toISOString().split('T')[0];
    const endDate = new Date(year, month + 1, 0).toISOString().split('T')[0];

    return this.getScheduledOutfits(userId, startDate, endDate);
  }

  /**
   * Get scheduled outfit for a specific date
   */
  static async getScheduledOutfitForDate(userId: string, date: string): Promise<ScheduledOutfit | null> {
    try {
      const outfits = await this.getScheduledOutfits(userId, date, date);
      return outfits.length > 0 ? outfits[0] : null;
    } catch (error) {
      console.error('Error fetching scheduled outfit for date:', error);
      throw error;
    }
  }
}
