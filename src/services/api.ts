import { CitySearchResult, WeatherData } from '@/types/user';
import { env } from '@/config/environment';

// Custom ApiError class
export class ApiError extends Error {
  constructor(
    message: string,
    public code?: string,
    public status?: number,
    public details?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// Rate limiting and caching utilities
class ApiCache {
  private cache = new Map<string, { data: any; timestamp: number }>();
  private readonly cacheDuration = env.apiCacheDuration;

  get(key: string): any | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheDuration) {
      return cached.data;
    }
    this.cache.delete(key);
    return null;
  }

  set(key: string, data: any): void {
    this.cache.set(key, { data, timestamp: Date.now() });
  }

  clear(): void {
    this.cache.clear();
  }
}

class RateLimiter {
  private requests = new Map<string, number[]>();
  private readonly maxRequests = env.apiRateLimitRequests;
  private readonly windowMs = env.apiRateLimitWindow;

  canMakeRequest(endpoint: string): boolean {
    const now = Date.now();
    const requests = this.requests.get(endpoint) || [];
    
    // Remove old requests outside the window
    const validRequests = requests.filter(time => now - time < this.windowMs);
    
    if (validRequests.length >= this.maxRequests) {
      return false;
    }

    validRequests.push(now);
    this.requests.set(endpoint, validRequests);
    return true;
  }
}

const apiCache = new ApiCache();
const rateLimiter = new RateLimiter();

// Debounce utility
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Generic API request handler
async function apiRequest<T>(
  url: string,
  options: RequestInit = {},
  cacheKey?: string
): Promise<T> {
  // Check cache first
  if (cacheKey) {
    const cached = apiCache.get(cacheKey);
    if (cached) {
      return cached;
    }
  }

  // Check rate limit
  const endpoint = new URL(url).hostname;
  if (!rateLimiter.canMakeRequest(endpoint)) {
    throw new ApiError('Rate limit exceeded. Please try again later.');
  }

  try {
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      throw new ApiError(
        `API request failed: ${response.status} ${response.statusText}`,
        response.status.toString(),
        response.status
      );
    }

    const data = await response.json();

    // Cache successful response
    if (cacheKey) {
      apiCache.set(cacheKey, data);
    }

    return data;
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError(
      error instanceof Error ? error.message : 'Unknown API error occurred'
    );
  }
}

// City search using Nominatim API
export async function searchCities(query: string): Promise<CitySearchResult[]> {
  if (!query || query.length < 2) {
    return [];
  }

  const baseUrl = env.nominatimApiUrl;
  const url = `${baseUrl}/search?q=${encodeURIComponent(query)}&format=json&addressdetails=1&limit=10&featuretype=city`;
  
  const cacheKey = `cities:${query.toLowerCase()}`;

  try {
    const results = await apiRequest<any[]>(url, {}, cacheKey);
    
    return results.map((result): CitySearchResult => {
      const address = result.address || {};
      const city = address.city || address.town || address.village || result.name;
      const state = address.state || address.province || address.region;
      const country = address.country;
      
      let formattedName = city;
      if (state) formattedName += `, ${state}`;
      if (country) formattedName += `, ${country}`;

      return {
        place_id: result.place_id,
        licence: result.licence,
        osm_type: result.osm_type,
        osm_id: result.osm_id,
        boundingbox: result.boundingbox,
        lat: result.lat,
        lon: result.lon,
        display_name: result.display_name,
        class: result.class,
        type: result.type,
        importance: result.importance,
        icon: result.icon,
        city,
        state,
        country,
        formattedName,
      };
    });
  } catch (error) {
    console.error('City search error:', error);
    throw new ApiError('Failed to search cities. Please check your internet connection.');
  }
}

// Weather data using OpenWeatherMap API
export async function getWeatherData(lat: number, lon: number): Promise<WeatherData> {
  const apiKey = env.openWeatherApiKey;
  const baseUrl = env.openWeatherApiUrl;

  console.log('Weather API Debug:', {
    hasApiKey: !!apiKey,
    hasBaseUrl: !!baseUrl,
    lat,
    lon,
    apiKeyLength: apiKey?.length
  });

  if (!apiKey) {
    throw new ApiError('OpenWeather API key not configured');
  }

  if (!baseUrl) {
    throw new ApiError('OpenWeather API URL not configured');
  }

  const url = `${baseUrl}/weather?lat=${lat}&lon=${lon}&appid=${apiKey}&units=metric`;
  const cacheKey = `weather:${lat}:${lon}`;

  console.log('Weather API URL:', url.replace(apiKey, 'HIDDEN_API_KEY'));

  try {
    // Use a more direct fetch approach for weather API
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Weather API HTTP Error:', {
        status: response.status,
        statusText: response.statusText,
        body: errorText
      });

      if (response.status === 401) {
        throw new ApiError('Invalid OpenWeather API key');
      } else if (response.status === 404) {
        throw new ApiError('Location not found');
      } else {
        throw new ApiError(`Weather API error: ${response.status} ${response.statusText}`);
      }
    }

    const data = await response.json();
    console.log('Weather API Success:', { city: data.name, temp: data.main?.temp });

    // Cache the successful response
    apiCache.set(cacheKey, data);

    return data;
  } catch (error) {
    console.error('Weather fetch error:', error);

    if (error instanceof ApiError) {
      throw error;
    }

    // Check if it's a network error
    if (error instanceof TypeError && error.message.includes('fetch')) {
      throw new ApiError('Network error: Unable to connect to weather service. Please check your internet connection.');
    }

    throw new ApiError('Failed to fetch weather data. Please try again later.');
  }
}

// Clear all caches (useful for testing or user logout)
export function clearApiCache(): void {
  apiCache.clear();
}
