import { FileUploadResult, FileValidationOptions } from '@/types/user';

// Default validation options
const DEFAULT_VALIDATION: FileValidationOptions = {
  maxSize: parseInt(import.meta.env.VITE_MAX_FILE_SIZE) || 5242880, // 5MB
  allowedTypes: (import.meta.env.VITE_ALLOWED_FILE_TYPES || 'image/jpeg,image/png,image/webp,image/heic').split(','),
  maxWidth: parseInt(import.meta.env.VITE_MAX_IMAGE_WIDTH) || 2048,
  maxHeight: parseInt(import.meta.env.VITE_MAX_IMAGE_HEIGHT) || 2048,
};

// File validation function
export function validateFile(file: File, options: Partial<FileValidationOptions> = {}): { isValid: boolean; error?: string } {
  const validation = { ...DEFAULT_VALIDATION, ...options };

  // Check file size
  if (file.size > validation.maxSize) {
    const maxSizeMB = (validation.maxSize / (1024 * 1024)).toFixed(1);
    return {
      isValid: false,
      error: `File size must be less than ${maxSizeMB}MB`
    };
  }

  // Check file type
  if (!validation.allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: 'File type not supported. Please use JPEG, PNG, WebP, or HEIC images.'
    };
  }

  return { isValid: true };
}

// Resize image if needed
export function resizeImage(file: File, maxWidth: number, maxHeight: number, quality: number = 0.8): Promise<File> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // Calculate new dimensions
      let { width, height } = img;
      
      if (width > maxWidth || height > maxHeight) {
        const ratio = Math.min(maxWidth / width, maxHeight / height);
        width *= ratio;
        height *= ratio;
      }

      // Set canvas dimensions
      canvas.width = width;
      canvas.height = height;

      // Draw and compress image
      ctx?.drawImage(img, 0, 0, width, height);
      
      canvas.toBlob(
        (blob) => {
          if (blob) {
            const resizedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now(),
            });
            resolve(resizedFile);
          } else {
            reject(new Error('Failed to resize image'));
          }
        },
        file.type,
        quality
      );
    };

    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(file);
  });
}

// File picker service
export class FilePickerService {
  
  // Open camera for photo capture (web implementation)
  static async captureFromCamera(): Promise<FileUploadResult> {
    try {
      // Check if getUserMedia is supported
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        return {
          success: false,
          error: 'Camera access is not supported in this browser'
        };
      }

      // Request camera access
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: { 
          facingMode: 'environment', // Use back camera if available
          width: { ideal: 1920 },
          height: { ideal: 1080 }
        } 
      });

      // Create video element to show camera feed
      const video = document.createElement('video');
      video.srcObject = stream;
      video.autoplay = true;
      video.playsInline = true;

      // Create canvas for capturing
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      // Wait for video to be ready
      await new Promise((resolve) => {
        video.onloadedmetadata = resolve;
      });

      // Set canvas size to video size
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // Capture frame
      ctx?.drawImage(video, 0, 0);

      // Stop camera stream
      stream.getTracks().forEach(track => track.stop());

      // Convert to blob and file
      return new Promise((resolve) => {
        canvas.toBlob((blob) => {
          if (blob) {
            const file = new File([blob], `camera-capture-${Date.now()}.jpg`, {
              type: 'image/jpeg',
              lastModified: Date.now(),
            });

            const validation = validateFile(file);
            if (!validation.isValid) {
              resolve({
                success: false,
                error: validation.error
              });
              return;
            }

            resolve({
              success: true,
              file,
              url: URL.createObjectURL(file)
            });
          } else {
            resolve({
              success: false,
              error: 'Failed to capture image'
            });
          }
        }, 'image/jpeg', 0.8);
      });

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Camera access denied'
      };
    }
  }

  // Open file picker for gallery selection
  static async selectFromGallery(): Promise<FileUploadResult> {
    return new Promise((resolve) => {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = DEFAULT_VALIDATION.allowedTypes.join(',');
      input.multiple = false;

      input.onchange = async (event) => {
        const target = event.target as HTMLInputElement;
        const file = target.files?.[0];

        if (!file) {
          resolve({
            success: false,
            error: 'No file selected'
          });
          return;
        }

        const validation = validateFile(file);
        if (!validation.isValid) {
          resolve({
            success: false,
            error: validation.error
          });
          return;
        }

        try {
          // Resize image if needed
          const processedFile = await resizeImage(
            file, 
            DEFAULT_VALIDATION.maxWidth!, 
            DEFAULT_VALIDATION.maxHeight!
          );

          resolve({
            success: true,
            file: processedFile,
            url: URL.createObjectURL(processedFile)
          });
        } catch (error) {
          resolve({
            success: false,
            error: 'Failed to process image'
          });
        }
      };

      input.oncancel = () => {
        resolve({
          success: false,
          error: 'File selection cancelled'
        });
      };

      // Trigger file picker
      input.click();
    });
  }

  // Open file system browser
  static async browseFiles(): Promise<FileUploadResult> {
    return new Promise((resolve) => {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = 'image/*';
      input.multiple = false;

      input.onchange = async (event) => {
        const target = event.target as HTMLInputElement;
        const file = target.files?.[0];

        if (!file) {
          resolve({
            success: false,
            error: 'No file selected'
          });
          return;
        }

        const validation = validateFile(file);
        if (!validation.isValid) {
          resolve({
            success: false,
            error: validation.error
          });
          return;
        }

        try {
          // Resize image if needed
          const processedFile = await resizeImage(
            file, 
            DEFAULT_VALIDATION.maxWidth!, 
            DEFAULT_VALIDATION.maxHeight!
          );

          resolve({
            success: true,
            file: processedFile,
            url: URL.createObjectURL(processedFile)
          });
        } catch (error) {
          resolve({
            success: false,
            error: 'Failed to process image'
          });
        }
      };

      input.oncancel = () => {
        resolve({
          success: false,
          error: 'File selection cancelled'
        });
      };

      // Trigger file picker
      input.click();
    });
  }

  // Handle file drop
  static async handleFileDrop(event: DragEvent): Promise<FileUploadResult> {
    event.preventDefault();
    
    const files = event.dataTransfer?.files;
    const file = files?.[0];

    if (!file) {
      return {
        success: false,
        error: 'No file dropped'
      };
    }

    const validation = validateFile(file);
    if (!validation.isValid) {
      return {
        success: false,
        error: validation.error
      };
    }

    try {
      // Resize image if needed
      const processedFile = await resizeImage(
        file, 
        DEFAULT_VALIDATION.maxWidth!, 
        DEFAULT_VALIDATION.maxHeight!
      );

      return {
        success: true,
        file: processedFile,
        url: URL.createObjectURL(processedFile)
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to process image'
      };
    }
  }
}

// Utility function to check if device has camera
export function hasCamera(): boolean {
  return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
}

// Utility function to check if device supports file API
export function supportsFileAPI(): boolean {
  return !!(window.File && window.FileReader && window.FileList && window.Blob);
}
