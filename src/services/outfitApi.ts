import { getApiConfig } from './backendApi';

export interface OutfitItem {
  id: string;
  name: string;
  color: string;
  brand?: string;
  image_url?: string;
  category_name?: string;
}

export interface Outfit {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  occasion?: string;
  season?: string;
  is_favorite: boolean;
  wear_count: number;
  last_worn_date?: string;
  image_url?: string;
  created_at: string;
  updated_at: string;
  clothing_items?: OutfitItem[];
}

export interface CreateOutfitRequest {
  userId: string;
  name?: string;
  description?: string;
  occasion?: string;
  season?: string;
  clothingItemIds: string[];
  notes?: string;
}

export class OutfitApiService {
  private static getBaseUrl() {
    return getApiConfig().baseUrl;
  }

  /**
   * Create a new outfit with clothing items
   */
  static async createOutfit(request: CreateOutfitRequest): Promise<Outfit> {
    try {
      const response = await fetch(`${this.getBaseUrl()}/outfits`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('Error creating outfit:', error);
      throw error;
    }
  }

  /**
   * Get all outfits for a user
   */
  static async getOutfits(userId: string): Promise<Outfit[]> {
    try {
      const response = await fetch(`${this.getBaseUrl()}/outfits/${userId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('Error fetching outfits:', error);
      throw error;
    }
  }

  /**
   * Get a specific outfit with clothing items
   */
  static async getOutfitDetail(outfitId: string): Promise<Outfit> {
    try {
      const response = await fetch(`${this.getBaseUrl()}/outfits/detail/${outfitId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('Error fetching outfit detail:', error);
      throw error;
    }
  }

  /**
   * Delete an outfit
   */
  static async deleteOutfit(outfitId: string): Promise<void> {
    try {
      const response = await fetch(`${this.getBaseUrl()}/outfits/${outfitId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }
    } catch (error) {
      console.error('Error deleting outfit:', error);
      throw error;
    }
  }
}
