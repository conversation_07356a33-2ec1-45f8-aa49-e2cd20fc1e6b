import { AuthCredentials, AuthResult, PasswordValidation, UserProfile } from '@/types/user';
import { UserProfileService } from './database';

/**
 * Authentication service for handling user login, registration, and password management
 */
export class AuthService {
  
  /**
   * Hash a password using a simple but secure method
   * In a production app, you'd want to use a more robust library like bcrypt
   */
  private static async hashPassword(password: string): Promise<string> {
    try {
      // Check if crypto.subtle is available
      if (typeof crypto !== 'undefined' && crypto.subtle) {
        const encoder = new TextEncoder();
        const data = encoder.encode(password + 'closet-glass-chic-salt');
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
      } else {
        // Fallback to a simple hash for development/testing
        return this.simpleHash(password + 'closet-glass-chic-salt');
      }
    } catch (error) {
      console.warn('Crypto API not available, using fallback hash:', error);
      // Fallback to a simple hash
      return this.simpleHash(password + 'closet-glass-chic-salt');
    }
  }

  /**
   * Simple hash function fallback for environments without crypto.subtle
   */
  private static simpleHash(str: string): string {
    let hash = 0;
    if (str.length === 0) return hash.toString();

    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }

    // Convert to positive hex string
    return Math.abs(hash).toString(16).padStart(8, '0');
  }

  /**
   * Validate password strength
   */
  static validatePassword(password: string): PasswordValidation {
    const errors: string[] = [];
    
    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }
    
    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }
    
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }
    
    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }
    
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate email format
   */
  static validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Register a new user
   */
  static async register(credentials: AuthCredentials, profileData: Omit<UserProfile, 'id' | 'email' | 'createdAt' | 'updatedAt'>): Promise<AuthResult> {
    try {
      console.log('Starting user registration process...');

      // Validate email
      if (!this.validateEmail(credentials.email)) {
        console.log('Email validation failed');
        return {
          success: false,
          error: 'Invalid email format'
        };
      }

      // Validate password
      const passwordValidation = this.validatePassword(credentials.password);
      if (!passwordValidation.isValid) {
        console.log('Password validation failed:', passwordValidation.errors);
        return {
          success: false,
          error: passwordValidation.errors.join(', ')
        };
      }

      // Check if user already exists
      console.log('Checking if user already exists...');
      try {
        const existingUser = await UserProfileService.getUserByEmail(credentials.email);
        if (existingUser) {
          console.log('User already exists');
          return {
            success: false,
            error: 'User with this email already exists'
          };
        }
      } catch (error) {
        console.log('Error checking existing user (continuing):', error);
        // Continue with registration if getUserByEmail fails (user doesn't exist)
      }

      // Hash password
      console.log('Hashing password...');
      const passwordHash = await this.hashPassword(credentials.password);
      console.log('Password hashed successfully');

      // Create user profile
      console.log('Creating user profile...');
      const userProfile: Omit<UserProfile, 'id' | 'createdAt' | 'updatedAt'> = {
        ...profileData,
        email: credentials.email
      };

      const userId = await UserProfileService.createUserProfileWithAuth(userProfile, passwordHash);
      console.log('User profile created with ID:', userId);

      const createdUser = await UserProfileService.getUserProfile(userId);
      console.log('Retrieved created user:', createdUser ? 'success' : 'failed');

      if (!createdUser) {
        return {
          success: false,
          error: 'Failed to create user profile'
        };
      }

      console.log('User registration completed successfully');
      
      // Set the current user session
      this.setCurrentUser(createdUser);
      
      return {
        success: true,
        user: createdUser
      };

    } catch (error) {
      console.error('Registration error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      return {
        success: false,
        error: `Registration failed: ${errorMessage}`
      };
    }
  }

  /**
   * Sign in an existing user
   */
  static async signIn(credentials: AuthCredentials): Promise<AuthResult> {
    try {
      console.log('Starting user sign in process...');

      // Validate email format
      if (!this.validateEmail(credentials.email)) {
        console.log('Email validation failed');
        return {
          success: false,
          error: 'Invalid email format'
        };
      }

      // Get user by email
      console.log('Looking up user by email...');
      const user = await UserProfileService.getUserByEmail(credentials.email);
      if (!user) {
        console.log('User not found');
        return {
          success: false,
          error: 'Invalid email or password'
        };
      }

      // Verify password
      console.log('Verifying password...');
      const passwordHash = await this.hashPassword(credentials.password);
      const isValidPassword = await UserProfileService.verifyPassword(user.id!, passwordHash);

      if (!isValidPassword) {
        console.log('Password verification failed');
        return {
          success: false,
          error: 'Invalid email or password'
        };
      }

      console.log('Sign in successful');
      
      // Set the current user session
      this.setCurrentUser(user);
      
      return {
        success: true,
        user
      };

    } catch (error) {
      console.error('Sign in error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      return {
        success: false,
        error: `Sign in failed: ${errorMessage}`
      };
    }
  }

  /**
   * Sign out the current user
   */
  static async signOut(): Promise<void> {
    // Clear any stored session data
    localStorage.removeItem('current-user-id');
    localStorage.removeItem('auth-token');
  }

  /**
   * Get current authenticated user
   */
  static async getCurrentUser(): Promise<UserProfile | null> {
    try {
      const userId = localStorage.getItem('current-user-id');
      if (!userId) {
        return null;
      }

      return await UserProfileService.getUserProfile(userId);
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  }

  /**
   * Set current user session
   */
  static setCurrentUser(user: UserProfile): void {
    if (user.id) {
      localStorage.setItem('current-user-id', user.id);
    }
  }


}
