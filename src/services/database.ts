import Dexie, { Table } from 'dexie';
import { UserProfile, UserProfileTable } from '@/types/user';
import { databaseLogger } from '@/utils/logger';

// Database schema
export class ClosetDatabase extends Dexie {
  userProfiles!: Table<UserProfileTable>;

  constructor() {
    super(import.meta.env.VITE_DB_NAME || 'ClosetGlassChic');
    
    this.version(1).stores({
      userProfiles: '++id, firstName, lastName, email, passwordHash, gender, dateOfBirth, cityName, latitude, longitude, weatherPreferences, createdAt, updatedAt'
    });
  }
}

export const db = new ClosetDatabase();



// Database operations
export class UserProfileService {
  
    // Create a new user profile
  static async createUserProfile(profile: Omit<UserProfile, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    databaseLogger.info('Starting user profile creation', {
      firstName: profile.firstName,
      lastName: profile.lastName,
      cityName: profile.cityName
    });

    try {
      // Step 1: Validate input
      databaseLogger.debug('Validating user profile data', profile);
      const validation = UserProfileService.validateUserProfile(profile);

      if (!validation.isValid) {
        databaseLogger.error('User profile validation failed', { errors: validation.errors });
        throw new Error(`Invalid user profile: ${validation.errors.join(', ')}`);
      }

      databaseLogger.debug('User profile validation passed');

      // Step 2: Check database connection
      databaseLogger.debug('Checking database connection', {
        dbName: db.name,
        isOpen: db.isOpen(),
        hasUserProfilesTable: !!db.userProfiles
      });

      if (!db.isOpen()) {
        databaseLogger.warn('Database not open, attempting to open');
        await db.open();
      }

      // Step 3: Prepare data
      const now = new Date().toISOString();
      const userProfileData: Omit<UserProfileTable, 'id'> = {
        firstName: profile.firstName,
        lastName: profile.lastName,
        email: profile.email,
        passwordHash: '', // Will be set by createUserProfileWithAuth
        gender: profile.gender,
        dateOfBirth: profile.dateOfBirth,
        cityName: profile.cityName,
        latitude: profile.latitude,
        longitude: profile.longitude,
        weatherPreferences: JSON.stringify(profile.weatherPreferences || {}),
        createdAt: now,
        updatedAt: now,
      };

      databaseLogger.debug('Prepared user profile data for insertion', userProfileData);

      // Step 4: Check table and method availability
      databaseLogger.debug('Checking database table and methods', {
        tableExists: !!db.userProfiles,
        addMethodExists: typeof db.userProfiles?.add,
        addMethodType: typeof db.userProfiles?.add
      });

      if (!db.userProfiles) {
        throw new Error('UserProfiles table not available');
      }

      if (typeof db.userProfiles.add !== 'function') {
        throw new Error('Database add method not available');
      }

      // Step 5: Insert into database
      databaseLogger.info('Inserting user profile into database');
      const id = await db.userProfiles.add(userProfileData as UserProfileTable);

      databaseLogger.info('User profile created successfully', { id: id.toString() });
      return id.toString();

    } catch (error) {
      databaseLogger.error('Failed to create user profile', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      }, error instanceof Error ? error : undefined);

      throw new Error(`Failed to create user profile: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Get user profile by ID
  static async getUserProfile(id: string | number): Promise<UserProfile | null> {
    try {
      // Convert string ID to number if needed for Dexie
      const numericId = typeof id === 'string' ? parseInt(id, 10) : id;

      if (isNaN(numericId)) {
        databaseLogger.error('Invalid user ID provided', { id });
        return null;
      }

      const profile = await db.userProfiles.get(numericId);
      if (!profile) {
        databaseLogger.debug('User profile not found', { id: numericId });
        return null;
      }

      return this.mapTableToProfile(profile);
    } catch (error) {
      databaseLogger.error('Error getting user profile', {
        id,
        error: error instanceof Error ? error.message : 'Unknown error'
      }, error instanceof Error ? error : undefined);
      throw new Error('Failed to retrieve user profile');
    }
  }

  // Get the current user profile (assuming single user for now)
  static async getCurrentUserProfile(): Promise<UserProfile | null> {
    try {
      const profiles = await db.userProfiles.orderBy('createdAt').reverse().limit(1).toArray();
      if (profiles.length === 0) return null;

      return this.mapTableToProfile(profiles[0]);
    } catch (error) {
      console.error('Error getting current user profile:', error);
      throw new Error('Failed to retrieve current user profile');
    }
  }

  // Update user profile
  static async updateUserProfile(id: string, updates: Partial<Omit<UserProfile, 'id' | 'createdAt'>>): Promise<void> {
    try {
      const updateData: Partial<UserProfileTable> = {
        ...updates,
        weatherPreferences: updates.weatherPreferences ? JSON.stringify(updates.weatherPreferences) : undefined,
        updatedAt: new Date().toISOString(),
      };

      await db.userProfiles.update(id, updateData);
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw new Error('Failed to update user profile');
    }
  }

  // Delete user profile
  static async deleteUserProfile(id: string): Promise<void> {
    try {
      await db.userProfiles.delete(id);
    } catch (error) {
      console.error('Error deleting user profile:', error);
      throw new Error('Failed to delete user profile');
    }
  }

  // Check if user profile exists
  static async hasUserProfile(): Promise<boolean> {
    try {
      const count = await db.userProfiles.count();
      return count > 0;
    } catch (error) {
      console.error('Error checking user profile existence:', error);
      return false;
    }
  }

  // Clear all user profiles (for testing/reset)
  static async clearAllProfiles(): Promise<void> {
    try {
      await db.userProfiles.clear();
    } catch (error) {
      console.error('Error clearing user profiles:', error);
      throw new Error('Failed to clear user profiles');
    }
  }

  // Validate user profile data
  static validateUserProfile(profile: Partial<UserProfile>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!profile.firstName || profile.firstName.trim().length < 1) {
      errors.push('First name is required');
    }

    if (!profile.lastName || profile.lastName.trim().length < 1) {
      errors.push('Last name is required');
    }

    if (!profile.gender || !['Male', 'Female', 'Other', 'Prefer not to say'].includes(profile.gender)) {
      errors.push('Valid gender selection is required');
    }

    if (!profile.dateOfBirth) {
      errors.push('Date of birth is required');
    } else {
      const birthDate = new Date(profile.dateOfBirth);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      
      if (age < 13 || age > 120) {
        errors.push('Age must be between 13 and 120 years');
      }
    }

    if (!profile.cityName || profile.cityName.trim().length < 2) {
      errors.push('City name is required');
    }

    if (typeof profile.latitude !== 'number' || typeof profile.longitude !== 'number') {
      errors.push('Valid location coordinates are required');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  // Create user profile with authentication
  static async createUserProfileWithAuth(profile: Omit<UserProfile, 'id' | 'createdAt' | 'updatedAt'>, passwordHash: string): Promise<string> {
    databaseLogger.info('Starting user profile creation with authentication', {
      firstName: profile.firstName,
      lastName: profile.lastName,
      email: profile.email,
      cityName: profile.cityName
    });

    try {
      const validation = UserProfileService.validateUserProfile(profile);
      if (!validation.isValid) {
        databaseLogger.error('User profile validation failed', { errors: validation.errors });
        throw new Error(`Invalid user profile: ${validation.errors.join(', ')}`);
      }

      if (!db.isOpen()) {
        await db.open();
      }

      const now = new Date().toISOString();
      const userProfileData: Omit<UserProfileTable, 'id'> = {
        firstName: profile.firstName,
        lastName: profile.lastName,
        email: profile.email,
        passwordHash: passwordHash,
        gender: profile.gender,
        dateOfBirth: profile.dateOfBirth,
        cityName: profile.cityName,
        latitude: profile.latitude,
        longitude: profile.longitude,
        weatherPreferences: JSON.stringify(profile.weatherPreferences || {}),
        createdAt: now,
        updatedAt: now,
      };

      const id = await db.userProfiles.add(userProfileData as UserProfileTable);
      const stringId = id.toString();
      databaseLogger.info('User profile with auth created successfully', { id: stringId });
      return stringId;

    } catch (error) {
      databaseLogger.error('Failed to create user profile with auth', {
        error: error instanceof Error ? error.message : 'Unknown error'
      }, error instanceof Error ? error : undefined);
      throw new Error(`Failed to create user profile: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Get user by email
  static async getUserByEmail(email: string): Promise<UserProfile | null> {
    try {
      databaseLogger.debug('Getting user by email', { email });

      if (!db.isOpen()) {
        await db.open();
      }

      const profile = await db.userProfiles.where('email').equals(email).first();
      if (!profile) {
        databaseLogger.debug('User not found by email', { email });
        return null;
      }

      databaseLogger.debug('User found by email', { email, userId: profile.id });
      return this.mapTableToProfile(profile);
    } catch (error) {
      databaseLogger.error('Error getting user by email', {
        email,
        error: error instanceof Error ? error.message : 'Unknown error'
      }, error instanceof Error ? error : undefined);

      // Return null instead of throwing to allow registration to continue
      return null;
    }
  }

  // Verify password
  static async verifyPassword(userId: string | number, passwordHash: string): Promise<boolean> {
    try {
      databaseLogger.debug('Verifying password for user', { userId });

      if (!db.isOpen()) {
        await db.open();
      }

      // Convert string ID to number if needed for Dexie
      const numericId = typeof userId === 'string' ? parseInt(userId, 10) : userId;

      if (isNaN(numericId)) {
        databaseLogger.error('Invalid user ID provided for password verification', { userId });
        return false;
      }

      const profile = await db.userProfiles.get(numericId);
      if (!profile) {
        databaseLogger.debug('User not found for password verification', { userId: numericId });
        return false;
      }

      const isValid = profile.passwordHash === passwordHash;
      databaseLogger.debug('Password verification result', { userId: numericId, isValid });
      return isValid;
    } catch (error) {
      databaseLogger.error('Error verifying password', {
        userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      }, error instanceof Error ? error : undefined);
      return false;
    }
  }

  // Helper method to map database table to UserProfile
  private static mapTableToProfile(tableData: UserProfileTable): UserProfile {
    return {
      id: tableData.id?.toString(), // Ensure ID is always a string
      firstName: tableData.firstName,
      lastName: tableData.lastName,
      email: tableData.email,
      gender: tableData.gender as UserProfile['gender'],
      dateOfBirth: tableData.dateOfBirth,
      cityName: tableData.cityName,
      latitude: tableData.latitude,
      longitude: tableData.longitude,
      weatherPreferences: tableData.weatherPreferences ? JSON.parse(tableData.weatherPreferences) : undefined,
      createdAt: tableData.createdAt,
      updatedAt: tableData.updatedAt,
    };
  }
}

// Initialize database
export async function initializeDatabase(): Promise<void> {
  try {
    databaseLogger.info('Initializing database');
    await db.open();

    // Verify database structure
    const tables = db.tables;
    databaseLogger.info('Database opened successfully', {
      name: db.name,
      version: db.verno,
      tables: tables.map(t => t.name),
      isOpen: db.isOpen()
    });

    // Test userProfiles table
    if (db.userProfiles) {
      const count = await db.userProfiles.count();
      databaseLogger.info('UserProfiles table verified', { recordCount: count });
    } else {
      throw new Error('UserProfiles table not found');
    }

  } catch (error) {
    databaseLogger.error('Failed to initialize database', {
      errorMessage: error instanceof Error ? error.message : 'Unknown error'
    }, error instanceof Error ? error : undefined);
    throw new Error(`Database initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Export database instance for direct access if needed
export { db as database };
