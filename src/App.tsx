import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { Layout } from "./components/Layout";
import { Home } from "./pages/Home";
import { Closet } from "./pages/Closet";
import { Schedule } from "./pages/Schedule";
import { Profile } from "./pages/Profile";
import NewLandingPage from "./pages/NewLandingPage";
import { LoginPage } from "./pages/LoginPage";
import NotFound from "./pages/NotFound";
import { DebugOnboarding } from "./components/DebugOnboarding";
import { OnboardingProvider } from "./contexts/OnboardingContext";
import { OnboardingRouter } from "./components/onboarding/OnboardingRouter";
import { useOnboarding } from "./hooks/useOnboarding";
import { checkBackendHealth } from "./services/backendApi";
import { ErrorBoundary } from "./components/ErrorBoundary";
import PersistentAuthService from "./services/persistentAuth";
import "./utils/debugConfig"; // Load debug utilities
import { AuthGuard } from "./components/AuthGuard";
import { useEffect, useState } from "react";
import { UserProfileFormData } from "./types/user";

const queryClient = new QueryClient();

// Authentication states
type AuthState = 'landing' | 'login' | 'onboarding' | 'authenticated';

// Main App Component with Authentication and Onboarding Integration
const MainApp = () => {
  const { isCompleted, resetOnboarding } = useOnboarding();
  const [authState, setAuthState] = useState<AuthState>('landing');
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);
  const [prefilledEmail, setPrefilledEmail] = useState<string>('');

  // Check backend health on app start
  useEffect(() => {
    checkBackendHealth().then(health => {
      if (!health.healthy) {
        console.warn('Backend health check failed:', health.details);
      } else {
        console.log('✅ Backend is healthy');
      }
    }).catch(error => {
      console.error('Failed to check backend health:', error);
    });
  }, []);

  // Check if user is already authenticated
  useEffect(() => {
    const checkAuthentication = async () => {
      try {
        console.log('🔍 Checking authentication...', { isCompleted });
        const currentUser = await PersistentAuthService.getCurrentUser();
        console.log('👤 Current user:', currentUser);

        if (currentUser) {
          // If user is authenticated, go directly to main app
          // Onboarding is only for new signups, not existing users
          console.log('✅ User authenticated, going to main app');
          setIsAuthenticated(true);
          setAuthState('authenticated');
        } else {
          console.log('🏠 No user authenticated, showing landing page');
          setAuthState('landing');
        }
      } catch (error) {
        console.error('Authentication check failed:', error);
        setAuthState('landing');
      } finally {
        setIsCheckingAuth(false);
      }
    };

    checkAuthentication();
  }, [isCompleted]);

  const handleSignUp = () => {
    // Directly go to onboarding for signup
    setPrefilledEmail(''); // Clear any prefilled email when going to signup
    setAuthState('onboarding');
  };

  const handleSignIn = (email?: string) => {
    setPrefilledEmail(email || ''); // Store the email for pre-population
    setAuthState('login');
  };

  const handleBackToLanding = () => {
    setPrefilledEmail(''); // Clear prefilled email when going back to landing
    setAuthState('landing');
  };

  const handleLoginSuccess = async () => {
    try {
      const currentUser = await PersistentAuthService.getCurrentUser();
      if (currentUser) {
        // For login, always go directly to authenticated state
        // Onboarding is only for new signups
        setIsAuthenticated(true);
        setAuthState('authenticated');
      } else {
        setAuthState('landing');
      }
    } catch (error) {
      console.error('Login success check failed:', error);
      setAuthState('landing');
    }
  };

  const handleOnboardingComplete = () => {
    setIsAuthenticated(true);
    setAuthState('authenticated');
    console.log('Onboarding completed successfully');
  };

  const handleLogout = () => {
    console.log('🚪 Initiating logout process...');

    // Clear authentication data
    PersistentAuthService.signOut();

    // Reset onboarding state completely (don't preserve preferences)
    resetOnboarding(false);

    // Reset application state
    setIsAuthenticated(false);
    setAuthState('landing');
    setPrefilledEmail('');
    setIsCheckingAuth(false);

    console.log('✅ Logout completed - redirecting to landing page');
  };

  const handleResetOnboarding = () => {
    resetOnboarding();
    PersistentAuthService.signOut();
    setIsAuthenticated(false);
    setAuthState('landing');
  };

  const handleClearStorage = () => {
    localStorage.clear();
    window.location.reload();
  };

  // Show landing page for unauthenticated users
  if (authState === 'landing') {
    return (
      <NewLandingPage
        onSignUp={handleSignUp}
        onSignIn={handleSignIn}
      />
    );
  }

  // Show login page
  if (authState === 'login') {
    return (
      <LoginPage
        onBack={handleBackToLanding}
        onSignUp={handleSignUp}
        onSuccess={handleLoginSuccess}
        initialEmail={prefilledEmail}
      />
    );
  }

  // Show loading while checking authentication
  if (isCheckingAuth) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  // Show onboarding if user is authenticated but hasn't completed onboarding
  if (authState === 'onboarding') {
    return <OnboardingRouter onComplete={handleOnboardingComplete} />;
  }

  // Show main application for authenticated users who completed onboarding
  return (
    <AuthGuard fallback={
      <NewLandingPage
        onSignUp={handleSignUp}
        onSignIn={handleSignIn}
      />
    }>
      <BrowserRouter>
        <Layout>
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/closet" element={<Closet />} />
            <Route path="/schedule" element={<Schedule />} />
            <Route path="/profile" element={<Profile onLogout={handleLogout} />} />
            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={<NotFound />} />
          </Routes>


        </Layout>
      </BrowserRouter>
    </AuthGuard>
  );
};

const App = () => (
  <ErrorBoundary>
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <OnboardingProvider>
          <Toaster />
          <Sonner />
          <MainApp />
        </OnboardingProvider>
      </TooltipProvider>
    </QueryClientProvider>
  </ErrorBoundary>
);

export default App;
