import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { SignUpPage } from '../SignUpPage';
import { authApi } from '@/services/backendApi';

// Mock the authApi
vi.mock('@/services/backendApi', () => ({
  authApi: {
    validateEmail: vi.fn()
  },
  BackendApiError: class extends Error {
    constructor(message: string, public status?: number, public code?: string) {
      super(message);
      this.name = 'BackendApiError';
    }
  }
}));

describe('SignUpPage Email Validation', () => {
  const mockProps = {
    onBack: vi.fn(),
    onSignIn: vi.fn(),
    onSuccess: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render email input field', () => {
    render(<SignUpPage {...mockProps} />);
    
    const emailInput = screen.getByPlaceholderText('Enter your email');
    expect(emailInput).toBeInTheDocument();
  });

  it('should trigger email validation on blur', async () => {
    const mockValidateEmail = vi.mocked(authApi.validateEmail);
    mockValidateEmail.mockResolvedValue({ available: true, message: 'Email is available' });

    render(<SignUpPage {...mockProps} />);
    
    const emailInput = screen.getByPlaceholderText('Enter your email');
    
    // Type email and blur
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.blur(emailInput);

    // Should call validateEmail
    await waitFor(() => {
      expect(mockValidateEmail).toHaveBeenCalledWith('<EMAIL>');
    });
  });

  it('should show loading indicator during validation', async () => {
    const mockValidateEmail = vi.mocked(authApi.validateEmail);
    // Create a promise that we can control
    let resolveValidation: (value: { available: boolean; message: string }) => void;
    const validationPromise = new Promise<{ available: boolean; message: string }>(resolve => {
      resolveValidation = resolve;
    });
    mockValidateEmail.mockReturnValue(validationPromise);

    render(<SignUpPage {...mockProps} />);
    
    const emailInput = screen.getByPlaceholderText('Enter your email');
    
    // Type email and blur
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.blur(emailInput);

    // Should show loading indicator (Loader2 icon with animate-spin class)
    await waitFor(() => {
      const spinner = document.querySelector('.animate-spin');
      expect(spinner).toBeInTheDocument();
    });

    // Resolve the validation
    resolveValidation!({ available: true, message: 'Email is available' });
  });

  it('should disable signup button when email is taken', async () => {
    const mockValidateEmail = vi.mocked(authApi.validateEmail);
    mockValidateEmail.mockResolvedValue({ available: false, message: 'Email already exists' });

    render(<SignUpPage {...mockProps} />);
    
    const emailInput = screen.getByPlaceholderText('Enter your email');
    const submitButton = screen.getByRole('button', { name: /create account/i });
    
    // Initially button should be enabled
    expect(submitButton).not.toBeDisabled();
    
    // Type email and blur
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.blur(emailInput);

    // Wait for validation to complete and button to be disabled
    await waitFor(() => {
      expect(submitButton).toBeDisabled();
    });
  });

  it('should show error message when email is taken', async () => {
    const mockValidateEmail = vi.mocked(authApi.validateEmail);
    mockValidateEmail.mockResolvedValue({ available: false, message: 'This email is already registered' });

    render(<SignUpPage {...mockProps} />);
    
    const emailInput = screen.getByPlaceholderText('Enter your email');
    
    // Type email and blur
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.blur(emailInput);

    // Should show error message
    await waitFor(() => {
      expect(screen.getByText('This email is already registered.')).toBeInTheDocument();
    });
  });

  it('should show sign in suggestion when email is taken', async () => {
    const mockValidateEmail = vi.mocked(authApi.validateEmail);
    mockValidateEmail.mockResolvedValue({ available: false, message: 'Email already exists' });

    render(<SignUpPage {...mockProps} />);
    
    const emailInput = screen.getByPlaceholderText('Enter your email');
    
    // Type email and blur
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.blur(emailInput);

    // Should show sign in suggestion
    await waitFor(() => {
      expect(screen.getByText('Sign in instead')).toBeInTheDocument();
    });

    // Click sign in suggestion
    const signInButton = screen.getByText('Sign in instead');
    fireEvent.click(signInButton);

    expect(mockProps.onSignIn).toHaveBeenCalledWith('<EMAIL>');
  });

  it('should reset validation state when email changes', async () => {
    const mockValidateEmail = vi.mocked(authApi.validateEmail);
    mockValidateEmail.mockResolvedValue({ available: false, message: 'Email already exists' });

    render(<SignUpPage {...mockProps} />);
    
    const emailInput = screen.getByPlaceholderText('Enter your email');
    
    // Type email and blur
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.blur(emailInput);

    // Wait for validation to complete
    await waitFor(() => {
      expect(screen.getByText('This email is already registered.')).toBeInTheDocument();
    });

    // Change email
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });

    // Error message should be cleared
    expect(screen.queryByText('This email is already registered.')).not.toBeInTheDocument();
  });

  it('should not validate invalid email formats', async () => {
    const mockValidateEmail = vi.mocked(authApi.validateEmail);

    render(<SignUpPage {...mockProps} />);
    
    const emailInput = screen.getByPlaceholderText('Enter your email');
    
    // Type invalid email and blur
    fireEvent.change(emailInput, { target: { value: 'invalid-email' } });
    fireEvent.blur(emailInput);

    // Should not call validateEmail for invalid format
    expect(mockValidateEmail).not.toHaveBeenCalled();
  });

  // Error handling and recovery tests
  describe('Error Handling and Recovery', () => {
    it('should handle network errors gracefully', async () => {
      const { BackendApiError } = await import('@/services/backendApi');
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      mockValidateEmail.mockRejectedValue(new BackendApiError('Network error', 0, 'NETWORK_ERROR'));

      render(<SignUpPage {...mockProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      const submitButton = screen.getByRole('button', { name: /create account/i });
      
      // Type email and blur
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.blur(emailInput);

      // Should show network error message and allow signup
      await waitFor(() => {
        expect(screen.getByText(/Network error/)).toBeInTheDocument();
      });

      // Signup button should remain enabled (graceful degradation)
      expect(submitButton).not.toBeDisabled();
    });

    it('should handle timeout errors gracefully', async () => {
      const { BackendApiError } = await import('@/services/backendApi');
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      mockValidateEmail.mockRejectedValue(new BackendApiError('Validation timed out. You can retry by clicking the email field again, or proceed with signup.', 0, 'VALIDATION_TIMEOUT'));

      render(<SignUpPage {...mockProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      const submitButton = screen.getByRole('button', { name: /create account/i });
      
      // Type email and blur
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.blur(emailInput);

      // Should show timeout error message and allow signup
      await waitFor(() => {
        expect(screen.getByText(/timed out/i)).toBeInTheDocument();
      });

      // Signup button should remain enabled (graceful degradation)
      expect(submitButton).not.toBeDisabled();
    });

    it('should show retry button for network errors', async () => {
      const { BackendApiError } = await import('@/services/backendApi');
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      mockValidateEmail.mockRejectedValue(new BackendApiError('Network connection issue', 0, 'NETWORK_ERROR'));

      render(<SignUpPage {...mockProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      
      // Type email and blur
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.blur(emailInput);

      // Should show retry button
      await waitFor(() => {
        expect(screen.getByText('Try again')).toBeInTheDocument();
      });
    });

    it('should allow retry by clicking retry button', async () => {
      const { BackendApiError } = await import('@/services/backendApi');
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      
      // First call fails, second succeeds
      mockValidateEmail
        .mockRejectedValueOnce(new BackendApiError('Network error', 0, 'NETWORK_ERROR'))
        .mockResolvedValueOnce({ available: true, message: 'Email is available' });

      render(<SignUpPage {...mockProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      
      // Type email and blur
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.blur(emailInput);

      // Wait for error and retry button
      await waitFor(() => {
        expect(screen.getByText('Try again')).toBeInTheDocument();
      });

      // Click retry button
      const retryButton = screen.getByText('Try again');
      fireEvent.click(retryButton);

      // Should call validateEmail again
      await waitFor(() => {
        expect(mockValidateEmail).toHaveBeenCalledTimes(2);
      });

      // Error message should be cleared
      await waitFor(() => {
        expect(screen.queryByText('Try again')).not.toBeInTheDocument();
      });
    });

    it('should allow retry by re-focusing email field', async () => {
      const { BackendApiError } = await import('@/services/backendApi');
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      mockValidateEmail.mockRejectedValue(new BackendApiError('Network connection issue. You can retry by clicking the email field again, or proceed with signup.', 0, 'NETWORK_ERROR'));

      render(<SignUpPage {...mockProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      
      // Type email and blur
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.blur(emailInput);

      // Wait for error message
      await waitFor(() => {
        expect(screen.getByText(/Network connection issue/)).toBeInTheDocument();
      });

      // Re-focus the email field
      fireEvent.focus(emailInput);

      // Verify that the error message is cleared when focusing (indicating retry capability)
      await waitFor(() => {
        expect(screen.queryByText(/Network connection issue/)).not.toBeInTheDocument();
      });
    });

    it('should handle server errors gracefully', async () => {
      const { BackendApiError } = await import('@/services/backendApi');
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      mockValidateEmail.mockRejectedValue(new BackendApiError('Internal server error', 500));

      render(<SignUpPage {...mockProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      const submitButton = screen.getByRole('button', { name: /create account/i });
      
      // Type email and blur
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.blur(emailInput);

      // Should show error message and allow signup
      await waitFor(() => {
        expect(screen.getByText(/server error/i)).toBeInTheDocument();
      });

      // Signup button should remain enabled (graceful degradation)
      expect(submitButton).not.toBeDisabled();
    });

    it('should not prevent signup for any validation errors', async () => {
      const { BackendApiError } = await import('@/services/backendApi');
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      
      const errorScenarios = [
        new BackendApiError('Network error', 0, 'NETWORK_ERROR'),
        new BackendApiError('Timeout', 0, 'VALIDATION_TIMEOUT'),
        new BackendApiError('Server error', 500),
        new Error('Unexpected error')
      ];

      for (const error of errorScenarios) {
        mockValidateEmail.mockRejectedValue(error);

        render(<SignUpPage {...mockProps} />);
        
        const emailInput = screen.getByPlaceholderText('Enter your email');
        const submitButton = screen.getByRole('button', { name: /create account/i });
        
        // Fill out form
        fireEvent.change(screen.getByPlaceholderText('First name'), { target: { value: 'John' } });
        fireEvent.change(screen.getByPlaceholderText('Last name'), { target: { value: 'Doe' } });
        fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
        fireEvent.change(screen.getByPlaceholderText('Create a password'), { target: { value: 'Password123' } });
        fireEvent.change(screen.getByPlaceholderText('Confirm your password'), { target: { value: 'Password123' } });
        
        fireEvent.blur(emailInput);

        // Wait for validation to complete
        await waitFor(() => {
          // Should show some error message
          expect(document.body.textContent).toMatch(/(error|timeout|server|network|unable)/i);
        });

        // Signup button should remain enabled for all error types
        expect(submitButton).not.toBeDisabled();
        
        // Clean up for next iteration
        document.body.innerHTML = '';
      }
    });
  });

  it('should pass email to onSignIn when clicking "Sign In Instead" button', () => {
    render(<SignUpPage {...mockProps} />);
    
    const emailInput = screen.getByPlaceholderText('Enter your email');
    const signInButton = screen.getByRole('button', { name: /sign in instead/i });
    
    // Type email
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    
    // Click "Sign In Instead" button
    fireEvent.click(signInButton);

    expect(mockProps.onSignIn).toHaveBeenCalledWith('<EMAIL>');
  });

  // Additional comprehensive tests for complete coverage
  describe('EmailValidationState Management and Transitions', () => {
    it('should properly manage validation state transitions from initial to validating to success', async () => {
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      mockValidateEmail.mockResolvedValue({ available: true, message: 'Email is available' });

      render(<SignUpPage {...mockProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      
      // Initial state - no validation indicators
      expect(document.querySelector('.animate-spin')).not.toBeInTheDocument();
      expect(screen.queryByText(/already registered/)).not.toBeInTheDocument();
      
      // Type email and blur to trigger validation
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.blur(emailInput);

      // Should show loading state
      await waitFor(() => {
        expect(document.querySelector('.animate-spin')).toBeInTheDocument();
      });

      // Should complete validation and clear loading state
      await waitFor(() => {
        expect(document.querySelector('.animate-spin')).not.toBeInTheDocument();
      });

      // Should not show any error messages for available email
      expect(screen.queryByText(/already registered/)).not.toBeInTheDocument();
      expect(screen.queryByText(/error/i)).not.toBeInTheDocument();
    });

    it('should properly manage validation state transitions from initial to validating to error', async () => {
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      mockValidateEmail.mockResolvedValue({ available: false, message: 'Email already exists' });

      render(<SignUpPage {...mockProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      const submitButton = screen.getByRole('button', { name: /create account/i });
      
      // Initial state
      expect(submitButton).not.toBeDisabled();
      expect(document.querySelector('.animate-spin')).not.toBeInTheDocument();
      
      // Type email and blur
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.blur(emailInput);

      // Should show loading state
      await waitFor(() => {
        expect(document.querySelector('.animate-spin')).toBeInTheDocument();
      });

      // Should complete validation and show error state
      await waitFor(() => {
        expect(document.querySelector('.animate-spin')).not.toBeInTheDocument();
        expect(screen.getByText('This email is already registered.')).toBeInTheDocument();
        expect(submitButton).toBeDisabled();
      });
    });

    it('should reset validation state completely when email field is cleared', async () => {
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      mockValidateEmail.mockResolvedValue({ available: false, message: 'Email already exists' });

      render(<SignUpPage {...mockProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      const submitButton = screen.getByRole('button', { name: /create account/i });
      
      // Type email and validate
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.blur(emailInput);

      // Wait for validation to complete
      await waitFor(() => {
        expect(screen.getByText('This email is already registered.')).toBeInTheDocument();
        expect(submitButton).toBeDisabled();
      });

      // Clear email field
      fireEvent.change(emailInput, { target: { value: '' } });

      // All validation state should be reset
      expect(screen.queryByText('This email is already registered.')).not.toBeInTheDocument();
      expect(screen.queryByText('Sign in instead')).not.toBeInTheDocument();
      expect(submitButton).not.toBeDisabled();
    });

    it('should handle rapid email changes without triggering multiple validations', async () => {
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      mockValidateEmail.mockResolvedValue({ available: true, message: 'Email is available' });

      render(<SignUpPage {...mockProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      
      // Rapidly change email multiple times
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      
      // Only blur once
      fireEvent.blur(emailInput);

      // Should only call validateEmail once for the final email
      await waitFor(() => {
        expect(mockValidateEmail).toHaveBeenCalledTimes(1);
        expect(mockValidateEmail).toHaveBeenCalledWith('<EMAIL>');
      });
    });
  });

  describe('API Integration Edge Cases', () => {
    it('should handle empty email validation response gracefully', async () => {
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      mockValidateEmail.mockResolvedValue({ available: true, message: '' });

      render(<SignUpPage {...mockProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.blur(emailInput);

      // Should handle empty message gracefully
      await waitFor(() => {
        expect(mockValidateEmail).toHaveBeenCalledWith('<EMAIL>');
      });

      // Should not show any error messages
      expect(screen.queryByText(/error/i)).not.toBeInTheDocument();
    });

    it('should handle malformed API responses gracefully', async () => {
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      // Mock a malformed response (missing required fields)
      mockValidateEmail.mockResolvedValue({ available: true } as any);

      render(<SignUpPage {...mockProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      const submitButton = screen.getByRole('button', { name: /create account/i });
      
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.blur(emailInput);

      // Should handle malformed response without crashing
      await waitFor(() => {
        expect(mockValidateEmail).toHaveBeenCalledWith('<EMAIL>');
      });

      // Should not prevent signup
      expect(submitButton).not.toBeDisabled();
    });

    it('should handle API responses with special characters in messages', async () => {
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      const specialMessage = 'Email "<EMAIL>" is already registered with special chars: <>&"\'';
      mockValidateEmail.mockResolvedValue({ available: false, message: specialMessage });

      render(<SignUpPage {...mockProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.blur(emailInput);

      // Should display the message with special characters safely
      await waitFor(() => {
        expect(screen.getByText('This email is already registered.')).toBeInTheDocument();
      });
    });

    it('should handle concurrent validation requests correctly', async () => {
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      
      // First call takes longer, second call resolves faster
      let firstResolve: (value: { available: boolean; message: string }) => void;
      let secondResolve: (value: { available: boolean; message: string }) => void;
      
      const firstPromise = new Promise<{ available: boolean; message: string }>(resolve => {
        firstResolve = resolve;
      });
      
      const secondPromise = new Promise<{ available: boolean; message: string }>(resolve => {
        secondResolve = resolve;
      });

      mockValidateEmail
        .mockReturnValueOnce(firstPromise)
        .mockReturnValueOnce(secondPromise);

      render(<SignUpPage {...mockProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      
      // First validation
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.blur(emailInput);
      
      // Change email and trigger second validation before first completes
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.blur(emailInput);

      // Resolve second request first (email available)
      secondResolve!({ available: true, message: 'Available' });
      
      // Then resolve first request (email taken) - should be ignored
      firstResolve!({ available: false, message: 'Taken' });

      // Should show result from the most recent validation (second email)
      await waitFor(() => {
        expect(screen.queryByText(/taken/i)).not.toBeInTheDocument();
        expect(screen.queryByText(/already registered/i)).not.toBeInTheDocument();
      });
    });
  });

  describe('UI Feedback and Button State Changes', () => {
    it('should maintain button disabled state throughout validation process', async () => {
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      
      let resolveValidation: (value: { available: boolean; message: string }) => void;
      const validationPromise = new Promise<{ available: boolean; message: string }>(resolve => {
        resolveValidation = resolve;
      });
      mockValidateEmail.mockReturnValue(validationPromise);

      render(<SignUpPage {...mockProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      const submitButton = screen.getByRole('button', { name: /create account/i });
      
      // Fill out all required fields
      fireEvent.change(screen.getByPlaceholderText('First name'), { target: { value: 'John' } });
      fireEvent.change(screen.getByPlaceholderText('Last name'), { target: { value: 'Doe' } });
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(screen.getByPlaceholderText('Create a password'), { target: { value: 'Password123' } });
      fireEvent.change(screen.getByPlaceholderText('Confirm your password'), { target: { value: 'Password123' } });
      
      // Button should be enabled initially
      expect(submitButton).not.toBeDisabled();
      
      // Trigger validation
      fireEvent.blur(emailInput);

      // Button should remain enabled during validation (only disabled after email is confirmed taken)
      expect(submitButton).not.toBeDisabled();

      // Resolve with email taken
      resolveValidation!({ available: false, message: 'Email already exists' });

      // Button should now be disabled
      await waitFor(() => {
        expect(submitButton).toBeDisabled();
      });
    });

    it('should re-enable button when switching from taken email to available email', async () => {
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      
      render(<SignUpPage {...mockProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      const submitButton = screen.getByRole('button', { name: /create account/i });
      
      // Fill out all required fields with taken email
      fireEvent.change(screen.getByPlaceholderText('First name'), { target: { value: 'John' } });
      fireEvent.change(screen.getByPlaceholderText('Last name'), { target: { value: 'Doe' } });
      fireEvent.change(screen.getByPlaceholderText('Create a password'), { target: { value: 'Password123' } });
      fireEvent.change(screen.getByPlaceholderText('Confirm your password'), { target: { value: 'Password123' } });
      
      // First validation - email taken
      mockValidateEmail.mockResolvedValueOnce({ available: false, message: 'Email already exists' });
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.blur(emailInput);

      await waitFor(() => {
        expect(submitButton).toBeDisabled();
      });

      // Second validation - email available
      mockValidateEmail.mockResolvedValueOnce({ available: true, message: 'Email is available' });
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.blur(emailInput);

      await waitFor(() => {
        expect(submitButton).not.toBeDisabled();
      });
    });

    it('should show appropriate loading states during validation', async () => {
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      
      let resolveValidation: (value: { available: boolean; message: string }) => void;
      const validationPromise = new Promise<{ available: boolean; message: string }>(resolve => {
        resolveValidation = resolve;
      });
      mockValidateEmail.mockReturnValue(validationPromise);

      render(<SignUpPage {...mockProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      
      // No loading indicator initially
      expect(document.querySelector('.animate-spin')).not.toBeInTheDocument();
      
      // Trigger validation
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.blur(emailInput);

      // Should show loading indicator
      await waitFor(() => {
        const spinner = document.querySelector('.animate-spin');
        expect(spinner).toBeInTheDocument();
        expect(spinner).toHaveClass('w-5', 'h-5');
      });

      // Resolve validation
      resolveValidation!({ available: true, message: 'Available' });

      // Loading indicator should disappear
      await waitFor(() => {
        expect(document.querySelector('.animate-spin')).not.toBeInTheDocument();
      });
    });

    it('should display validation messages with correct styling', async () => {
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      mockValidateEmail.mockResolvedValue({ available: false, message: 'This email is already registered' });

      render(<SignUpPage {...mockProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.blur(emailInput);

      await waitFor(() => {
        const errorMessage = screen.getByText('This email is already registered.');
        expect(errorMessage).toBeInTheDocument();
        expect(errorMessage).toHaveClass('zara-caption', 'text-red-600');
      });

      await waitFor(() => {
        const signInSuggestion = screen.getByText('Sign in instead');
        expect(signInSuggestion).toBeInTheDocument();
        expect(signInSuggestion).toHaveClass('text-zara-charcoal', 'hover:underline', 'font-medium');
      });
    });
  });

  describe('Email Format Validation Edge Cases', () => {
    it('should not trigger API validation for various invalid email formats', async () => {
      const mockValidateEmail = vi.mocked(authApi.validateEmail);

      render(<SignUpPage {...mockProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      
      const invalidEmails = [
        'invalid',
        'invalid@',
        '@invalid.com',
        'invalid@.com',
        'invalid@com',
        'invalid.@com',
        'invalid..email@com',
        'invalid@com.',
        '',
        ' ',
        'invalid <EMAIL>',
        'invalid@exam ple.com'
      ];

      for (const invalidEmail of invalidEmails) {
        fireEvent.change(emailInput, { target: { value: invalidEmail } });
        fireEvent.blur(emailInput);
        
        // Should not call API for invalid formats
        expect(mockValidateEmail).not.toHaveBeenCalled();
        
        // Reset for next iteration
        vi.clearAllMocks();
      }
    });

    it('should trigger API validation for valid email formats', async () => {
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      mockValidateEmail.mockResolvedValue({ available: true, message: 'Available' });

      render(<SignUpPage {...mockProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ];

      for (const validEmail of validEmails) {
        fireEvent.change(emailInput, { target: { value: validEmail } });
        fireEvent.blur(emailInput);
        
        // Should call API for valid formats
        await waitFor(() => {
          expect(mockValidateEmail).toHaveBeenCalledWith(validEmail);
        });
        
        // Reset for next iteration
        vi.clearAllMocks();
      }
    });
  });
});