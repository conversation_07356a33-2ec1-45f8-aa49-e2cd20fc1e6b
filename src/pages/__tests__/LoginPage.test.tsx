import { render, screen } from '@testing-library/react';
import { vi, describe, it, expect } from 'vitest';
import { LoginPage } from '../LoginPage';

// Mock PersistentAuthService
vi.mock('@/services/persistentAuth', () => ({
  default: {
    signIn: vi.fn(),
  },
}));

describe('LoginPage', () => {
  const mockProps = {
    onBack: vi.fn(),
    onSignUp: vi.fn(),
    onSuccess: vi.fn(),
  };

  it('should pre-populate email field when initialEmail is provided', () => {
    const initialEmail = '<EMAIL>';
    
    render(<LoginPage {...mockProps} initialEmail={initialEmail} />);
    
    const emailInput = screen.getByPlaceholderText('Enter your email') as HTMLInputElement;
    expect(emailInput.value).toBe(initialEmail);
  });

  it('should have empty email field when no initialEmail is provided', () => {
    render(<LoginPage {...mockProps} />);
    
    const emailInput = screen.getByPlaceholderText('Enter your email') as HTMLInputElement;
    expect(emailInput.value).toBe('');
  });
});