import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { SignUpPage } from '../SignUpPage';
import { authApi } from '@/services/backendApi';

// Mock the authApi
vi.mock('@/services/backendApi', () => ({
  authApi: {
    validateEmail: vi.fn()
  },
  BackendApiError: class extends Error {
    constructor(message: string, public status?: number, public code?: string) {
      super(message);
      this.name = 'BackendApiError';
    }
  }
}));

describe('EmailValidationState Management', () => {
  const mockProps = {
    onBack: vi.fn(),
    onSignIn: vi.fn(),
    onSuccess: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('State Initialization', () => {
    it('should initialize with correct default EmailValidationState', () => {
      render(<SignUpPage {...mockProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      const submitButton = screen.getByRole('button', { name: /create account/i });
      
      // Initial state should have no validation indicators
      expect(document.querySelector('.animate-spin')).not.toBeInTheDocument();
      expect(screen.queryByText(/already registered/)).not.toBeInTheDocument();
      expect(screen.queryByText(/try again/i)).not.toBeInTheDocument();
      expect(submitButton).not.toBeDisabled();
      
      // Should not show email validation specific "Sign in instead" link (different from the bottom button)
      expect(screen.queryByText('Already have an account?')).toBeInTheDocument(); // This is the bottom section
      expect(screen.queryByText('This email is already registered.')).not.toBeInTheDocument();
      
      // Email input should be empty and ready for input
      expect(emailInput).toHaveValue('');
    });
  });

  describe('State Transitions During Validation', () => {
    it('should transition through all validation states correctly for successful validation', async () => {
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      
      let resolveValidation: (value: { available: boolean; message: string }) => void;
      const validationPromise = new Promise<{ available: boolean; message: string }>(resolve => {
        resolveValidation = resolve;
      });
      mockValidateEmail.mockReturnValue(validationPromise);

      render(<SignUpPage {...mockProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      
      // State 1: Initial state (not validating, not validated)
      expect(document.querySelector('.animate-spin')).not.toBeInTheDocument();
      
      // Trigger validation
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.blur(emailInput);
      
      // State 2: Validating state (isValidating: true)
      await waitFor(() => {
        expect(document.querySelector('.animate-spin')).toBeInTheDocument();
      });
      
      // Resolve validation with available email
      resolveValidation!({ available: true, message: 'Email is available' });
      
      // State 3: Validation complete, email available (isValidating: false, hasValidated: true, isEmailTaken: false)
      await waitFor(() => {
        expect(document.querySelector('.animate-spin')).not.toBeInTheDocument();
      });
      
      // Should not show any error messages
      expect(screen.queryByText(/already registered/)).not.toBeInTheDocument();
      expect(screen.queryByText(/error/i)).not.toBeInTheDocument();
    });

    it('should transition through all validation states correctly for email taken', async () => {
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      
      let resolveValidation: (value: { available: boolean; message: string }) => void;
      const validationPromise = new Promise<{ available: boolean; message: string }>(resolve => {
        resolveValidation = resolve;
      });
      mockValidateEmail.mockReturnValue(validationPromise);

      render(<SignUpPage {...mockProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      const submitButton = screen.getByRole('button', { name: /create account/i });
      
      // State 1: Initial state
      expect(document.querySelector('.animate-spin')).not.toBeInTheDocument();
      expect(submitButton).not.toBeDisabled();
      
      // Trigger validation
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.blur(emailInput);
      
      // State 2: Validating state
      await waitFor(() => {
        expect(document.querySelector('.animate-spin')).toBeInTheDocument();
      });
      
      // Resolve validation with taken email
      resolveValidation!({ available: false, message: 'Email already exists' });
      
      // State 3: Validation complete, email taken (isValidating: false, hasValidated: true, isEmailTaken: true)
      await waitFor(() => {
        expect(document.querySelector('.animate-spin')).not.toBeInTheDocument();
        expect(screen.getByText('This email is already registered.')).toBeInTheDocument();
        expect(screen.getByText('Sign in instead')).toBeInTheDocument();
        expect(submitButton).toBeDisabled();
      });
    });

    it('should transition through all validation states correctly for network error', async () => {
      const { BackendApiError } = await import('@/services/backendApi');
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      
      let rejectValidation: (error: Error) => void;
      const validationPromise = new Promise<{ available: boolean; message: string }>((_, reject) => {
        rejectValidation = reject;
      });
      mockValidateEmail.mockReturnValue(validationPromise);

      render(<SignUpPage {...mockProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      const submitButton = screen.getByRole('button', { name: /create account/i });
      
      // State 1: Initial state
      expect(document.querySelector('.animate-spin')).not.toBeInTheDocument();
      expect(submitButton).not.toBeDisabled();
      
      // Trigger validation
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.blur(emailInput);
      
      // State 2: Validating state
      await waitFor(() => {
        expect(document.querySelector('.animate-spin')).toBeInTheDocument();
      });
      
      // Reject validation with network error
      rejectValidation!(new BackendApiError('Network error', 0, 'NETWORK_ERROR'));
      
      // State 3: Validation complete with error (isValidating: false, hasValidated: true, validationError: set, canRetry: true)
      await waitFor(() => {
        expect(document.querySelector('.animate-spin')).not.toBeInTheDocument();
        expect(screen.getByText(/Network error/)).toBeInTheDocument();
        expect(screen.getByText('Try again')).toBeInTheDocument();
        expect(submitButton).not.toBeDisabled(); // Should allow signup despite error
      });
    });
  });

  describe('State Reset Behavior', () => {
    it('should completely reset EmailValidationState when email changes', async () => {
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      mockValidateEmail.mockResolvedValue({ available: false, message: 'Email already exists' });

      render(<SignUpPage {...mockProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      const submitButton = screen.getByRole('button', { name: /create account/i });
      
      // Validate first email
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.blur(emailInput);

      // Wait for validation to complete and set state
      await waitFor(() => {
        expect(screen.getByText('This email is already registered.')).toBeInTheDocument();
        expect(submitButton).toBeDisabled();
      });

      // Change email - should reset all validation state
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });

      // All validation state should be reset immediately
      expect(screen.queryByText('This email is already registered.')).not.toBeInTheDocument();
      expect(screen.queryByText('Sign in instead')).not.toBeInTheDocument();
      expect(screen.queryByText(/error/i)).not.toBeInTheDocument();
      expect(screen.queryByText('Try again')).not.toBeInTheDocument();
      expect(submitButton).not.toBeDisabled();
    });

    it('should reset retry state when email field is focused after error', async () => {
      const { BackendApiError } = await import('@/services/backendApi');
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      mockValidateEmail.mockRejectedValue(new BackendApiError('Network error', 0, 'NETWORK_ERROR'));

      render(<SignUpPage {...mockProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      
      // Trigger validation that fails
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.blur(emailInput);

      // Wait for error state
      await waitFor(() => {
        expect(screen.getByText(/Network error/)).toBeInTheDocument();
        expect(screen.getByText('Try again')).toBeInTheDocument();
      });

      // Focus email field - should clear error state for retry
      fireEvent.focus(emailInput);

      // Error message should be cleared (indicating retry capability)
      await waitFor(() => {
        expect(screen.queryByText(/Network error/)).not.toBeInTheDocument();
        expect(screen.queryByText('Try again')).not.toBeInTheDocument();
      });
    });
  });

  describe('State Persistence and Memory', () => {
    it('should maintain hasValidated state across multiple validations', async () => {
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      
      render(<SignUpPage {...mockProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      
      // First validation
      mockValidateEmail.mockResolvedValueOnce({ available: true, message: 'Available' });
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.blur(emailInput);

      await waitFor(() => {
        expect(mockValidateEmail).toHaveBeenCalledWith('<EMAIL>');
      });

      // Change email and validate again
      mockValidateEmail.mockResolvedValueOnce({ available: false, message: 'Taken' });
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.blur(emailInput);

      await waitFor(() => {
        expect(mockValidateEmail).toHaveBeenCalledWith('<EMAIL>');
        expect(screen.getByText('This email is already registered.')).toBeInTheDocument();
      });

      // Both validations should have been called, indicating hasValidated was maintained
      expect(mockValidateEmail).toHaveBeenCalledTimes(2);
    });

    it('should track retry count correctly', async () => {
      const { BackendApiError } = await import('@/services/backendApi');
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      
      render(<SignUpPage {...mockProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      
      // First attempt fails
      mockValidateEmail.mockRejectedValueOnce(new BackendApiError('Network error', 0, 'NETWORK_ERROR'));
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.blur(emailInput);

      await waitFor(() => {
        expect(screen.getByText('Try again')).toBeInTheDocument();
      });

      // Retry - should increment retry count
      mockValidateEmail.mockResolvedValueOnce({ available: true, message: 'Available' });
      const retryButton = screen.getByText('Try again');
      fireEvent.click(retryButton);

      await waitFor(() => {
        expect(mockValidateEmail).toHaveBeenCalledTimes(2);
        expect(screen.queryByText('Try again')).not.toBeInTheDocument();
      });
    });
  });

  describe('State Validation Logic', () => {
    it('should not allow invalid state combinations', async () => {
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      
      render(<SignUpPage {...mockProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      
      // Test that isValidating and hasValidated cannot both be true with error state
      let resolveValidation: (value: { available: boolean; message: string }) => void;
      const validationPromise = new Promise<{ available: boolean; message: string }>(resolve => {
        resolveValidation = resolve;
      });
      mockValidateEmail.mockReturnValue(validationPromise);

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.blur(emailInput);

      // During validation, should show loading but no error messages
      await waitFor(() => {
        expect(document.querySelector('.animate-spin')).toBeInTheDocument();
        expect(screen.queryByText(/error/i)).not.toBeInTheDocument();
        expect(screen.queryByText(/already registered/)).not.toBeInTheDocument();
      });

      resolveValidation!({ available: true, message: 'Available' });

      // After validation, should not show loading
      await waitFor(() => {
        expect(document.querySelector('.animate-spin')).not.toBeInTheDocument();
      });
    });

    it('should handle state transitions atomically', async () => {
      const mockValidateEmail = vi.mocked(authApi.validateEmail);
      mockValidateEmail.mockResolvedValue({ available: false, message: 'Email taken' });

      render(<SignUpPage {...mockProps} />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email');
      const submitButton = screen.getByRole('button', { name: /create account/i });
      
      // Trigger validation
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.blur(emailInput);

      // State should transition atomically - either loading OR error, never both
      let sawLoadingAndError = false;
      
      const checkStates = () => {
        const hasLoading = !!document.querySelector('.animate-spin');
        const hasError = !!screen.queryByText(/already registered/);
        
        if (hasLoading && hasError) {
          sawLoadingAndError = true;
        }
      };

      // Check states multiple times during transition
      const interval = setInterval(checkStates, 10);
      
      await waitFor(() => {
        expect(screen.getByText('This email is already registered.')).toBeInTheDocument();
        expect(submitButton).toBeDisabled();
      });

      clearInterval(interval);
      
      // Should never have seen both loading and error at the same time
      expect(sawLoadingAndError).toBe(false);
    });
  });
});