import { useState, useEffect } from 'react';
import { Cloud, Sun, Shirt, ChevronRight, MapPin, Calendar, TrendingUp, Sparkles, CloudRain, Loader2, RefreshCw } from 'lucide-react';
import { GlassCard } from '@/components/GlassCard';
import { cn } from '@/lib/utils';
import { useUserProfile } from '@/hooks/useUserProfile';
import { ClothingApiService, ClothingItem } from '@/services/clothingApi';
import { useNavigate } from 'react-router-dom';

interface OutfitSuggestion {
  id: string;
  name: string;
  weather: string;
  items: string[];
  temperature: number;
  style: string;
}

export const Home = () => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [clothingItems, setClothingItems] = useState<ClothingItem[]>([]);
  const [isLoadingItems, setIsLoadingItems] = useState(false);
  const { userProfile, weatherData, isLoadingWeather, refreshWeather } = useUserProfile();
  const navigate = useNavigate();

  // Generate outfit suggestions from user's clothing items
  const generateOutfitSuggestions = (): OutfitSuggestion[] => {
    if (clothingItems.length < 2) return [];

    const suggestions: OutfitSuggestion[] = [];
    const temperature = weatherData?.main?.temp ? Math.round(weatherData.main.temp) : 20;

    // Group items by category
    const itemsByCategory: Record<string, ClothingItem[]> = {};
    clothingItems.forEach(item => {
      const category = item.category_name || 'Other';
      if (!itemsByCategory[category]) {
        itemsByCategory[category] = [];
      }
      itemsByCategory[category].push(item);
    });

    // Create outfit combinations
    const categories = Object.keys(itemsByCategory);
    const maxSuggestions = Math.min(3, Math.floor(clothingItems.length / 2));

    for (let i = 0; i < maxSuggestions; i++) {
      const selectedItems: string[] = [];
      const usedCategories = new Set<string>();

      // Try to pick items from different categories
      const shuffledCategories = [...categories].sort(() => Math.random() - 0.5);

      for (const category of shuffledCategories) {
        if (selectedItems.length >= 3) break;
        if (usedCategories.has(category)) continue;

        const categoryItems = itemsByCategory[category];
        if (categoryItems.length > 0) {
          const randomItem = categoryItems[Math.floor(Math.random() * categoryItems.length)];
          selectedItems.push(randomItem.name);
          usedCategories.add(category);
        }
      }

      if (selectedItems.length >= 2) {
        const weatherCondition = weatherData?.weather?.[0]?.main || 'Clear';
        const style = ['Casual', 'Smart Casual', 'Comfortable', 'Stylish'][Math.floor(Math.random() * 4)];

        suggestions.push({
          id: `suggestion-${i}`,
          name: `${style} Look`,
          weather: weatherCondition,
          items: selectedItems,
          temperature,
          style
        });
      }
    }

    return suggestions;
  };

  const outfitSuggestions = generateOutfitSuggestions();

  useEffect(() => {
    // Trigger loading animation
    const timer = setTimeout(() => setIsLoaded(true), 100);

    // Update time every minute
    const timeInterval = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    return () => {
      clearTimeout(timer);
      clearInterval(timeInterval);
    };
  }, []);

  // Load clothing items when user profile is available
  useEffect(() => {
    const loadClothingItems = async () => {
      if (userProfile?.id) {
        setIsLoadingItems(true);
        try {
          const items = await ClothingApiService.getClothingItems(userProfile.id);
          setClothingItems(items);
        } catch (error) {
          console.error('Failed to load clothing items:', error);
        } finally {
          setIsLoadingItems(false);
        }
      }
    };

    loadClothingItems();
  }, [userProfile?.id]);

  // Generate closet summary
  const getClosetSummary = () => {
    if (isLoadingItems) return 'Loading...';
    if (clothingItems.length === 0) return '0 items';

    const categoryCounts: Record<string, number> = {};
    clothingItems.forEach(item => {
      const category = item.category_name || 'Other';
      categoryCounts[category] = (categoryCounts[category] || 0) + 1;
    });

    const topCategories = Object.entries(categoryCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([category, count]) => `${count} ${category.toLowerCase()}${count === 1 ? '' : 's'}`);

    if (topCategories.length === 0) return '0 items';
    if (topCategories.length === 1) return topCategories[0];
    if (topCategories.length === 2) return `${topCategories[0]}, ${topCategories[1]}`;
    return `${topCategories[0]}, ${topCategories[1]}, +${clothingItems.length - categoryCounts[Object.keys(categoryCounts)[0]] - categoryCounts[Object.keys(categoryCounts)[1]]} more`;
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'long',
      day: 'numeric'
    });
  };

  // Get weather icon based on weather condition
  const getWeatherIcon = (weatherCode: string) => {
    const code = weatherCode.toLowerCase();
    if (code.includes('rain') || code.includes('drizzle')) {
      return CloudRain;
    } else if (code.includes('cloud')) {
      return Cloud;
    } else {
      return Sun;
    }
  };

  // Get display city name
  const getDisplayCity = () => {
    if (weatherData?.name) {
      return weatherData.name;
    }
    if (userProfile?.cityName) {
      return userProfile.cityName.split(',')[0]; // Get just the city name part
    }
    return 'Your Location';
  };

  // Get weather description
  const getWeatherDescription = () => {
    if (weatherData?.weather?.[0]) {
      return weatherData.weather[0].description
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
    }
    return 'Weather unavailable';
  };

  // Get temperature
  const getTemperature = () => {
    if (weatherData?.main?.temp) {
      return Math.round(weatherData.main.temp);
    }
    return '--';
  };

  return (
    <div className="min-h-screen bg-background relative overflow-hidden">
      {/* Liquid glass background with subtle pattern */}
      <div className="absolute inset-0">
        <div className="w-full h-full bg-gradient-to-br from-background via-muted/30 to-background" />
        <div className="absolute inset-0 opacity-[0.02]">
          <div className="w-full h-full" style={{
            backgroundImage: `radial-gradient(circle at 25% 25%, hsl(var(--foreground)) 1px, transparent 1px)`,
            backgroundSize: '60px 60px'
          }} />
        </div>
      </div>

      {/* Floating glass orbs for ambient effect */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-32 h-32 glass-panel rounded-full opacity-20 animate-refraction-pulse"
             style={{ animationDuration: '4s', animationDelay: '0s' }} />
        <div className="absolute bottom-1/3 right-1/4 w-24 h-24 glass-panel rounded-full opacity-15 animate-refraction-pulse"
             style={{ animationDuration: '6s', animationDelay: '2s' }} />
        <div className="absolute top-1/2 right-1/3 w-16 h-16 glass-panel rounded-full opacity-10 animate-refraction-pulse"
             style={{ animationDuration: '5s', animationDelay: '1s' }} />
      </div>

      {/* Main Content */}
      <div className={cn(
        "relative z-10 transition-all duration-1000 ease-liquid",
        isLoaded ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
      )}>
        {/* Hero Section */}
        <div className="px-6 pt-16 pb-8">
          <div className={cn(
            "transition-all duration-800 ease-premium",
            isLoaded ? "opacity-100 translate-y-0" : "opacity-0 translate-y-4"
          )} style={{ animationDelay: '200ms' }}>
            <h1 className="zara-hero mb-2">Good {currentTime.getHours() < 12 ? 'Morning' : currentTime.getHours() < 18 ? 'Afternoon' : 'Evening'}</h1>
            <p className="zara-subtitle text-muted-foreground">
              {formatDate(currentTime)} • {formatTime(currentTime)}
            </p>
          </div>
        </div>

        {/* Enhanced Weather Card */}
        <div className="px-6 mb-8">
          <GlassCard
            variant="hero"
            className={cn(
              "p-8 transition-all duration-1000 ease-liquid",
              isLoaded ? "opacity-100 translate-y-0" : "opacity-0 translate-y-6"
            )}
            style={{ animationDelay: '400ms' }}
          >
            <div className="flex items-center justify-between">
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <MapPin size={16} className="text-zara-dark-gray" />
                  <h2 className="zara-title">{getDisplayCity()}</h2>
                  {isLoadingWeather && (
                    <Loader2 size={16} className="text-zara-dark-gray animate-spin" />
                  )}
                </div>
                <p className="zara-body text-zara-dark-gray">
                  {formatDate(currentTime)}
                </p>
                {userProfile && !weatherData && !isLoadingWeather && (
                  <button
                    onClick={refreshWeather}
                    className="flex items-center space-x-1 text-zara-dark-gray hover:text-zara-charcoal transition-colors"
                  >
                    <RefreshCw size={12} />
                    <span className="zara-caption">Refresh weather</span>
                  </button>
                )}
              </div>

              <div className="text-right space-y-2">
                <div className="flex items-center justify-end space-x-3">
                  <div className="p-3 glass-subtle rounded-full">
                    {isLoadingWeather ? (
                      <Loader2 size={24} className="text-zara-dark-gray animate-spin" />
                    ) : (
                      (() => {
                        const WeatherIcon = weatherData?.weather?.[0]
                          ? getWeatherIcon(weatherData.weather[0].main)
                          : Sun;
                        return <WeatherIcon size={24} className="text-yellow-500" />;
                      })()
                    )}
                  </div>
                  <span className="text-4xl font-extralight text-zara-charcoal">
                    {getTemperature()}°
                  </span>
                </div>
                <p className="zara-body text-zara-dark-gray">{getWeatherDescription()}</p>
                {weatherData?.main?.feels_like && (
                  <p className="zara-caption">
                    Feels like {Math.round(weatherData.main.feels_like)}°
                  </p>
                )}
                {!weatherData && !isLoadingWeather && userProfile && (
                  <p className="zara-caption text-zara-medium-gray">
                    Weather data unavailable
                  </p>
                )}
              </div>
            </div>
          </GlassCard>
        </div>

        {/* Quick Stats Grid */}
        <div className="px-6 mb-12">
          <div className="grid grid-cols-2 gap-4">
            <GlassCard
              variant="product"
              className={cn(
                "p-6 spring-hover cursor-pointer transition-all duration-800 ease-premium",
                isLoaded ? "opacity-100 translate-y-0" : "opacity-0 translate-y-6"
              )}
              style={{ animationDelay: '600ms' }}
            >
              <div className="space-y-3">
                <div className="p-3 glass-subtle rounded-full w-fit">
                  <Shirt size={20} className="text-zara-charcoal" />
                </div>
                <div>
                  <h3 className="zara-subtitle">Your Closet</h3>
                  <p className="zara-body text-zara-dark-gray">{getClosetSummary()}</p>
                </div>
              </div>
            </GlassCard>

            <GlassCard
              variant="product"
              className={cn(
                "p-6 spring-hover cursor-pointer transition-all duration-800 ease-premium",
                isLoaded ? "opacity-100 translate-y-0" : "opacity-0 translate-y-6"
              )}
              style={{ animationDelay: '700ms' }}
            >
              <div className="space-y-3">
                <div className="p-3 glass-subtle rounded-full w-fit">
                  <Calendar size={20} className="text-zara-charcoal" />
                </div>
                <div>
                  <h3 className="zara-subtitle">Planned</h3>
                  <p className="zara-body text-zara-dark-gray">0 outfits</p>
                </div>
              </div>
            </GlassCard>
          </div>
        </div>

        {/* Today's Suggestions Section */}
        <div className="px-6 pb-32">
          <div className={cn(
            "transition-all duration-1000 ease-liquid",
            isLoaded ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
          )} style={{ animationDelay: '800ms' }}>
            <div className="flex items-center justify-between mb-8">
              <h2 className="zara-title">Today's Suggestions</h2>
              <div className="flex items-center space-x-2">
                <Sparkles size={16} className="text-zara-dark-gray" />
                <span className="zara-caption">Curated for you</span>
              </div>
            </div>

            {outfitSuggestions.length > 0 ? (
              <div className="grid grid-cols-1 gap-6">
                {outfitSuggestions.map((outfit, index) => (
                  <GlassCard
                    key={outfit.id}
                    variant="product"
                    className="p-8 glass-morphing cursor-pointer"
                    onClick={() => console.log('Outfit selected:', outfit.name)}
                  >
                    <div className="space-y-6">
                      <div className="flex items-start justify-between">
                        <div className="space-y-2">
                          <h3 className="zara-subtitle">{outfit.name}</h3>
                          <p className="zara-body text-zara-dark-gray">{outfit.style}</p>
                        </div>
                        <div className="text-right space-y-1">
                          <span className="zara-caption bg-glass-subtle px-3 py-1 rounded-full">
                            {outfit.weather}
                          </span>
                          <p className="zara-caption text-zara-dark-gray">{outfit.temperature}°</p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-1">
                        {outfit.items.map((item: string, itemIndex: number) => (
                          <span key={itemIndex} className="zara-body text-zara-dark-gray">
                            {item}
                            {itemIndex < outfit.items.length - 1 && (
                              <span className="mx-2 text-zara-medium-gray">•</span>
                            )}
                          </span>
                        ))}
                      </div>

                      <div className="flex space-x-3">
                        {[1, 2, 3].map((item) => (
                          <div
                            key={item}
                            className="w-16 h-16 glass-product rounded-xl flex items-center justify-center hover:scale-105 transition-transform duration-300"
                          >
                            <Shirt size={20} className="text-zara-dark-gray" />
                          </div>
                        ))}
                        <div className="flex-1 flex items-center justify-center">
                          <ChevronRight size={20} className="text-zara-medium-gray" />
                        </div>
                      </div>
                    </div>
                  </GlassCard>
                ))}
              </div>
            ) : (
              <GlassCard variant="hero" className="p-12">
                <div className="text-center space-y-6">
                  <div className="w-20 h-20 mx-auto glass-medium rounded-full flex items-center justify-center">
                    <Sparkles size={32} className="text-zara-dark-gray" />
                  </div>
                  <div className="space-y-3">
                    <h3 className="zara-title">Ready to Style?</h3>
                    <p className="zara-body text-zara-dark-gray max-w-md mx-auto leading-relaxed">
                      Add clothes to your closet to get personalized outfit recommendations
                      tailored to the weather and your style preferences.
                    </p>
                  </div>
                  <div className="pt-4">
                    <button
                      onClick={() => navigate('/closet')}
                      className="glass-medium px-8 py-3 rounded-xl zara-body spring-hover hover:glass-strong hover:shadow-lg"
                    >
                      Start Building Your Closet
                    </button>
                  </div>
                </div>
              </GlassCard>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};