import { useState, useEffect } from 'react';
import { ArrowLeft, Eye, EyeOff, Mail, Lock, Sparkles, AlertCircle } from 'lucide-react';
import { GlassCard } from '@/components/GlassCard';
import { GlassButton } from '@/components/ui/glass-button';
import { GlassInput } from '@/components/ui/glass-input';
import PersistentAuthService from '@/services/persistentAuth';
import { cn } from '@/lib/utils';

interface LoginPageProps {
  onBack: () => void;
  onSignUp: () => void;
  onSuccess: () => void;
  initialEmail?: string;
}

interface LoginFormData {
  email: string;
  password: string;
}

interface LoginErrors {
  email?: string;
  password?: string;
  general?: string;
}

export const LoginPage = ({ onBack, onSignUp, onSuccess, initialEmail }: LoginPageProps) => {
  // Ensure initialEmail is properly handled as a string
  const safeInitialEmail = typeof initialEmail === 'string' ? initialEmail : '';

  const [formData, setFormData] = useState<LoginFormData>({
    email: safeInitialEmail,
    password: ''
  });
  const [errors, setErrors] = useState<LoginErrors>({});
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // Update email field when initialEmail prop changes
  useEffect(() => {
    const safeEmail = typeof initialEmail === 'string' ? initialEmail : '';
    if (safeEmail && safeEmail !== formData.email) {
      setFormData(prev => ({ ...prev, email: safeEmail }));
    }
  }, [initialEmail]);

  const validateForm = (): boolean => {
    const newErrors: LoginErrors = {};

    // Email validation
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setErrors({});

    try {
      const result = await PersistentAuthService.signIn({
        email: formData.email,
        password: formData.password
      });

      if (result.success) {
        onSuccess();
      } else {
        setErrors({ general: result.error || 'Login failed. Please try again.' });
      }
    } catch (error) {
      setErrors({ 
        general: 'An unexpected error occurred. Please try again.' 
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: keyof LoginFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear field-specific error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-zara-white via-zara-light-gray/30 to-zara-white flex items-center justify-center p-6">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <button
            onClick={onBack}
            className="inline-flex items-center text-zara-dark-gray hover:text-zara-charcoal transition-colors mb-6"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </button>
          
          <div className="flex items-center justify-center space-x-3 mb-6">
            <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-zara-charcoal to-zara-dark-gray flex items-center justify-center">
              <Sparkles className="w-7 h-7 text-white" />
            </div>
            <span className="zara-h3 text-zara-charcoal">Closet Glass Chic</span>
          </div>
          
          <h1 className="zara-h2 text-zara-charcoal mb-2">Welcome Back</h1>
          <p className="zara-body text-zara-dark-gray">
            Sign in to access your digital closet
          </p>
        </div>

        {/* Login Form */}
        <GlassCard variant="prominent" className="p-8">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* General Error */}
            {errors.general && (
              <div className="glass-subtle p-4 rounded-xl border border-red-200">
                <div className="flex items-start space-x-3">
                  <AlertCircle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                  <p className="zara-caption text-red-700">{errors.general}</p>
                </div>
              </div>
            )}

            {/* Email Field */}
            <div className="space-y-2">
              <label className="zara-body text-zara-charcoal block">
                Email Address
              </label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-zara-dark-gray" />
                <GlassInput
                  type="email"
                  placeholder="Enter your email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className={cn(
                    "pl-11",
                    errors.email && "border-red-300 focus:border-red-500"
                  )}
                  disabled={isLoading}
                />
              </div>
              {errors.email && (
                <p className="zara-caption text-red-600">{errors.email}</p>
              )}
            </div>

            {/* Password Field */}
            <div className="space-y-2">
              <label className="zara-body text-zara-charcoal block">
                Password
              </label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-zara-dark-gray" />
                <GlassInput
                  type={showPassword ? "text" : "password"}
                  placeholder="Enter your password"
                  value={formData.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  className={cn(
                    "pl-11 pr-11",
                    errors.password && "border-red-300 focus:border-red-500"
                  )}
                  disabled={isLoading}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-zara-dark-gray hover:text-zara-charcoal transition-colors"
                  disabled={isLoading}
                >
                  {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
              {errors.password && (
                <p className="zara-caption text-red-600">{errors.password}</p>
              )}
            </div>

            {/* Forgot Password Link */}
            <div className="text-right">
              <button
                type="button"
                className="zara-caption text-zara-dark-gray hover:text-zara-charcoal transition-colors"
                disabled={isLoading}
              >
                Forgot your password?
              </button>
            </div>

            {/* Submit Button */}
            <GlassButton
              type="submit"
              variant="primary"
              size="lg"
              className="w-full"
              disabled={isLoading}
            >
              {isLoading ? 'Signing In...' : 'Sign In'}
            </GlassButton>

            {/* Divider */}
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-zara-medium-gray/30" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-4 bg-white/80 zara-caption text-zara-dark-gray">
                  Don't have an account?
                </span>
              </div>
            </div>

            {/* Sign Up Link */}
            <GlassButton
              type="button"
              variant="secondary"
              size="lg"
              className="w-full"
              onClick={onSignUp}
              disabled={isLoading}
            >
              Create New Account
            </GlassButton>
          </form>
        </GlassCard>

        {/* Security Note */}
        <div className="mt-6 text-center">
          <p className="zara-caption text-zara-dark-gray">
            Your data is encrypted and secure. We never share your information with third parties.
          </p>
        </div>
      </div>
    </div>
  );
};
