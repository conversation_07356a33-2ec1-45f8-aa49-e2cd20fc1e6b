import { User, <PERSON>ting<PERSON>, <PERSON>, Shield, HelpCircle, Loader2, LogOut } from 'lucide-react';
import { GlassCard } from '@/components/GlassCard';
import { GlassButton } from '@/components/ui/glass-button';
import { useUserProfile } from '@/hooks/useUserProfile';
import fashionHeroBg from '@/assets/fashion-hero-bg.jpg';

interface ProfileProps {
  onLogout?: () => void;
}

export const Profile = ({ onLogout }: ProfileProps) => {
  const { userProfile, isLoadingProfile, error } = useUserProfile();

  const handleSignOut = () => {
    if (onLogout) {
      // Use the enhanced logout handler from App.tsx
      onLogout();
    } else {
      // Fallback to page reload if no logout handler provided
      console.warn('No logout handler provided, falling back to page reload');
      window.location.reload();
    }
  };

  const profileSections = [
    {
      title: 'Account',
      items: [
        { icon: User, label: 'Personal Information', action: () => {} },
        { icon: Settings, label: 'Preferences', action: () => {} }
      ]
    },
    {
      title: 'Notifications',
      items: [
        { icon: Bell, label: 'Weather Alerts', action: () => {} },
        { icon: Bell, label: 'Outfit Reminders', action: () => {} }
      ]
    },
    {
      title: 'Support',
      items: [
        { icon: Shield, label: 'Privacy & Security', action: () => {} },
        { icon: HelpCircle, label: 'Help & Support', action: () => {} }
      ]
    },
    {
      title: 'Session',
      items: [
        { icon: LogOut, label: 'Sign Out', action: handleSignOut, destructive: true }
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-background relative overflow-hidden">
      {/* Liquid glass background with subtle pattern */}
      <div className="absolute inset-0">
        <div className="w-full h-full bg-gradient-to-br from-background via-muted/30 to-background" />
        <div className="absolute inset-0 opacity-[0.02] z-0 pointer-events-none">
          <div className="w-full h-full" style={{
            backgroundImage: `radial-gradient(circle at 25% 25%, hsl(var(--foreground)) 1px, transparent 1px)`,
            backgroundSize: '60px 60px'
          }} />
        </div>
      </div>
      {/* Content */}
      <div className="relative z-10 px-6 pt-20 pb-32">
        {/* Header */}
        <h1 className="zara-title mb-6">Profile</h1>
        {/* Profile card */}
        <div className="max-w-xl mx-auto">
          <GlassCard variant="hero" className="p-6 mb-6">
            <div className="flex items-center space-x-4">
              {/* Profile photo */}
              <div className="relative w-16 h-16">
                <div className="w-full h-full rounded-full glass-panel flex items-center justify-center">
                  <User size={28} className="text-muted-foreground" />
                </div>
                <div className="absolute inset-0 rounded-full ring-2 ring-border/40" />
              </div>
              {/* Profile info */}
              <div className="flex-1">
                {isLoadingProfile ? (
                  <div className="flex items-center space-x-2">
                    <Loader2 size={16} className="animate-spin text-muted-foreground" />
                    <span className="zara-body text-muted-foreground">Loading profile...</span>
                  </div>
                ) : userProfile ? (
                  <>
                    <h2 className="zara-subtitle">
                      {userProfile.firstName} {userProfile.lastName}
                    </h2>
                    <p className="zara-body text-muted-foreground">{userProfile.cityName}</p>
                    <p className="zara-body text-muted-foreground mt-1">
                      Member since {userProfile.createdAt ? new Date(userProfile.createdAt).toLocaleDateString('en-US', { month: 'long', year: 'numeric' }) : 'Recently'}
                    </p>
                  </>
                ) : (
                  <>
                    <h2 className="zara-subtitle">Welcome</h2>
                    <p className="zara-body text-muted-foreground">Complete your profile to get started</p>
                  </>
                )}
              </div>
              <GlassButton variant="secondary" size="sm">
                Edit
              </GlassButton>
            </div>
          </GlassCard>
        </div>
        {/* Profile sections */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-3xl mx-auto">
          {profileSections.map((section) => (
            <GlassCard
              key={section.title}
              variant="product"
              className="p-5"
            >
              <h2 className="mb-4 zara-title">
                {section.title}
              </h2>
              <div className="space-y-3">
                {section.items.map((item) => (
                  <button
                    key={item.label}
                    className={`flex items-center w-full px-3 py-2 rounded-lg transition group ${
                      (item as any).destructive
                        ? 'hover:bg-red-50 text-red-600'
                        : 'hover:glass-light'
                    }`}
                    onClick={item.action}
                  >
                    <item.icon size={20} className={`mr-3 transition ${
                      (item as any).destructive
                        ? 'text-red-500 group-hover:text-red-700'
                        : 'text-muted-foreground group-hover:text-foreground'
                    }`} />
                    <span className={`zara-body transition ${
                      (item as any).destructive
                        ? 'text-red-600 group-hover:font-semibold'
                        : 'text-foreground group-hover:font-semibold'
                    }`}>
                      {item.label}
                    </span>
                  </button>
                ))}
              </div>
            </GlassCard>
          ))}

        </div>
      </div>
    </div>
  );
};