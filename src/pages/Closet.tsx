import { useState, useEffect } from 'react';
import { Plus, Shirt, Search, Grid, List, SlidersHorizontal, ChevronRight } from 'lucide-react';
import { GlassCard } from '@/components/GlassCard';
import { GlassButton } from '@/components/ui/glass-button';
import { GlassInput } from '@/components/ui/glass-input';
import { AddClothingModal } from '@/components/AddClothingModal';
import { ClothingApiService, ClothingItem as ApiClothingItem } from '@/services/clothingApi';
import { useUserProfile } from '@/hooks/useUserProfile';
import { cn } from '@/lib/utils';
import { toast } from '@/components/ui/sonner';
import { ClothingItemSkeleton, LoadingOverlay } from '@/components/ui/loading-skeleton';

// Use the API ClothingItem type
type ClothingItem = ApiClothingItem;

interface ClothingFormData {
  image?: File;
  processedImageUrl?: string;
  category: string;
  color: string;
  brand: string;
  size: string;
  season: string;
}

export const Closet = () => {
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isLoaded, setIsLoaded] = useState(false);
  const [clothingItems, setClothingItems] = useState<ClothingItem[]>([]);
  const [isLoadingItems, setIsLoadingItems] = useState(false);
  const [newlyAddedItems, setNewlyAddedItems] = useState<Set<string>>(new Set());
  const { userProfile } = useUserProfile();

  const categories = ['All', 'Tops', 'Bottoms', 'Outerwear', 'Dresses', 'Shoes', 'Accessories'];

  // Category mapping from filter categories to database categories
  const categoryMapping: Record<string, string[]> = {
    'Tops': ['T-Shirts', 'Blouses', 'Sweaters', 'Shirts', 'Tank Tops', 'Hoodies'],
    'Bottoms': ['Jeans', 'Trousers', 'Skirts', 'Shorts', 'Leggings', 'Pants'],
    'Shoes': ['Sneakers', 'Heels', 'Boots', 'Sandals', 'Flats', 'Loafers'],
    'Accessories': ['Bags', 'Jewelry', 'Belts', 'Hats', 'Scarves', 'Watches'],
    'Outerwear': ['Jackets', 'Coats', 'Blazers', 'Cardigans', 'Vests'],
    'Dresses': ['Dresses', 'Gowns', 'Sundresses']
  };

  // Function to check if an item belongs to a filter category
  const itemBelongsToCategory = (item: ClothingItem, filterCategory: string): boolean => {
    if (filterCategory === 'All') return true;

    const itemCategory = item.category_name || '';
    const mappedCategories = categoryMapping[filterCategory] || [];

    return mappedCategories.some(category =>
      itemCategory.toLowerCase().includes(category.toLowerCase()) ||
      category.toLowerCase().includes(itemCategory.toLowerCase())
    );
  };

  // Get item count for each category
  const getCategoryCount = (category: string): number => {
    if (category === 'All') return clothingItems.length;
    return clothingItems.filter(item => itemBelongsToCategory(item, category)).length;
  };

  useEffect(() => {
    const timer = setTimeout(() => setIsLoaded(true), 100);
    return () => clearTimeout(timer);
  }, []);

  // Load clothing items when user profile is available
  useEffect(() => {
    const loadClothingItems = async () => {
      if (userProfile?.id) {
        setIsLoadingItems(true);
        try {
          const items = await ClothingApiService.getClothingItems(userProfile.id);
          setClothingItems(items);
        } catch (error) {
          console.error('Failed to load clothing items:', error);
        } finally {
          setIsLoadingItems(false);
        }
      }
    };

    loadClothingItems();
  }, [userProfile?.id]);

  const handleSaveClothingItem = async (data: ClothingFormData) => {
    if (!userProfile?.id) {
      throw new Error('User not logged in');
    }

    // Create optimistic item for immediate UI update
    const optimisticItem: ClothingItem = {
      id: `temp-${Date.now()}`, // Temporary ID
      name: `${data.brand || 'Unknown'} ${data.category}`,
      category: data.category,
      color: data.color,
      brand: data.brand,
      size: data.size,
      season: data.season,
      image_url: data.processedImageUrl,
      user_id: userProfile.id,
      category_id: null,
      tags: data.season ? [data.season] : [],
      status: 'active',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      category_name: data.category
    };

    // Optimistically add to local state for immediate UI feedback
    setClothingItems(prev => [optimisticItem, ...prev]);
    setIsAddModalOpen(false);

    try {
      // Save via API
      const savedItem = await ClothingApiService.saveClothingItem({
        userId: userProfile.id,
        name: `${data.brand || 'Unknown'} ${data.category}`,
        category: data.category,
        color: data.color,
        brand: data.brand,
        size: data.size,
        season: data.season,
        processedImageUrl: data.processedImageUrl
      });

      // Replace optimistic item with real saved item
      setClothingItems(prev =>
        prev.map(item =>
          item.id === optimisticItem.id ? savedItem : item
        )
      );

      // Mark as newly added for visual feedback
      setNewlyAddedItems(prev => new Set(prev).add(savedItem.id));

      // Remove the "newly added" indicator after 3 seconds
      setTimeout(() => {
        setNewlyAddedItems(prev => {
          const newSet = new Set(prev);
          newSet.delete(savedItem.id);
          return newSet;
        });
      }, 3000);

      // Show success toast
      toast.success('Item added successfully!', {
        description: `${savedItem.name} has been added to your closet.`,
        duration: 3000,
      });
    } catch (error) {
      // Remove optimistic item on error and re-open modal
      setClothingItems(prev =>
        prev.filter(item => item.id !== optimisticItem.id)
      );
      setIsAddModalOpen(true);

      // Show error toast
      const errorMessage = error instanceof Error ? error.message : 'Failed to save clothing item';
      toast.error('Failed to add item', {
        description: errorMessage,
        duration: 5000,
      });

      throw new Error(errorMessage);
    }
  };

  const filteredClothes = clothingItems.filter(item => {
    const matchesCategory = itemBelongsToCategory(item, selectedCategory);
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.color.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         (item.brand && item.brand.toLowerCase().includes(searchQuery.toLowerCase()));
    return matchesCategory && matchesSearch;
  });

  return (
    <div className="min-h-screen bg-background relative overflow-hidden">
      {/* Liquid glass background with subtle pattern */}
      <div className="absolute inset-0">
        <div className="w-full h-full bg-gradient-to-br from-background via-muted/30 to-background" />
        <div className="absolute inset-0 opacity-[0.02]">
          <div className="w-full h-full" style={{
            backgroundImage: `radial-gradient(circle at 25% 25%, hsl(var(--foreground)) 1px, transparent 1px)`,
            backgroundSize: '60px 60px'
          }} />
        </div>
      </div>

      {/* Header */}
      <div className={cn(
        "sticky top-0 z-30 glass-navigation border-b border-border transition-all duration-800 ease-liquid",
        isLoaded ? "opacity-100 translate-y-0" : "opacity-0 -translate-y-4"
      )}>
        <div className="px-6 pt-16 pb-6">
          <div className="flex items-center justify-between mb-6">
            <h1 className="zara-hero">Your Closet</h1>
            <div className="flex items-center space-x-3">
              <button
                onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
                className="p-2 glass-panel rounded-xl hover:glass-light transition-all duration-300"
              >
                {viewMode === 'grid' ? <List size={20} /> : <Grid size={20} />}
              </button>
              <button className="p-2 glass-panel rounded-xl hover:glass-light transition-all duration-300">
                <SlidersHorizontal size={20} />
              </button>
            </div>
          </div>

          {/* Search Bar */}
          <div className="mb-6">
            <div className="relative">
              <Search size={20} className="absolute left-4 top-1/2 transform -translate-y-1/2 text-zara-dark-gray" />
              <GlassInput
                variant="subtle"
                placeholder="Search your closet..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-12 pr-4"
              />
            </div>
          </div>

          {/* Enhanced Category Filter */}
          <div className="overflow-x-auto">
            <div className="flex space-x-3 pb-2">
              {categories.map((category) => {
                const count = getCategoryCount(category);
                return (
                  <button
                    key={category}
                    onClick={() => setSelectedCategory(category)}
                    className={cn(
                      "px-6 py-3 rounded-full whitespace-nowrap spring-hover transition-all duration-300 ease-glass",
                      selectedCategory === category
                        ? 'glass-strong text-zara-charcoal scale-105'
                        : 'glass-subtle text-zara-dark-gray hover:glass-light'
                    )}
                  >
                    <span className="zara-body font-medium">
                      {category}
                      {count > 0 && (
                        <span className={cn(
                          "ml-2 px-2 py-1 rounded-full text-xs",
                          selectedCategory === category
                            ? 'bg-zara-charcoal text-white'
                            : 'bg-zara-light-gray text-zara-dark-gray'
                        )}>
                          {count}
                        </span>
                      )}
                    </span>
                  </button>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className={cn(
        "relative z-10 transition-all duration-1000 ease-liquid",
        isLoaded ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
      )}>

        {/* Clothing Display */}
        <div className="px-6 pb-32">
          {isLoadingItems ? (
            <div className={cn(
              "transition-all duration-500",
              viewMode === 'grid'
                ? "grid grid-cols-2 gap-6"
                : "space-y-4"
            )}>
              {Array.from({ length: 6 }).map((_, index) => (
                <ClothingItemSkeleton
                  key={index}
                  viewMode={viewMode}
                />
              ))}
            </div>
          ) : filteredClothes.length > 0 ? (
            <div className={cn(
              "transition-all duration-500",
              viewMode === 'grid'
                ? "grid grid-cols-2 gap-6"
                : "space-y-4"
            )}>
              {filteredClothes.map((item, index) => {
                const isOptimistic = item.id.startsWith('temp-');
                const isNewlyAdded = newlyAddedItems.has(item.id);
                return (
                  <GlassCard
                    key={item.id}
                    variant="product"
                    className={cn(
                      "glass-morphing cursor-pointer transition-all duration-500 ease-premium relative spring-hover",
                      viewMode === 'grid' ? "aspect-[3/4] p-4" : "p-6",
                      isOptimistic && "opacity-75",
                      isNewlyAdded && "newly-added animate-bounce-in",
                      !isOptimistic && !isNewlyAdded && "animate-slide-in-up"
                    )}
                    onClick={() => console.log('Item selected:', item.name)}
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    {/* Loading overlay for optimistic items */}
                    <LoadingOverlay
                      isVisible={isOptimistic}
                      message="Saving item..."
                    />

                    {/* New item indicator */}
                    {isNewlyAdded && !isOptimistic && (
                      <div className="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full z-10 animate-pulse">
                        New
                      </div>
                    )}
                  {viewMode === 'grid' ? (
                    <div className="h-full flex flex-col">
                      <div className="flex-1 glass-subtle rounded-2xl flex items-center justify-center mb-4 overflow-hidden">
                        {item.image_url ? (
                          <img
                            src={item.image_url}
                            alt={item.name}
                            className="w-full h-full object-cover fashion-image"
                          />
                        ) : (
                          <div className="w-20 h-20 glass-medium rounded-xl flex items-center justify-center">
                            <Shirt size={24} className="text-muted-foreground" />
                          </div>
                        )}
                      </div>

                      <div className="space-y-2">
                        <h3 className="zara-subtitle truncate">{item.name}</h3>
                        <p className="zara-body text-muted-foreground">{item.color}</p>
                        {item.brand && (
                          <p className="zara-caption">{item.brand}</p>
                        )}
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-4">
                      <div className="w-16 h-16 glass-subtle rounded-xl flex items-center justify-center flex-shrink-0 overflow-hidden">
                        {item.image_url ? (
                          <img
                            src={item.image_url}
                            alt={item.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <Shirt size={20} className="text-muted-foreground" />
                        )}
                      </div>
                      <div className="flex-1 space-y-1">
                        <h3 className="zara-subtitle">{item.name}</h3>
                        <p className="zara-body text-muted-foreground">{item.color}</p>
                        <p className="zara-caption">{item.category_name || 'Unknown'}</p>
                      </div>
                      <ChevronRight size={20} className="text-muted-foreground" />
                    </div>
                  )}
                </GlassCard>
                );
              })}
            </div>
          ) : (
            <GlassCard variant="hero" className="p-12">
              <div className="text-center space-y-6">
                <div className="w-20 h-20 mx-auto glass-medium rounded-full flex items-center justify-center">
                  <Shirt size={32} className="text-zara-dark-gray" />
                </div>
                <div className="space-y-3">
                  <h3 className="zara-title">
                    {searchQuery ? 'No Items Found' : 'Your Closet is Empty'}
                  </h3>
                  <p className="zara-body text-zara-dark-gray max-w-md mx-auto leading-relaxed">
                    {searchQuery
                      ? `No items match "${searchQuery}". Try adjusting your search or browse different categories.`
                      : 'Start building your wardrobe by adding your first item. Organize your clothes and create amazing outfits.'
                    }
                  </p>
                </div>
                <div className="pt-4">
                  <GlassButton
                    onClick={() => setIsAddModalOpen(true)}
                    variant="primary"
                    size="lg"
                  >
                    Add Your First Item
                  </GlassButton>
                </div>
              </div>
            </GlassCard>
          )}
        </div>

      </div>

      {/* Enhanced Floating Action Button */}
      <button
        onClick={() => setIsAddModalOpen(true)}
        aria-label="Add new clothing item"
        className={cn(
          "fixed bottom-24 right-6 z-40 w-16 h-16 glass-strong rounded-full",
          "flex items-center justify-center spring-hover",
          "shadow-lg hover:shadow-xl hover:animate-pulse-glow",
          isLoaded ? "opacity-100 translate-y-0 animate-bounce-in" : "opacity-0 translate-y-8"
        )}
        style={{ animationDelay: '1000ms' }}
        onMouseEnter={(e) => {
          e.currentTarget.classList.add('animate-spring-scale');
          setTimeout(() => {
            e.currentTarget.classList.remove('animate-spring-scale');
          }, 300);
        }}
      >
        <Plus size={24} className="text-zara-charcoal" />
      </button>

      {/* Enhanced Add Clothing Modal */}
      <AddClothingModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSave={handleSaveClothingItem}
        userId={userProfile?.id}
      />
    </div>
  );
};