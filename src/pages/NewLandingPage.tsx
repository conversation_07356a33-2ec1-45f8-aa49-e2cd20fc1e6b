import HeroSection from "@/components/HeroSection";
import FeatureHighlights from "@/components/FeatureHighlights";
import MockupShowcase from "@/components/MockupShowcase";
import LiveDemo from "@/components/LiveDemo";
import Testimonials from "@/components/Testimonials";
import FinalCTA from "@/components/FinalCTA";
import Footer from "@/components/Footer";
import heroBackground from "@/assets/fashion-hero-bg.jpg";

interface NewLandingPageProps {
  onSignUp: () => void;
  onSignIn: () => void;
}

const NewLandingPage = ({ onSignUp, onSignIn }: NewLandingPageProps) => {
  return (
    <div className="min-h-screen overflow-x-hidden">
      {/* Global Background */}
      <div 
        className="fixed inset-0 z-0"
        style={{
          backgroundImage: `url(${heroBackground})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundAttachment: 'fixed'
        }}
      />
      <div className="fixed inset-0 z-0 bg-black/35" />
      
      {/* Content */}
      <div className="relative z-10">
        <HeroSection onSignUp={onSignUp} onSignIn={onSignIn} />
        <FeatureHighlights />
        <MockupShowcase />
        <LiveDemo />
        <Testimonials />
        <FinalCTA onSignUp={onSignUp} />
        <Footer />
      </div>
    </div>
  );
};

export default NewLandingPage;
