import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { render, renderHook, act } from '@testing-library/react';
import { ReactNode } from 'react';
import { OnboardingProvider, useOnboardingContext, OnboardingState } from '../OnboardingContext';

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Test wrapper component
const TestWrapper = ({ children }: { children: ReactNode }) => (
  <OnboardingProvider>{children}</OnboardingProvider>
);

describe('OnboardingContext', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Provider initialization', () => {
    it('should initialize with default state', () => {
      const { result } = renderHook(() => useOnboardingContext(), {
        wrapper: TestWrapper,
      });

      expect(result.current.state.isCompleted).toBe(false);
      expect(result.current.state.currentStep).toBe(0);
      expect(result.current.state.skippedSteps).toEqual([]);
      expect(result.current.state.userPreferences).toEqual({
        enableNotifications: false,
        preferredStyle: 'casual',
        weatherLocation: '',
      });
    });

    it('should load state from localStorage on mount', () => {
      const savedState: OnboardingState = {
        isCompleted: true,
        currentStep: 2,
        skippedSteps: ['closet-management'],
        completedAt: new Date('2024-01-01'),
        userPreferences: {
          enableNotifications: true,
          preferredStyle: 'formal',
          weatherLocation: 'New York',
        },
      };

      localStorageMock.getItem.mockReturnValue(JSON.stringify(savedState));

      const { result } = renderHook(() => useOnboardingContext(), {
        wrapper: TestWrapper,
      });

      expect(result.current.state.isCompleted).toBe(true);
      expect(result.current.state.currentStep).toBe(2);
      expect(result.current.state.skippedSteps).toEqual(['closet-management']);
      expect(result.current.state.userPreferences.enableNotifications).toBe(true);
    });

    it('should handle localStorage errors gracefully', () => {
      localStorageMock.getItem.mockImplementation(() => {
        throw new Error('localStorage error');
      });

      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

      const { result } = renderHook(() => useOnboardingContext(), {
        wrapper: TestWrapper,
      });

      expect(result.current.state.isCompleted).toBe(false);
      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to load onboarding state from localStorage:',
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });
  });

  describe('Navigation functions', () => {
    it('should navigate to next step', () => {
      const { result } = renderHook(() => useOnboardingContext(), {
        wrapper: TestWrapper,
      });

      act(() => {
        result.current.nextStep();
      });

      expect(result.current.state.currentStep).toBe(1);
    });

    it('should navigate to previous step', () => {
      const { result } = renderHook(() => useOnboardingContext(), {
        wrapper: TestWrapper,
      });

      // First go to step 1
      act(() => {
        result.current.nextStep();
      });

      // Then go back
      act(() => {
        result.current.previousStep();
      });

      expect(result.current.state.currentStep).toBe(0);
    });

    it('should not go below step 0', () => {
      const { result } = renderHook(() => useOnboardingContext(), {
        wrapper: TestWrapper,
      });

      act(() => {
        result.current.previousStep();
      });

      expect(result.current.state.currentStep).toBe(0);
    });

    it('should complete onboarding when reaching last step', () => {
      const { result } = renderHook(() => useOnboardingContext(), {
        wrapper: TestWrapper,
      });

      // Navigate to last step (step 3)
      act(() => {
        result.current.dispatch({ type: 'SET_CURRENT_STEP', payload: 3 });
      });

      // Try to go next from last step
      act(() => {
        result.current.nextStep();
      });

      expect(result.current.state.isCompleted).toBe(true);
      expect(result.current.state.completedAt).toBeInstanceOf(Date);
    });

    it('should skip step and add to skipped list', () => {
      const { result } = renderHook(() => useOnboardingContext(), {
        wrapper: TestWrapper,
      });

      // Go to step 1 (closet-management which can be skipped)
      act(() => {
        result.current.nextStep();
      });

      act(() => {
        result.current.skipStep();
      });

      expect(result.current.state.skippedSteps).toContain('closet-management');
      expect(result.current.state.currentStep).toBe(2);
    });

    it('should not skip step if canSkip is false', () => {
      const { result } = renderHook(() => useOnboardingContext(), {
        wrapper: TestWrapper,
      });

      // Stay on step 0 (welcome which cannot be skipped)
      act(() => {
        result.current.skipStep();
      });

      expect(result.current.state.skippedSteps).toEqual([]);
      expect(result.current.state.currentStep).toBe(0);
    });
  });

  describe('Helper functions', () => {
    it('should return current step data', () => {
      const { result } = renderHook(() => useOnboardingContext(), {
        wrapper: TestWrapper,
      });

      const currentStep = result.current.getCurrentStep();
      expect(currentStep?.id).toBe('welcome');
      expect(currentStep?.title).toBe('Welcome to Your Fashion Assistant');
    });

    it('should check if can go next', () => {
      const { result } = renderHook(() => useOnboardingContext(), {
        wrapper: TestWrapper,
      });

      expect(result.current.canGoNext()).toBe(true);

      // Go to last step
      act(() => {
        result.current.dispatch({ type: 'SET_CURRENT_STEP', payload: 3 });
      });

      expect(result.current.canGoNext()).toBe(false);
    });

    it('should check if can go previous', () => {
      const { result } = renderHook(() => useOnboardingContext(), {
        wrapper: TestWrapper,
      });

      expect(result.current.canGoPrevious()).toBe(false);

      act(() => {
        result.current.nextStep();
      });

      expect(result.current.canGoPrevious()).toBe(true);
    });
  });

  describe('User preferences', () => {
    it('should update user preferences', () => {
      const { result } = renderHook(() => useOnboardingContext(), {
        wrapper: TestWrapper,
      });

      act(() => {
        result.current.updateUserPreferences({
          enableNotifications: true,
          preferredStyle: 'formal',
        });
      });

      expect(result.current.state.userPreferences.enableNotifications).toBe(true);
      expect(result.current.state.userPreferences.preferredStyle).toBe('formal');
      expect(result.current.state.userPreferences.weatherLocation).toBe(''); // Should preserve existing
    });
  });

  describe('Onboarding completion', () => {
    it('should complete onboarding manually', () => {
      const { result } = renderHook(() => useOnboardingContext(), {
        wrapper: TestWrapper,
      });

      act(() => {
        result.current.completeOnboarding();
      });

      expect(result.current.state.isCompleted).toBe(true);
      expect(result.current.state.completedAt).toBeInstanceOf(Date);
    });

    it('should reset onboarding while preserving preferences', () => {
      const { result } = renderHook(() => useOnboardingContext(), {
        wrapper: TestWrapper,
      });

      // Set some state and preferences
      act(() => {
        result.current.updateUserPreferences({ enableNotifications: true });
        result.current.nextStep();
        result.current.completeOnboarding();
      });

      // Reset onboarding
      act(() => {
        result.current.resetOnboarding();
      });

      expect(result.current.state.isCompleted).toBe(false);
      expect(result.current.state.currentStep).toBe(0);
      expect(result.current.state.skippedSteps).toEqual([]);
      expect(result.current.state.userPreferences.enableNotifications).toBe(true); // Preserved
    });
  });

  describe('localStorage persistence', () => {
    it('should save state to localStorage on changes', () => {
      const { result } = renderHook(() => useOnboardingContext(), {
        wrapper: TestWrapper,
      });

      act(() => {
        result.current.nextStep();
      });

      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'onboarding-state',
        expect.stringContaining('"currentStep":1')
      );
    });

    it('should handle localStorage save errors gracefully', () => {
      localStorageMock.setItem.mockImplementation(() => {
        throw new Error('localStorage save error');
      });

      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

      const { result } = renderHook(() => useOnboardingContext(), {
        wrapper: TestWrapper,
      });

      act(() => {
        result.current.nextStep();
      });

      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to save onboarding state to localStorage:',
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });
  });

  describe('Error handling', () => {
    it('should throw error when used outside provider', () => {
      expect(() => {
        renderHook(() => useOnboardingContext());
      }).toThrow('useOnboardingContext must be used within an OnboardingProvider');
    });
  });

  describe('Steps configuration', () => {
    it('should provide correct steps configuration', () => {
      const { result } = renderHook(() => useOnboardingContext(), {
        wrapper: TestWrapper,
      });

      expect(result.current.steps).toHaveLength(4);
      expect(result.current.steps[0].id).toBe('welcome');
      expect(result.current.steps[0].canSkip).toBe(false);
      expect(result.current.steps[1].id).toBe('closet-management');
      expect(result.current.steps[1].canSkip).toBe(true);
    });
  });
});