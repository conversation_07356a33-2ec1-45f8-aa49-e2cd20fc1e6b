const Footer = () => {
  const footerLinks = [
    { label: "About", href: "#" },
    { label: "Privacy", href: "#" },
    { label: "Terms", href: "#" },
    { label: "Contact", href: "#" }
  ];

  return (
    <footer className="relative">
      {/* Liquid Glass Footer Bar */}
      <div className="glass-panel glass-refraction border-t border-white/10">
        <div className="container mx-auto px-6 py-8">
          <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
            {/* Brand */}
            <div className="text-white/80 font-light">
              © 2024 ClosetMate. Smartly styled.
            </div>
            
            {/* Links */}
            <nav className="flex space-x-8">
              {footerLinks.map((link, index) => (
                <a
                  key={index}
                  href={link.href}
                  className="text-white/70 hover:text-white transition-colors duration-300 font-light"
                >
                  {link.label}
                </a>
              ))}
            </nav>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
