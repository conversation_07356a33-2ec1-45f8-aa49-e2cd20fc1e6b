import { Button } from "@/components/ui/button";
import heroBackground from "@/assets/fashion-hero-bg.jpg";
import { useScrollRefraction } from "@/hooks/useScrollRefraction";

interface HeroSectionProps {
  onSignUp?: () => void;
  onSignIn?: () => void;
}

const HeroSection = ({ onSignUp, onSignIn }: HeroSectionProps) => {
  useScrollRefraction();
  return (
    <section className="relative h-screen flex items-center justify-center overflow-hidden">
      {/* Background Image with Parallax */}
      <div 
        className="absolute inset-0 parallax"
        style={{
          backgroundImage: `url(${heroBackground})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          transform: 'scale(1.1)',
        }}
      />
      
      {/* Clear Glass Overlay */}
      <div className="absolute inset-0 bg-black/30" />
      
      {/* Hero Content */}
      <div className="relative z-10 text-center animate-slide-in-up">
        <div className="glass-panel glass-refraction p-12 rounded-3xl max-w-2xl mx-4">
          {/* Logo/Brand */}
          <h1 className="text-6xl md:text-7xl font-extralight text-white mb-4 text-shadow tracking-wide">
            ClosetMate
          </h1>
          
          {/* Tagline */}
          <p className="text-xl md:text-2xl text-white/90 mb-8 font-light tracking-wide">
            Your Style. Smartly Styled.
          </p>
          
          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              className="glass-button shimmer text-lg px-12 py-6 animate-float"
              onClick={onSignUp}
            >
              Start Styling
            </Button>
            {onSignIn && (
              <Button 
                variant="outline"
                className="glass-button shimmer text-lg px-12 py-6 animate-float"
                onClick={onSignIn}
              >
                Sign In
              </Button>
            )}
          </div>
        </div>
      </div>
      
      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-white/50 rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
