import { useState } from 'react';
import { useOnboarding } from '@/hooks/useOnboarding';
import { OnboardingLayout } from './OnboardingLayout';
import { WelcomeScreen } from './WelcomeScreen';
import { PersonalInfoStep, DateOfBirthStep, LocationStep, AccountCreationStep } from './steps';
import { cn } from '@/lib/utils';
import { UserProfileFormData, UserProfileFormErrors } from '@/types/user';
import PersistentAuthService from '@/services/persistentAuth';
import { onboardingLogger } from '@/utils/logger';

interface MultiStepOnboardingFlowProps {
  onComplete: () => void;
}

export const MultiStepOnboardingFlow = ({ onComplete }: MultiStepOnboardingFlowProps) => {
  const {
    currentStep,
    totalSteps,
    currentStepData,
    nextStep,
    previousStep,
    completeOnboarding,
    updateUserProfile,
    canGoPrevious,
    isFirstStep
  } = useOnboarding();

  const [isTransitioning, setIsTransitioning] = useState(false);
  const [direction, setDirection] = useState<'forward' | 'backward'>('forward');
  const [isLoading, setIsLoading] = useState(false);

  // Form data state
  const [formData, setFormData] = useState<UserProfileFormData>({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    gender: '',
    dateOfBirth: '',
    cityName: '',
  });

  const [errors, setErrors] = useState<UserProfileFormErrors>({});

  // Handle smooth transitions between screens
  const handleTransition = (callback: () => void, transitionDirection: 'forward' | 'backward' = 'forward') => {
    setDirection(transitionDirection);
    setIsTransitioning(true);
    
    setTimeout(() => {
      callback();
      setTimeout(() => {
        setIsTransitioning(false);
      }, 100);
    }, 300);
  };

  // Validation functions
  const validatePersonalInfo = () => {
    const newErrors: UserProfileFormErrors = {};
    
    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
    }
    
    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    }
    
    if (!formData.gender) {
      newErrors.gender = 'Gender is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateDateOfBirth = () => {
    const newErrors: UserProfileFormErrors = {};
    
    if (!formData.dateOfBirth) {
      newErrors.dateOfBirth = 'Date of birth is required';
    } else {
      const birthDate = new Date(formData.dateOfBirth);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      
      if (age < 13) {
        newErrors.dateOfBirth = 'You must be at least 13 years old';
      }
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateLocation = () => {
    const newErrors: UserProfileFormErrors = {};
    
    if (!formData.cityName.trim()) {
      newErrors.cityName = 'City is required';
    }
    
    if (!formData.selectedCity) {
      newErrors.cityName = 'Please select a city from the dropdown';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateAccountCreation = () => {
    const newErrors: UserProfileFormErrors = {};
    
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!PersistentAuthService.validateEmail(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else {
      const passwordValidation = PersistentAuthService.validatePassword(formData.password);
      if (!passwordValidation.isValid) {
        newErrors.password = passwordValidation.errors[0];
      }
    }
    
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password';
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Step handlers
  const handlePersonalInfoNext = () => {
    if (validatePersonalInfo()) {
      handleTransition(nextStep, 'forward');
    }
  };

  const handleDateOfBirthNext = () => {
    if (validateDateOfBirth()) {
      handleTransition(nextStep, 'forward');
    }
  };

  const handleLocationNext = () => {
    if (validateLocation()) {
      handleTransition(nextStep, 'forward');
    }
  };

  const handleAccountCreationComplete = async () => {
    if (!validateAccountCreation()) {
      return;
    }

    setIsLoading(true);
    try {
      onboardingLogger.info('Starting account creation process');

      // Prepare profile data
      const profileData = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        gender: formData.gender as 'Male' | 'Female' | 'Other' | 'Prefer not to say',
        dateOfBirth: formData.dateOfBirth,
        cityName: formData.cityName,
        latitude: parseFloat(formData.selectedCity?.lat || '0'),
        longitude: parseFloat(formData.selectedCity?.lon || '0'),
        weatherPreferences: {},
      };

      // Register user
      const authResult = await PersistentAuthService.register(
        { email: formData.email, password: formData.password },
        profileData
      );

      if (!authResult.success) {
        setErrors({ general: authResult.error });
        return;
      }

      // Update onboarding profile data
      if (authResult.user) {
        updateUserProfile(formData);
      }

      onboardingLogger.info('Account creation successful');
      
      // Complete onboarding
      handleTransition(() => {
        completeOnboarding();
        onComplete();
      });

    } catch (error) {
      onboardingLogger.error('Account creation failed', {
        error: error instanceof Error ? error.message : 'Unknown error'
      }, error instanceof Error ? error : undefined);
      
      setErrors({ 
        general: 'Account creation failed. Please try again.' 
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handlePrevious = () => {
    setErrors({}); // Clear errors when going back
    handleTransition(previousStep, 'backward');
  };

  const handleSkip = () => {
    handleTransition(() => {
      completeOnboarding();
      onComplete();
    });
  };

  // Welcome screen (step 0)
  if (isFirstStep) {
    return (
      <OnboardingLayout 
        className={cn(
          "transition-all duration-500 ease-liquid",
          isTransitioning && direction === 'forward' && "opacity-0 translate-x-full",
          isTransitioning && direction === 'backward' && "opacity-0 -translate-x-full"
        )}
      >
        <WelcomeScreen 
          onContinue={() => handleTransition(nextStep, 'forward')}
          onSkip={handleSkip}
        />
      </OnboardingLayout>
    );
  }

  // Render current step
  const renderCurrentStep = () => {
    if (!currentStepData) return null;

    const stepProps = {
      currentStep: currentStep + 1,
      totalSteps,
      onPrevious: handlePrevious,
      errors
    };

    switch (currentStepData.id) {
      case 'personal-info':
        return (
          <PersonalInfoStep
            {...stepProps}
            data={{
              firstName: formData.firstName,
              lastName: formData.lastName,
              gender: formData.gender
            }}
            onDataChange={(data) => setFormData(prev => ({ ...prev, ...data }))}
            onNext={handlePersonalInfoNext}
            canGoPrevious={canGoPrevious}
          />
        );

      case 'date-of-birth':
        return (
          <DateOfBirthStep
            {...stepProps}
            data={{ dateOfBirth: formData.dateOfBirth }}
            onDataChange={(data) => setFormData(prev => ({ ...prev, ...data }))}
            onNext={handleDateOfBirthNext}
          />
        );

      case 'location':
        return (
          <LocationStep
            {...stepProps}
            data={{
              cityName: formData.cityName,
              selectedCity: formData.selectedCity
            }}
            onDataChange={(data) => setFormData(prev => ({ ...prev, ...data }))}
            onNext={handleLocationNext}
          />
        );

      case 'account-creation':
        return (
          <AccountCreationStep
            {...stepProps}
            data={{
              email: formData.email,
              password: formData.password,
              confirmPassword: formData.confirmPassword
            }}
            onDataChange={(data) => setFormData(prev => ({ ...prev, ...data }))}
            onComplete={handleAccountCreationComplete}
            isLoading={isLoading}
          />
        );

      default:
        return null;
    }
  };

  return (
    <div className={cn(
      "transition-all duration-500 ease-liquid",
      isTransitioning && direction === 'forward' && "opacity-0 translate-x-full",
      isTransitioning && direction === 'backward' && "opacity-0 -translate-x-full"
    )}>
      {renderCurrentStep()}
    </div>
  );
};
