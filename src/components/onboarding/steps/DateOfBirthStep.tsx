import { Calendar } from 'lucide-react';
import { GlassCard } from '@/components/GlassCard';
import { GlassButton } from '@/components/ui/glass-button';
import { GlassInput } from '@/components/ui/glass-input';
import { cn } from '@/lib/utils';
import { UserProfileFormErrors } from '@/types/user';

interface DateOfBirthData {
  dateOfBirth: string;
}

interface DateOfBirthStepProps {
  data: DateOfBirthData;
  errors: UserProfileFormErrors;
  onDataChange: (data: DateOfBirthData) => void;
  onNext: () => void;
  onPrevious: () => void;
  currentStep: number;
  totalSteps: number;
}

export const DateOfBirthStep = ({
  data,
  errors,
  onDataChange,
  onNext,
  onPrevious,
  currentStep,
  totalSteps
}: DateOfBirthStepProps) => {

  const handleDateChange = (value: string) => {
    onDataChange({
      dateOfBirth: value
    });
  };

  const validateAndNext = () => {
    // Basic validation will be handled by parent component
    onNext();
  };

  // Calculate max date (18 years ago)
  const maxDate = new Date();
  maxDate.setFullYear(maxDate.getFullYear() - 13); // Minimum age 13
  const maxDateString = maxDate.toISOString().split('T')[0];

  // Calculate min date (100 years ago)
  const minDate = new Date();
  minDate.setFullYear(minDate.getFullYear() - 100);
  const minDateString = minDate.toISOString().split('T')[0];

  return (
    <div className="min-h-screen flex items-center justify-center p-6 bg-gradient-to-br from-zara-white via-zara-light-gray/30 to-zara-white">
      <div className="w-full max-w-md">
        {/* Progress indicator */}
        <div className="text-center mb-8">
          <p className="zara-caption text-zara-dark-gray mb-4">
            Step {currentStep} of {totalSteps}
          </p>
          <div className="w-full bg-zara-light-gray/40 rounded-full h-2">
            <div 
              className="bg-gradient-to-r from-zara-charcoal to-zara-dark-gray h-2 rounded-full transition-all duration-500"
              style={{ width: `${(currentStep / totalSteps) * 100}%` }}
            />
          </div>
        </div>

        <GlassCard className="p-8 space-y-6">
          {/* Header */}
          <div className="text-center space-y-3">
            <div className="w-16 h-16 mx-auto glass-subtle rounded-full flex items-center justify-center">
              <Calendar size={24} className="text-zara-charcoal" />
            </div>
            <h1 className="zara-title">Date of Birth</h1>
            <p className="zara-body text-zara-dark-gray">
              When were you born? This helps us personalize your experience
            </p>
          </div>

          {/* Form field */}
          <div className="space-y-4">
            <div>
              <label className="block zara-body text-zara-dark-gray mb-2">
                Date of Birth *
              </label>
              <GlassInput
                type="date"
                value={data.dateOfBirth}
                onChange={(e) => handleDateChange(e.target.value)}
                min={minDateString}
                max={maxDateString}
                className={cn(
                  errors.dateOfBirth ? 'border-red-300' : '',
                  "text-zara-charcoal"
                )}
              />
              {errors.dateOfBirth && (
                <p className="text-red-500 zara-caption mt-1">{errors.dateOfBirth}</p>
              )}
              <p className="zara-caption text-zara-dark-gray mt-2">
                You must be at least 13 years old to use this service
              </p>
            </div>
          </div>

          {/* Navigation buttons */}
          <div className="flex gap-3 pt-4">
            <GlassButton
              variant="secondary"
              onClick={onPrevious}
              className="flex-1"
            >
              Previous
            </GlassButton>
            <GlassButton
              variant="primary"
              onClick={validateAndNext}
              className="flex-1"
            >
              Next
            </GlassButton>
          </div>
        </GlassCard>
      </div>
    </div>
  );
};
