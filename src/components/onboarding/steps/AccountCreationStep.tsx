import { useState } from 'react';
import { Mail, Lock, Eye, EyeOff, Shield, Loader2 } from 'lucide-react';
import { GlassCard } from '@/components/GlassCard';
import { GlassButton } from '@/components/ui/glass-button';
import { GlassInput } from '@/components/ui/glass-input';
import { cn } from '@/lib/utils';
import { UserProfileFormErrors } from '@/types/user';
import PersistentAuthService from '@/services/persistentAuth';
import { authApi, BackendApiError } from '@/services/backendApi';

interface AccountCreationData {
  email: string;
  password: string;
  confirmPassword: string;
}

interface AccountCreationStepProps {
  data: AccountCreationData;
  errors: UserProfileFormErrors;
  onDataChange: (data: AccountCreationData) => void;
  onComplete: () => void;
  onPrevious: () => void;
  currentStep: number;
  totalSteps: number;
  isLoading?: boolean;
}

interface EmailValidationState {
  isValidating: boolean;
  isEmailTaken: boolean;
  validationError: string | null;
  hasValidated: boolean;
  canRetry: boolean;
}

export const AccountCreationStep = ({
  data,
  errors,
  onDataChange,
  onComplete,
  onPrevious,
  currentStep,
  totalSteps,
  isLoading = false
}: AccountCreationStepProps) => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState<{
    score: number;
    feedback: string[];
  }>({ score: 0, feedback: [] });
  const [emailValidation, setEmailValidation] = useState<EmailValidationState>({
    isValidating: false,
    isEmailTaken: false,
    validationError: null,
    hasValidated: false,
    canRetry: false
  });

  // Email validation functions
  const resetEmailValidation = () => {
    setEmailValidation({
      isValidating: false,
      isEmailTaken: false,
      validationError: null,
      hasValidated: false,
      canRetry: false
    });
  };

  const validateEmailAvailability = async (email: string) => {
    // Don't validate if email format is invalid
    if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      return;
    }

    setEmailValidation(prev => ({
      ...prev,
      isValidating: true,
      validationError: null,
      canRetry: false
    }));

    try {
      const result = await authApi.validateEmail(email);

      setEmailValidation(prev => ({
        ...prev,
        isValidating: false,
        isEmailTaken: !result.available,
        validationError: !result.available ? result.message : null,
        hasValidated: true,
        canRetry: false
      }));
    } catch (error) {
      let errorMessage = 'Unable to verify email. Please try again.';
      let canRetry = true;

      if (error instanceof BackendApiError) {
        errorMessage = error.message;
        canRetry = error.code === 'VALIDATION_TIMEOUT' ||
                  error.code === 'NETWORK_ERROR' ||
                  error.status === 0 ||
                  (error.status && error.status >= 500);
      }

      setEmailValidation(prev => ({
        ...prev,
        isValidating: false,
        isEmailTaken: false, // Don't prevent signup on error
        validationError: errorMessage,
        hasValidated: true,
        canRetry: canRetry
      }));
    }
  };

  const handleEmailBlur = () => {
    // Only trigger validation when user stops typing and field loses focus
    if (data.email && data.email.trim()) {
      // Add a small delay to prevent rapid successive validations
      setTimeout(() => {
        // Check if email hasn't changed since blur event
        if (data.email && data.email.trim()) {
          validateEmailAvailability(data.email.trim());
        }
      }, 100);
    }
  };

  const handleInputChange = (field: keyof AccountCreationData, value: string) => {
    const newData = {
      ...data,
      [field]: value
    };

    onDataChange(newData);

    // Reset email validation when email field changes
    if (field === 'email') {
      resetEmailValidation();
    }

    // Update password strength when password changes
    if (field === 'password') {
      const validation = PersistentAuthService.validatePassword(value);
      setPasswordStrength({
        score: validation.isValid ? 4 : Math.min(3, value.length / 3),
        feedback: validation.errors
      });
    }
  };

  const getPasswordStrengthColor = (score: number) => {
    if (score < 2) return 'bg-red-500';
    if (score < 3) return 'bg-yellow-500';
    if (score < 4) return 'bg-blue-500';
    return 'bg-green-500';
  };

  const getPasswordStrengthText = (score: number) => {
    if (score < 2) return 'Weak';
    if (score < 3) return 'Fair';
    if (score < 4) return 'Good';
    return 'Strong';
  };

  const validateAndComplete = () => {
    // Basic validation will be handled by parent component
    onComplete();
  };

  return (
    <div className="h-screen overflow-y-auto p-6 bg-gradient-to-br from-zara-white via-zara-light-gray/30 to-zara-white">
      <div className="w-full max-w-md mx-auto my-10">
        {/* Progress indicator */}
        <div className="text-center mb-8">
          <p className="zara-caption text-zara-dark-gray mb-4">
            Step {currentStep} of {totalSteps}
          </p>
          <div className="w-full bg-zara-light-gray/40 rounded-full h-2">
            <div 
              className="bg-gradient-to-r from-zara-charcoal to-zara-dark-gray h-2 rounded-full transition-all duration-500"
              style={{ width: `${(currentStep / totalSteps) * 100}%` }}
            />
          </div>
        </div>

        <GlassCard className="p-8 space-y-6">
          {/* Header */}
          <div className="text-center space-y-3">
            <div className="w-16 h-16 mx-auto glass-subtle rounded-full flex items-center justify-center">
              <Shield size={24} className="text-zara-charcoal" />
            </div>
            <h1 className="zara-title">Create Your Account</h1>
            <p className="zara-body text-zara-dark-gray">
              Set up your account to save your preferences and access all features
            </p>
          </div>

          {/* Form fields */}
          <div className="space-y-4">
            {/* Email */}
            <div>
              <label className="block zara-body text-zara-dark-gray mb-2">
                Email Address *
              </label>
              <div className="relative">
                <GlassInput
                  type="email"
                  value={data.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  onBlur={handleEmailBlur}
                  onFocus={() => {
                    // Allow retry by re-focusing email field
                    if (emailValidation.canRetry && emailValidation.validationError) {
                      setEmailValidation(prev => ({
                        ...prev,
                        validationError: null,
                        canRetry: false
                      }));
                    }
                  }}
                  placeholder="Enter your email address"
                  className={cn(
                    errors.email ? 'border-red-300' : '',
                    emailValidation.isEmailTaken ? 'border-red-300' : '',
                    "pl-12 pr-11"
                  )}
                />
                <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                  <Mail size={20} className="text-zara-dark-gray" />
                </div>
                {/* Email validation loading indicator */}
                {emailValidation.isValidating && (
                  <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-zara-dark-gray animate-spin" />
                )}
              </div>

              {/* Email validation feedback */}
              {errors.email && (
                <p className="text-red-500 zara-caption mt-1">{errors.email}</p>
              )}
              {emailValidation.isEmailTaken && (
                <p className="text-red-500 zara-caption mt-1">
                  This email is already registered. <button
                    type="button"
                    className="text-zara-charcoal hover:underline"
                    onClick={() => {/* Could trigger login flow */}}
                  >
                    Login instead
                  </button>
                </p>
              )}
              {emailValidation.validationError && !emailValidation.isEmailTaken && (
                <p className="text-amber-600 zara-caption mt-1">
                  {emailValidation.validationError}
                  {emailValidation.canRetry && (
                    <button
                      type="button"
                      className="ml-2 text-zara-charcoal hover:underline"
                      onClick={() => validateEmailAvailability(data.email.trim())}
                    >
                      Retry
                    </button>
                  )}
                </p>
              )}
            </div>

            {/* Password */}
            <div>
              <label className="block zara-body text-zara-dark-gray mb-2">
                Password *
              </label>
              <div className="relative">
                <GlassInput
                  type={showPassword ? 'text' : 'password'}
                  value={data.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  placeholder="Create a strong password"
                  className={cn(
                    errors.password ? 'border-red-300' : '',
                    "pl-12 pr-12"
                  )}
                />
                <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                  <Lock size={20} className="text-zara-dark-gray" />
                </div>
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 text-zara-dark-gray hover:text-zara-charcoal"
                >
                  {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                </button>
              </div>
              
              {/* Password strength indicator */}
              {data.password && (
                <div className="mt-2">
                  <div className="flex items-center space-x-2 mb-1">
                    <div className="flex-1 bg-zara-light-gray/40 rounded-full h-1">
                      <div 
                        className={cn(
                          "h-1 rounded-full transition-all duration-300",
                          getPasswordStrengthColor(passwordStrength.score)
                        )}
                        style={{ width: `${(passwordStrength.score / 4) * 100}%` }}
                      />
                    </div>
                    <span className="zara-caption text-zara-dark-gray">
                      {getPasswordStrengthText(passwordStrength.score)}
                    </span>
                  </div>
                  {passwordStrength.feedback.length > 0 && (
                    <div className="space-y-1">
                      {passwordStrength.feedback.map((feedback, index) => (
                        <p key={index} className="text-red-500 zara-caption">
                          • {feedback}
                        </p>
                      ))}
                    </div>
                  )}
                </div>
              )}
              
              {errors.password && (
                <p className="text-red-500 zara-caption mt-1">{errors.password}</p>
              )}
            </div>

            {/* Confirm Password */}
            <div>
              <label className="block zara-body text-zara-dark-gray mb-2">
                Confirm Password *
              </label>
              <div className="relative">
                <GlassInput
                  type={showConfirmPassword ? 'text' : 'password'}
                  value={data.confirmPassword}
                  onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                  placeholder="Confirm your password"
                  className={cn(
                    errors.confirmPassword ? 'border-red-300' : '',
                    "pl-12 pr-12"
                  )}
                />
                <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                  <Lock size={20} className="text-zara-dark-gray" />
                </div>
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 text-zara-dark-gray hover:text-zara-charcoal"
                >
                  {showConfirmPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                </button>
              </div>
              {errors.confirmPassword && (
                <p className="text-red-500 zara-caption mt-1">{errors.confirmPassword}</p>
              )}
            </div>
          </div>

          {/* Security note */}
          <div className="glass-subtle p-4 rounded-xl">
            <div className="flex items-start space-x-3">
              <Shield size={16} className="text-zara-dark-gray mt-0.5 flex-shrink-0" />
              <div>
                <p className="zara-caption text-zara-dark-gray">
                  Your password is encrypted and stored securely. We never share your personal information with third parties.
                </p>
              </div>
            </div>
          </div>

          {/* Navigation buttons */}
          <div className="flex gap-3 pt-4">
            <GlassButton
              variant="secondary"
              onClick={onPrevious}
              className="flex-1"
              disabled={isLoading}
            >
              Previous
            </GlassButton>
            <GlassButton
              variant="primary"
              onClick={validateAndComplete}
              className="flex-1"
              disabled={isLoading || emailValidation.isEmailTaken}
            >
              {isLoading ? 'Creating Account...' : 'Complete'}
            </GlassButton>
          </div>
        </GlassCard>
      </div>
    </div>
  );
};
