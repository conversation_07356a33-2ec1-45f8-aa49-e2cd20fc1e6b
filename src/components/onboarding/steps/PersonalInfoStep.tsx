import { useState } from 'react';
import { User, ChevronDown } from 'lucide-react';
import { GlassCard } from '@/components/GlassCard';
import { GlassButton } from '@/components/ui/glass-button';
import { GlassInput } from '@/components/ui/glass-input';
import { cn } from '@/lib/utils';
import { UserProfileFormErrors } from '@/types/user';

interface PersonalInfoData {
  firstName: string;
  lastName: string;
  gender: string;
}

interface PersonalInfoStepProps {
  data: PersonalInfoData;
  errors: UserProfileFormErrors;
  onDataChange: (data: PersonalInfoData) => void;
  onNext: () => void;
  onPrevious?: () => void;
  canGoPrevious?: boolean;
  currentStep: number;
  totalSteps: number;
}

export const PersonalInfoStep = ({
  data,
  errors,
  onDataChange,
  onNext,
  onPrevious,
  canGoPrevious = false,
  currentStep,
  totalSteps
}: PersonalInfoStepProps) => {
  const [showGenderDropdown, setShowGenderDropdown] = useState(false);

  const genderOptions = [
    { value: 'Male', label: 'Male' },
    { value: 'Female', label: 'Female' },
    { value: 'Other', label: 'Other' },
    { value: 'Prefer not to say', label: 'Prefer not to say' },
  ];

  const handleInputChange = (field: keyof PersonalInfoData, value: string) => {
    onDataChange({
      ...data,
      [field]: value
    });
  };

  const handleGenderSelect = (gender: string) => {
    handleInputChange('gender', gender);
    setShowGenderDropdown(false);
  };

  const validateAndNext = () => {
    // Basic validation will be handled by parent component
    onNext();
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-6 bg-gradient-to-br from-zara-white via-zara-light-gray/30 to-zara-white">
      <div className="w-full max-w-md">
        {/* Progress indicator */}
        <div className="text-center mb-8">
          <p className="zara-caption text-zara-dark-gray mb-4">
            Step {currentStep} of {totalSteps}
          </p>
          <div className="w-full bg-zara-light-gray/40 rounded-full h-2">
            <div 
              className="bg-gradient-to-r from-zara-charcoal to-zara-dark-gray h-2 rounded-full transition-all duration-500"
              style={{ width: `${(currentStep / totalSteps) * 100}%` }}
            />
          </div>
        </div>

        <GlassCard className="p-8 space-y-6">
          {/* Header */}
          <div className="text-center space-y-3">
            <div className="w-16 h-16 mx-auto glass-subtle rounded-full flex items-center justify-center">
              <User size={24} className="text-zara-charcoal" />
            </div>
            <h1 className="zara-title">Personal Information</h1>
            <p className="zara-body text-zara-dark-gray">
              Let's start with some basic information about you
            </p>
          </div>

          {/* Form fields */}
          <div className="space-y-4">
            {/* First Name */}
            <div>
              <label className="block zara-body text-zara-dark-gray mb-2">
                First Name *
              </label>
              <GlassInput
                value={data.firstName}
                onChange={(e) => handleInputChange('firstName', e.target.value)}
                placeholder="Enter your first name"
                className={cn(errors.firstName ? 'border-red-300' : '')}
              />
              {errors.firstName && (
                <p className="text-red-500 zara-caption mt-1">{errors.firstName}</p>
              )}
            </div>

            {/* Last Name */}
            <div>
              <label className="block zara-body text-zara-dark-gray mb-2">
                Last Name *
              </label>
              <GlassInput
                value={data.lastName}
                onChange={(e) => handleInputChange('lastName', e.target.value)}
                placeholder="Enter your last name"
                className={cn(errors.lastName ? 'border-red-300' : '')}
              />
              {errors.lastName && (
                <p className="text-red-500 zara-caption mt-1">{errors.lastName}</p>
              )}
            </div>

            {/* Gender */}
            <div>
              <label className="block zara-body text-zara-dark-gray mb-2">
                Gender *
              </label>
              <div className="relative">
                <button
                  type="button"
                  onClick={() => setShowGenderDropdown(!showGenderDropdown)}
                  className={cn(
                    "w-full px-4 py-3 glass-subtle rounded-xl zara-body text-left flex items-center justify-between",
                    "hover:glass-light transition-all duration-200",
                    "focus:outline-none focus:ring-2 focus:ring-zara-charcoal focus:ring-offset-2",
                    errors.gender ? 'border border-red-300' : ''
                  )}
                >
                  <span className={data.gender ? 'text-zara-charcoal' : 'text-zara-dark-gray'}>
                    {data.gender || 'Select your gender'}
                  </span>
                  <ChevronDown 
                    size={20} 
                    className={cn(
                      "text-zara-dark-gray transition-transform duration-200",
                      showGenderDropdown && "rotate-180"
                    )} 
                  />
                </button>

                {/* Gender dropdown */}
                {showGenderDropdown && (
                  <div className="absolute top-full left-0 right-0 z-50 mt-1">
                    <GlassCard className="max-h-48 overflow-y-auto">
                      {genderOptions.map((option) => (
                        <button
                          key={option.value}
                          onClick={() => handleGenderSelect(option.value)}
                          className="w-full p-3 text-left hover:glass-light transition-all duration-200 zara-body"
                        >
                          {option.label}
                        </button>
                      ))}
                    </GlassCard>
                  </div>
                )}
              </div>
              {errors.gender && (
                <p className="text-red-500 zara-caption mt-1">{errors.gender}</p>
              )}
            </div>
          </div>

          {/* Navigation buttons */}
          <div className="flex gap-3 pt-4">
            {canGoPrevious && onPrevious && (
              <GlassButton
                variant="secondary"
                onClick={onPrevious}
                className="flex-1"
              >
                Previous
              </GlassButton>
            )}
            <GlassButton
              variant="primary"
              onClick={validateAndNext}
              className={canGoPrevious ? "flex-1" : "w-full"}
            >
              Next
            </GlassButton>
          </div>
        </GlassCard>
      </div>
    </div>
  );
};
