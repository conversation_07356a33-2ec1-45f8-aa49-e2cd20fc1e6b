import { useOnboarding } from '@/hooks/useOnboarding';
import { MultiStepOnboardingFlow } from './MultiStepOnboardingFlow';

interface OnboardingRouterProps {
  onComplete: () => void;
}

/**
 * OnboardingRouter component handles the navigation flow for the onboarding experience.
 * It manages the routing between different onboarding screens and handles completion.
 */
export const OnboardingRouter = ({ onComplete }: OnboardingRouterProps) => {
  const { isCompleted } = useOnboarding();

  // If onboarding is already completed, don't render anything
  if (isCompleted) {
    return null;
  }

  return (
    <div className="fixed inset-0 z-50 bg-white">
      <MultiStepOnboardingFlow onComplete={onComplete} />
    </div>
  );
};