import { ReactNode, useEffect, useState } from 'react';
import { cn } from '@/lib/utils';

interface OnboardingLayoutProps {
  children: ReactNode;
  showSkipButton?: boolean;
  onSkip?: () => void;
  className?: string;
}

export const OnboardingLayout = ({ 
  children, 
  showSkipButton = false, 
  onSkip,
  className 
}: OnboardingLayoutProps) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 50);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className={cn(
      "min-h-screen relative overflow-hidden",
      "transition-all duration-1000 ease-liquid",
      isVisible ? "opacity-100" : "opacity-0",
      className
    )}>
      {/* Skip button overlay */}
      {showSkipButton && onSkip && (
        <div className="absolute top-6 right-6 z-50">
          <button
            onClick={onSkip}
            className={cn(
              "glass-subtle rounded-full px-4 py-2",
              "zara-caption text-zara-dark-gray hover:text-zara-charcoal",
              "transition-all duration-300 ease-glass",
              "hover:glass-light hover:scale-105",
              "focus:outline-none focus:ring-2 focus:ring-zara-charcoal focus:ring-offset-2",
              "transition-all duration-500 ease-premium",
              isVisible ? "opacity-100 translate-y-0" : "opacity-0 -translate-y-4"
            )}
            style={{ animationDelay: '800ms' }}
          >
            Skip
          </button>
        </div>
      )}

      {/* Main content with smooth transitions */}
      <div className={cn(
        "transition-all duration-800 ease-liquid",
        isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-4"
      )}>
        {children}
      </div>

      {/* Ambient background elements */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Subtle gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-transparent via-zara-white/10 to-zara-light-gray/20" />
        
        {/* Floating glass orbs */}
        <div className="absolute top-1/4 left-1/4 w-32 h-32 glass-subtle rounded-full opacity-10 animate-pulse" 
             style={{ animationDuration: '4s', animationDelay: '0s' }} />
        <div className="absolute bottom-1/3 right-1/4 w-24 h-24 glass-subtle rounded-full opacity-8 animate-pulse" 
             style={{ animationDuration: '6s', animationDelay: '2s' }} />
        <div className="absolute top-1/2 right-1/3 w-16 h-16 glass-subtle rounded-full opacity-6 animate-pulse" 
             style={{ animationDuration: '5s', animationDelay: '1s' }} />
      </div>
    </div>
  );
};