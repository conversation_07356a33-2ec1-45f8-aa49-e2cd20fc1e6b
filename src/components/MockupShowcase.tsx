import React, { useState, useEffect } from "react";
import { ChevronLeft, ChevronRight, Sun, Calendar, Shirt, Home, Grid3X3 } from "lucide-react";

const MockupShowcase = () => {
  const [currentSlide, setCurrentSlide] = useState(0);

  const mockups = [
    {
      title: "Home Dashboard",
      description: "Weather-aware outfit recommendations at a glance",
      icon: Home,
      features: ["Weather Integration", "Daily Recommendations", "Quick Access"],
      mockupType: "dashboard"
    },
    {
      title: "Smart Closet",
      description: "Organize and browse your wardrobe effortlessly",
      icon: Shirt,
      features: ["Visual Organization", "Smart Categories", "Quick Search"],
      mockupType: "closet"
    },
    {
      title: "Schedule Planner",
      description: "Plan your outfits for upcoming events and occasions",
      icon: Calendar,
      features: ["Event Planning", "Outfit Scheduling", "Smart Suggestions"],
      mockupType: "schedule"
    },
    {
      title: "Carousel View",
      description: "Swipe through your favorite outfits and collections",
      icon: Grid3X3,
      features: ["Swipe Navigation", "Visual Discovery", "Collection Browse"],
      mockupType: "carousel"
    }
  ];

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % mockups.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + mockups.length) % mockups.length);
  };

  return (
    <section className="py-24 relative">
      <div className="container mx-auto px-6">
        {/* Header Section */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-light text-white mb-6 text-shadow">
            Experience ClosetMate
          </h2>
          <p className="text-xl text-white/80 max-w-2xl mx-auto font-light">
            Discover how our intuitive interface transforms your daily styling routine
          </p>
        </div>

        {/* Simple Mockup Container */}
        <div className="max-w-4xl mx-auto">
          {/* Mockup Display */}
          <div className="glass-panel glass-refraction p-8 rounded-3xl mb-8">
            <div className="h-96 md:h-[500px] relative">
              {mockups.map((mockup, index) => (
                <div
                  key={index}
                  className={`absolute inset-0 transition-opacity duration-700 ${
                    index === currentSlide ? 'opacity-100' : 'opacity-0'
                  }`}
                >
                  <div className="h-full">
                    {mockup.mockupType === 'dashboard' && <DashboardMockup />}
                    {mockup.mockupType === 'closet' && <ClosetMockup />}
                    {mockup.mockupType === 'schedule' && <ScheduleMockup />}
                    {mockup.mockupType === 'carousel' && <CarouselMockup />}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Simple Info Section */}
          <div className="glass-panel p-6 rounded-2xl mb-8">
            <div className="flex items-center gap-4 mb-4">
              <div className="p-3 glass-subtle rounded-xl">
                <MockupIcon icon={mockups[currentSlide].icon} />
              </div>
              <div>
                <h3 className="text-xl font-medium text-white mb-1">
                  {mockups[currentSlide].title}
                </h3>
                <p className="text-white/70 text-sm">
                  {mockups[currentSlide].description}
                </p>
              </div>
            </div>
            <div className="flex flex-wrap gap-2">
              {mockups[currentSlide].features.map((feature, idx) => (
                <span
                  key={idx}
                  className="px-3 py-1 bg-white/10 rounded-full text-xs text-white/80"
                >
                  {feature}
                </span>
              ))}
            </div>
          </div>

          {/* Simple Navigation */}
          <div className="flex justify-center items-center gap-4">
            <button
              onClick={prevSlide}
              className="p-3 glass-panel border-white/20 text-white hover:bg-white/10 rounded-xl"
            >
              <ChevronLeft className="h-4 w-4" />
            </button>

            {/* Dots Indicator */}
            <div className="flex space-x-2">
              {mockups.map((_, index) => (
                <button
                  key={index}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentSlide
                      ? 'bg-white'
                      : 'bg-white/30 hover:bg-white/50'
                  }`}
                  onClick={() => setCurrentSlide(index)}
                />
              ))}
            </div>

            <button
              onClick={nextSlide}
              className="p-3 glass-panel border-white/20 text-white hover:bg-white/10 rounded-xl"
            >
              <ChevronRight className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

// Mockup Components
const DashboardMockup = () => (
  <div className="h-full flex flex-col">
    {/* Header */}
    <div className="flex items-center justify-between mb-6">
      <div className="glass-subtle p-3 rounded-xl">
        <div className="flex items-center gap-2">
          <Sun className="w-4 h-4 text-white/70" />
          <span className="text-white text-sm">22°C Sunny</span>
        </div>
      </div>
      <div className="glass-subtle p-2 rounded-full">
        <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
          <div className="w-4 h-4 bg-white/40 rounded-full"></div>
        </div>
      </div>
    </div>

    {/* Today's Recommendation */}
    <div className="glass-light p-4 rounded-xl mb-4 flex-1">
      <h4 className="text-white font-medium mb-3">Today's Recommendation</h4>
      <div className="grid grid-cols-3 gap-3">
        <div className="glass-subtle p-3 rounded-lg text-center">
          <div className="w-full h-16 rounded-lg mb-2 overflow-hidden bg-white/5 flex items-center justify-center">
            <img
              src="/images/stock/white-shirt.jpg"
              alt="Light Shirt"
              className="w-12 h-12 object-cover rounded"
            />
          </div>
          <span className="text-white/70 text-xs">Light Shirt</span>
        </div>
        <div className="glass-subtle p-3 rounded-lg text-center">
          <div className="w-full h-16 rounded-lg mb-2 overflow-hidden bg-white/5 flex items-center justify-center">
            <img
              src="/images/stock/blue-jeans.jpg"
              alt="Chinos"
              className="w-12 h-12 object-cover rounded"
            />
          </div>
          <span className="text-white/70 text-xs">Chinos</span>
        </div>
        <div className="glass-subtle p-3 rounded-lg text-center">
          <div className="w-full h-16 rounded-lg mb-2 overflow-hidden bg-white/5 flex items-center justify-center">
            <img
              src="/images/stock/white-sneakers.jpg"
              alt="Sneakers"
              className="w-12 h-12 object-cover rounded"
            />
          </div>
          <span className="text-white/70 text-xs">Sneakers</span>
        </div>
      </div>
    </div>

    {/* Quick Stats */}
    <div className="grid grid-cols-2 gap-3">
      <div className="glass-subtle p-3 rounded-lg text-center">
        <div className="text-white text-lg font-light">127</div>
        <div className="text-white/60 text-xs">Items</div>
      </div>
      <div className="glass-subtle p-3 rounded-lg text-center">
        <div className="text-white text-lg font-light">8</div>
        <div className="text-white/60 text-xs">Outfits</div>
      </div>
    </div>
  </div>
);

const ClosetMockup = () => {
  const clothingItems = [
    { name: 'White Shirt', image: '/images/stock/white-shirt.jpg' },
    { name: 'Blue Jeans', image: '/images/stock/blue-jeans.jpg' },
    { name: 'Black Jacket', image: '/images/stock/black-jacket.jpg' },
    { name: 'Pink Dress', image: '/images/stock/summer-dress.jpg' },
    { name: 'Sneakers', image: '/images/stock/white-sneakers.jpg' },
    { name: 'Light Shirt', image: '/images/stock/striped-shirt.jpg' },
    { name: 'Dark Jeans', image: '/images/stock/blue-jeans.jpg' },
    { name: 'Casual Jacket', image: '/images/stock/denim-jacket.jpg' },
    { name: 'Summer Dress', image: '/images/stock/elegant-dress.jpg' }
  ];

  return (
    <div className="h-full flex flex-col">
      {/* Search Bar */}
      <div className="glass-light p-3 rounded-xl mb-4">
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-white/30 rounded-full flex items-center justify-center">
            <div className="w-2 h-2 bg-white/60 rounded-full"></div>
          </div>
          <span className="text-white/60 text-sm">Search your closet...</span>
        </div>
      </div>

      {/* Categories */}
      <div className="flex gap-2 mb-4">
        {['All', 'Tops', 'Bottoms', 'Shoes'].map((category, idx) => (
          <div
            key={category}
            className={`px-3 py-1 rounded-full text-xs ${idx === 0 ? 'glass-medium text-white' : 'glass-subtle text-white/70'
              }`}
          >
            {category}
          </div>
        ))}
      </div>

      {/* Closet Grid */}
      <div className="grid grid-cols-3 gap-3 flex-1">
        {clothingItems.slice(0, 9).map((item, idx) => (
          <div key={idx} className="glass-subtle rounded-lg p-2 hover:glass-light transition-all duration-200">
            <div className="w-full h-20 rounded-lg mb-2 overflow-hidden bg-white/5 flex items-center justify-center">
              <img
                src={item.image}
                alt={item.name}
                className="w-16 h-16 object-cover rounded"
              />
            </div>
            <div className="text-white/60 text-xs text-center truncate">
              {item.name}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const ScheduleMockup = () => {
  const weekSchedule = [
    {
      day: 'Mon',
      event: 'Work Meeting',
      outfit: 'Business Casual',
      items: ['/images/stock/white-shirt.jpg', '/images/stock/blue-jeans.jpg', '/images/stock/black-jacket.jpg']
    },
    {
      day: 'Tue',
      event: 'Lunch Date',
      outfit: 'Smart Casual',
      items: ['/images/stock/elegant-dress.jpg', '/images/stock/denim-jacket.jpg', '/images/stock/white-sneakers.jpg']
    },
    {
      day: 'Wed',
      event: 'Gym Session',
      outfit: 'Activewear',
      items: ['/images/stock/striped-shirt.jpg', '/images/stock/blue-jeans.jpg', '/images/stock/white-sneakers.jpg']
    },
    {
      day: 'Thu',
      event: 'Dinner Out',
      outfit: 'Evening Wear',
      items: ['/images/stock/elegant-dress.jpg', '/images/stock/black-jacket.jpg', '/images/stock/brown-boots.jpg']
    },
    {
      day: 'Fri',
      event: 'Casual Friday',
      outfit: 'Relaxed',
      items: ['/images/stock/striped-shirt.jpg', '/images/stock/blue-jeans.jpg', '/images/stock/white-sneakers.jpg']
    }
  ];

  return (
    <div className="h-full flex flex-col">
      {/* Calendar Header */}
      <div className="flex items-center justify-between mb-4">
        <h4 className="text-white font-medium">This Week</h4>
        <div className="glass-subtle p-2 rounded-lg">
          <Calendar className="w-4 h-4 text-white/70" />
        </div>
      </div>

      {/* Week View */}
      <div className="space-y-3 flex-1">
        {weekSchedule.slice(0, 5).map((item, idx) => (
          <div key={idx} className="glass-light p-3 rounded-xl hover:glass-medium transition-all duration-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="glass-subtle p-2 rounded-lg min-w-[2.5rem] text-center">
                  <span className="text-white text-xs font-medium">{item.day}</span>
                </div>
                <div>
                  <div className="text-white text-sm font-medium">{item.event}</div>
                  <div className="text-white/60 text-xs">{item.outfit}</div>
                </div>
              </div>
              <div className="flex gap-1">
                {item.items.map((itemImg, i) => (
                  <div
                    key={i}
                    className="w-6 h-6 rounded-full bg-white/10 flex items-center justify-center overflow-hidden"
                  >
                    <img
                      src={itemImg}
                      alt={`Item ${i + 1}`}
                      className="w-4 h-4 object-cover rounded"
                    />
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const CarouselMockup = () => {
  const collections = [
    {
      name: 'Summer Essentials',
      description: 'Perfect for warm weather',
      items: [
        { name: 'Linen Shirt', image: '/images/stock/white-shirt.jpg' },
        { name: 'Light Dress', image: '/images/stock/summer-dress.jpg' },
        { name: 'Casual Sneakers', image: '/images/stock/white-sneakers.jpg' },
        { name: 'Summer Jacket', image: '/images/stock/denim-jacket.jpg' }
      ]
    }
  ];

  return (
    <div className="h-full flex flex-col">
      {/* Carousel Header */}
      <div className="flex items-center justify-between mb-4">
        <h4 className="text-white font-medium">Your Collections</h4>
        <div className="flex gap-1">
          <div className="w-2 h-2 bg-white rounded-full"></div>
          <div className="w-2 h-2 bg-white/40 rounded-full"></div>
          <div className="w-2 h-2 bg-white/40 rounded-full"></div>
        </div>
      </div>

      {/* Main Carousel Item */}
      <div className="glass-light p-4 rounded-xl mb-4 flex-1">
        <div className="text-center mb-4">
          <h5 className="text-white font-medium mb-1">{collections[0].name}</h5>
          <p className="text-white/60 text-sm">{collections[0].description}</p>
        </div>

        <div className="grid grid-cols-2 gap-3 mb-4">
          {collections[0].items.slice(0, 4).map((item, idx) => (
            <div key={idx} className="glass-subtle p-3 rounded-lg text-center hover:glass-light transition-all duration-200">
              <div className="w-full h-24 rounded-lg mb-2 overflow-hidden bg-white/5 flex items-center justify-center">
                <img
                  src={item.image}
                  alt={item.name}
                  className="w-16 h-16 object-cover rounded"
                />
              </div>
              <span className="text-white/70 text-xs">{item.name}</span>
            </div>
          ))}
        </div>

        <div className="flex justify-center">
          <div className="glass-button px-4 py-2 rounded-full hover:glass-medium transition-all duration-200 cursor-pointer">
            <span className="text-white text-sm">View Collection</span>
          </div>
        </div>
      </div>

      {/* Thumbnail Navigation */}
      <div className="flex gap-2 justify-center">
        {['Summer', 'Work', 'Evening'].map((collection, idx) => (
          <div
            key={collection}
            className={`px-3 py-2 rounded-lg text-xs cursor-pointer transition-all duration-200 ${idx === 0 ? 'glass-medium text-white' : 'glass-subtle text-white/60 hover:glass-light hover:text-white/80'
              }`}
          >
            {collection}
          </div>
        ))}
      </div>
    </div>
  );
};

// Helper component for rendering icons
const MockupIcon = ({ icon: Icon }: { icon: React.ComponentType<{ className?: string }> }) => (
  <Icon className="w-5 h-5 text-white" />
);

export default MockupShowcase;
