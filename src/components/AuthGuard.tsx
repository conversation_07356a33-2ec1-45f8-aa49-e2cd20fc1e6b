import { ReactNode, useEffect, useState } from 'react';
import PersistentAuthService from '@/services/persistentAuth';
import { useOnboarding } from '@/hooks/useOnboarding';
import { GlassCard } from '@/components/GlassCard';
import { Loader2, Shield } from 'lucide-react';

interface AuthGuardProps {
  children: ReactNode;
  fallback?: ReactNode;
}

export const AuthGuard = ({ children, fallback }: AuthGuardProps) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { isCompleted } = useOnboarding();

  useEffect(() => {
    const checkAuthentication = async () => {
      try {
        const currentUser = await PersistentAuthService.getCurrentUser();

        if (currentUser) {
          // Check if user has a complete profile by checking the user profile data
          const userProfile = PersistentAuthService.getCurrentUserProfile();
          const hasCompleteProfile = userProfile &&
                                   userProfile.firstName &&
                                   userProfile.lastName &&
                                   userProfile.dateOfBirth &&
                                   userProfile.cityName;

          if (hasCompleteProfile || isCompleted) {
            setIsAuthenticated(true);
          } else {
            setIsAuthenticated(false);
          }
        } else {
          setIsAuthenticated(false);
        }
      } catch (error) {
        console.error('Authentication check failed:', error);
        setIsAuthenticated(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthentication();
  }, [isCompleted]);

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-zara-white via-zara-light-gray/30 to-zara-white flex items-center justify-center p-6">
        <GlassCard variant="prominent" className="p-8 text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-zara-charcoal" />
          <p className="zara-body text-zara-dark-gray">Checking authentication...</p>
        </GlassCard>
      </div>
    );
  }

  // Show fallback if not authenticated
  if (!isAuthenticated) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="min-h-screen bg-gradient-to-br from-zara-white via-zara-light-gray/30 to-zara-white flex items-center justify-center p-6">
        <GlassCard variant="prominent" className="p-8 text-center max-w-md">
          <Shield className="w-12 h-12 mx-auto mb-4 text-zara-charcoal" />
          <h2 className="zara-h3 text-zara-charcoal mb-4">Access Restricted</h2>
          <p className="zara-body text-zara-dark-gray mb-6">
            You need to be authenticated to access this page. Please sign in or create an account.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="zara-body text-zara-charcoal hover:text-zara-dark-gray transition-colors"
          >
            Return to Home
          </button>
        </GlassCard>
      </div>
    );
  }

  // Render children if authenticated
  return <>{children}</>;
};
