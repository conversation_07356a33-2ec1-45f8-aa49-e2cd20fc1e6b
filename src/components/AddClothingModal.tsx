import { useState } from 'react';
import { X, Camera, Upload, Link, Loader2, AlertCircle, ChevronRight, Save } from 'lucide-react';
import { GlassCard } from '@/components/GlassCard';
import { GlassButton } from '@/components/ui/glass-button';
import { GlassInput } from '@/components/ui/glass-input';
import { cn } from '@/lib/utils';
import { FilePickerService, hasCamera } from '@/services/filePicker';
import { ClothingApiService } from '@/services/clothingApi';
import { FileUploadResult } from '@/types/user';

interface ClothingFormData {
  image?: File;
  processedImageUrl?: string;
  category: string;
  color: string;
  brand: string;
  size: string;
  season: string;
  isApiCategoryDetected?: boolean;
  originalApiCategory?: string;
}

interface AddClothingModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: ClothingFormData) => Promise<void>;
  userId?: string;
}

const CLOTHING_CATEGORIES = [
  'Shirt', 'T-Shirts', 'Blouses', 'Sweaters', 'Jeans', 'Trousers', 'Skirts',
  'Dresses', 'Jackets', 'Coats', 'Sneakers', 'Heels', 'Boots', 'Accessories'
];

// Mapping from API categories to frontend categories (minimal by request)
const API_CATEGORY_MAPPING: Record<string, string> = {
  // Only explicit mappings required by product ask
  'shirt': 'Shirt',
  'blouse': 'Blouses',
  'sweater': 'Sweaters',
  'pullover': 'Sweaters',
  'cardigan': 'Sweaters',
  'hoodie': 'Sweaters',
  'sweatshirt': 'Sweaters',

  // Bottoms
  'jeans': 'Jeans',
  'jean': 'Jeans',
  'trouser': 'Trousers',
  'trousers': 'Trousers',
  'pants': 'Trousers',
  'pant': 'Trousers',
  'chinos': 'Trousers',
  'slacks': 'Trousers',
  'skirt': 'Skirts',
  'shorts': 'Trousers',
  'short': 'Trousers',

  // Dresses
  'dress': 'Dresses',
  'gown': 'Dresses',
  'frock': 'Dresses',

  // Outerwear
  'jacket': 'Jackets',
  'blazer': 'Jackets',
  'coat': 'Coats',
  'overcoat': 'Coats',
  'windbreaker': 'Jackets',

  // Footwear
  'sneaker': 'Sneakers',
  'sneakers': 'Sneakers',
  'trainer': 'Sneakers',
  'trainers': 'Sneakers',
  'shoe': 'Sneakers',
  'shoes': 'Sneakers',
  'heel': 'Heels',
  'heels': 'Heels',
  'pump': 'Heels',
  'pumps': 'Heels',
  'stiletto': 'Heels',
  'boot': 'Boots',
  'boots': 'Boots',
  'ankle boot': 'Boots',
  'ankle boots': 'Boots',

  // Accessories
  'accessory': 'Accessories',
  'accessories': 'Accessories',
  'bag': 'Accessories',
  'handbag': 'Accessories',
  'purse': 'Accessories',
  'backpack': 'Accessories',
  'hat': 'Accessories',
  'cap': 'Accessories',
  'scarf': 'Accessories',
  'belt': 'Accessories',
  'watch': 'Accessories',
  'jewelry': 'Accessories',
  'jewellery': 'Accessories',
  'necklace': 'Accessories',
  'bracelet': 'Accessories',
  'earrings': 'Accessories',
  'sunglasses': 'Accessories',
  'glasses': 'Accessories'
};

// Function to map API category to frontend category
const mapApiCategoryToFrontend = (apiCategory: string): string => {
  if (!apiCategory) return '';

  // Clean and normalize the input
  const normalizedInput = apiCategory.toLowerCase().trim();

  // Split by common separators and clean each part
  const categories = normalizedInput
    .split(/[,;|&\s]+/)
    .map(cat => cat.trim())
    .filter(Boolean);

  // Priority order for category matching (prefer more specific categories first)
  const priorityOrder = [
    'Dresses', 'Jeans', 'Blouses', 'Sweaters', 'Jackets', 'Coats',
    'Skirts', 'Trousers', 'Shirt', 'Heels', 'Boots', 'Sneakers', 'Accessories'
  ];

  // Try exact matches first (only for minimal mappings)
  for (const category of categories) {
    if (API_CATEGORY_MAPPING[category]) {
      return API_CATEGORY_MAPPING[category];
    }
  }

  // Try partial matches with priority order (still minimal)
  for (const frontendCategory of priorityOrder) {
    for (const category of categories) {
      for (const [apiKey, mappedCategory] of Object.entries(API_CATEGORY_MAPPING)) {
        if (mappedCategory === frontendCategory) {
          if (category.includes(apiKey) || apiKey.includes(category)) {
            return mappedCategory;
          }
        }
      }
    }
  }

  // Try fuzzy matching only for 'shirt' minimal requirement; otherwise don't auto-map
  for (const category of categories) {
    if (category.includes('shirt') && !category.includes('t-shirt')) return 'Shirt';
  }

  // Return empty string if no minimal match found - user can select manually
  return '';
};

const SEASONS = ['Spring', 'Summer', 'Fall', 'Winter'];

const SIZE_OPTIONS = {
  'Shirt': ['XS', 'S', 'M', 'L', 'XL', 'XXL'],
  'T-Shirts': ['XS', 'S', 'M', 'L', 'XL', 'XXL'],
  'Blouses': ['XS', 'S', 'M', 'L', 'XL', 'XXL'],
  'Sweaters': ['XS', 'S', 'M', 'L', 'XL', 'XXL'],
  'Jeans': ['26', '27', '28', '29', '30', '31', '32', '33', '34', '36', '38'],
  'Trousers': ['26', '27', '28', '29', '30', '31', '32', '33', '34', '36', '38'],
  'Skirts': ['XS', 'S', 'M', 'L', 'XL'],
  'Dresses': ['XS', 'S', 'M', 'L', 'XL', 'XXL'],
  'Jackets': ['XS', 'S', 'M', 'L', 'XL', 'XXL'],
  'Coats': ['XS', 'S', 'M', 'L', 'XL', 'XXL'],
  'Sneakers': ['6', '6.5', '7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11', '11.5', '12'],
  'Heels': ['6', '6.5', '7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11'],
  'Boots': ['6', '6.5', '7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11', '11.5', '12'],
  'Accessories': ['One Size', 'S', 'M', 'L']
};

export const AddClothingModal = ({ isOpen, onClose, onSave, userId }: AddClothingModalProps) => {
  const [step, setStep] = useState<'upload' | 'edit'>('upload');
  const [isUploading, setIsUploading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [touchedFields, setTouchedFields] = useState<Set<string>>(new Set());

  // Validation functions
  const validateField = (name: string, value: string): string => {
    switch (name) {
      case 'category':
        return !value ? 'Category is required' : '';
      case 'color':
        return !value ? 'Color is required' : '';
      case 'brand':
        return !value ? 'Brand is required' : '';
      case 'size':
        return !value ? 'Size is required' : '';
      default:
        return '';
    }
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};
    const requiredFields = ['category', 'color', 'brand', 'size'];

    requiredFields.forEach(field => {
      const error = validateField(field, formData[field as keyof ClothingFormData] as string || '');
      if (error) {
        errors[field] = error;
      }
    });

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleFieldChange = (name: string, value: string) => {
    if (name === 'category') {
      setFormData(prev => ({
        ...prev,
        category: value,
        size: '', // Reset size when category changes
        isApiCategoryDetected: false // User manually changed category
      }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }

    // Mark field as touched
    setTouchedFields(prev => new Set(prev).add(name));

    // Validate field and update errors
    const error = validateField(name, value);
    setValidationErrors(prev => ({
      ...prev,
      [name]: error
    }));
  };

  const handleFieldBlur = (name: string) => {
    setTouchedFields(prev => new Set(prev).add(name));
    const value = formData[name as keyof ClothingFormData] as string || '';
    const error = validateField(name, value);
    setValidationErrors(prev => ({
      ...prev,
      [name]: error
    }));
  };

  const [formData, setFormData] = useState<ClothingFormData>({
    category: '',
    color: '',
    brand: '',
    size: '',
    season: '',
    isApiCategoryDetected: false,
    originalApiCategory: ''
  });

  if (!isOpen) return null;

  const handleFileUpload = async (result: FileUploadResult) => {
    if (result.success && result.file) {
      setFormData(prev => ({ ...prev, image: result.file }));
      setIsProcessing(true);
      setUploadError(null);

      try {
        // Call backend API for image processing and analysis
        const analysisResult = await ClothingApiService.uploadAndAnalyzeImage(result.file);

        if (analysisResult.success) {
          const apiCategory = analysisResult.data.analysis.category;
          const mappedCategory = mapApiCategoryToFrontend(apiCategory);

          setFormData(prev => ({
            ...prev,
            processedImageUrl: analysisResult.data.processedImage,
            category: mappedCategory,
            color: analysisResult.data.analysis.color,
            isApiCategoryDetected: !!mappedCategory,
            originalApiCategory: apiCategory
          }));

          setStep('edit');
        } else {
          setUploadError('Failed to analyze image');
        }
      } catch (error) {
        setUploadError(error instanceof Error ? error.message : 'Failed to process image');
      } finally {
        setIsProcessing(false);
      }
    } else {
      setUploadError(result.error || 'Upload failed');
    }
    setIsUploading(false);
  };

  const handleCameraCapture = async () => {
    setIsUploading(true);
    setUploadError(null);
    try {
      const result = await FilePickerService.captureFromCamera();
      await handleFileUpload(result);
    } catch (error) {
      setUploadError('Camera capture failed');
      setIsUploading(false);
    }
  };

  const handleGallerySelect = async () => {
    setIsUploading(true);
    setUploadError(null);
    try {
      const result = await FilePickerService.selectFromGallery();
      await handleFileUpload(result);
    } catch (error) {
      setUploadError('Gallery selection failed');
      setIsUploading(false);
    }
  };

  const handleFileSystemBrowse = async () => {
    setIsUploading(true);
    setUploadError(null);
    try {
      const result = await FilePickerService.browseFiles();
      await handleFileUpload(result);
    } catch (error) {
      setUploadError('File selection failed');
      setIsUploading(false);
    }
  };

  const handleSave = async () => {
    // Validate form before saving
    if (!validateForm()) {
      // Mark all required fields as touched to show validation errors
      const requiredFields = ['category', 'color', 'brand', 'size'];
      setTouchedFields(new Set(requiredFields));
      return;
    }

    setIsSaving(true);
    try {
      // Always use the parent component's handler to ensure proper state management
      // The parent handler will take care of API calls and local state updates
      await onSave(formData);

      resetForm();
      onClose();
    } catch (error) {
      setUploadError(error instanceof Error ? error.message : 'Failed to save clothing item');
    } finally {
      setIsSaving(false);
    }
  };

  const getSizeOptions = () => {
    return SIZE_OPTIONS[formData.category as keyof typeof SIZE_OPTIONS] || ['XS', 'S', 'M', 'L', 'XL'];
  };

  const resetForm = () => {
    setFormData({
      category: '',
      color: '',
      brand: '',
      size: '',
      season: '',
      isApiCategoryDetected: false,
      originalApiCategory: ''
    });
    setStep('upload');
    setUploadError(null);
    setValidationErrors({});
    setTouchedFields(new Set());
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div
        className="absolute inset-0 bg-foreground/20 backdrop-blur-xl"
        onClick={handleClose}
      />

      <div className="relative w-full max-w-2xl mx-6 animate-glass-scale-in max-h-[90vh] overflow-y-auto">
        <GlassCard variant="modal" className="p-8">
          {/* Modal Header */}
          <div className="flex items-center justify-between mb-8">
            <h3 className="zara-title">Add New Clothing Item</h3>
            <button
              onClick={handleClose}
              className="p-2 glass-panel rounded-full hover:glass-light transition-all duration-300"
            >
              <X size={20} className="text-muted-foreground" />
            </button>
          </div>

          {step === 'upload' ? (
            <>
              {/* Upload Options */}
              <div className="space-y-4 mb-8">
                <h4 className="zara-subtitle mb-6">Choose how to add your item</h4>
                
                {/* Camera Capture */}
                <button
                  onClick={handleCameraCapture}
                  disabled={isUploading || !hasCamera()}
                  className={cn(
                    "w-full p-6 glass-panel rounded-2xl flex items-center space-x-4 transition-all duration-300 group",
                    hasCamera() && !isUploading ? "hover:glass-light" : "opacity-50 cursor-not-allowed"
                  )}
                >
                  <div className="p-3 glass-medium rounded-xl group-hover:scale-110 transition-transform duration-300">
                    {isUploading ? (
                      <Loader2 size={24} className="text-foreground animate-spin" />
                    ) : (
                      <Camera size={24} className="text-foreground" />
                    )}
                  </div>
                  <div className="text-left flex-1">
                    <p className="zara-subtitle">Take Photo</p>
                    <p className="zara-body text-muted-foreground">
                      {hasCamera() ? 'Capture with camera' : 'Camera not available'}
                    </p>
                  </div>
                  {!isUploading && hasCamera() && (
                    <ChevronRight size={20} className="text-muted-foreground group-hover:translate-x-1 transition-transform duration-300" />
                  )}
                </button>

                {/* Gallery Selection */}
                <button
                  onClick={handleGallerySelect}
                  disabled={isUploading}
                  className={cn(
                    "w-full p-6 glass-panel rounded-2xl flex items-center space-x-4 transition-all duration-300 group",
                    !isUploading ? "hover:glass-light" : "opacity-50 cursor-not-allowed"
                  )}
                >
                  <div className="p-3 glass-medium rounded-xl group-hover:scale-110 transition-transform duration-300">
                    {isUploading ? (
                      <Loader2 size={24} className="text-foreground animate-spin" />
                    ) : (
                      <Upload size={24} className="text-foreground" />
                    )}
                  </div>
                  <div className="text-left flex-1">
                    <p className="zara-subtitle">Upload Photo</p>
                    <p className="zara-body text-muted-foreground">Choose from gallery</p>
                  </div>
                  {!isUploading && (
                    <ChevronRight size={20} className="text-muted-foreground group-hover:translate-x-1 transition-transform duration-300" />
                  )}
                </button>

                {/* File System Browser */}
                <button
                  onClick={handleFileSystemBrowse}
                  disabled={isUploading}
                  className={cn(
                    "w-full p-6 glass-panel rounded-2xl flex items-center space-x-4 transition-all duration-300 group",
                    !isUploading ? "hover:glass-light" : "opacity-50 cursor-not-allowed"
                  )}
                >
                  <div className="p-3 glass-medium rounded-xl group-hover:scale-110 transition-transform duration-300">
                    {isUploading ? (
                      <Loader2 size={24} className="text-foreground animate-spin" />
                    ) : (
                      <Link size={24} className="text-foreground" />
                    )}
                  </div>
                  <div className="text-left flex-1">
                    <p className="zara-subtitle">Browse Files</p>
                    <p className="zara-body text-muted-foreground">Select from device</p>
                  </div>
                  {!isUploading && (
                    <ChevronRight size={20} className="text-muted-foreground group-hover:translate-x-1 transition-transform duration-300" />
                  )}
                </button>
              </div>

              {/* Upload Error */}
              {uploadError && (
                <div className="mb-6 p-4 glass-panel rounded-xl border border-red-300 flex items-center space-x-3">
                  <AlertCircle size={20} className="text-red-500 flex-shrink-0" />
                  <p className="text-red-500 zara-body">{uploadError}</p>
                </div>
              )}

              {/* Processing State */}
              {isProcessing && (
                <div className="mb-6 p-6 glass-panel rounded-xl text-center">
                  <Loader2 size={32} className="mx-auto mb-4 animate-spin text-foreground" />
                  <p className="zara-subtitle mb-2">Analyzing Image</p>
                  <p className="zara-body text-muted-foreground">
                    Detecting clothing type and color...
                  </p>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex space-x-4">
                <GlassButton
                  onClick={handleClose}
                  variant="secondary"
                  className="flex-1"
                  disabled={isUploading || isProcessing}
                >
                  Cancel
                </GlassButton>
              </div>
            </>
          ) : (
            <>
              {/* Edit Interface */}
              <div className="space-y-6">
                {/* Image Display */}
                <div className="text-center">
                  <div className="w-48 h-64 mx-auto glass-panel rounded-2xl overflow-hidden mb-4">
                    {formData.processedImageUrl ? (
                      <img
                        src={formData.processedImageUrl}
                        alt="Processed clothing item"
                        className="w-full h-full object-cover fashion-image"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <Loader2 size={32} className="animate-spin text-muted-foreground" />
                      </div>
                    )}
                  </div>
                  <p className="zara-caption text-muted-foreground">Processed clothing item</p>
                </div>

                {/* Form Fields */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Category Field */}
                  <div>
                    <label className="block zara-body font-medium mb-2">Category</label>
                    <select
                      value={formData.category}
                      onChange={(e) => handleFieldChange('category', e.target.value)}
                      onBlur={() => handleFieldBlur('category')}
                      className={cn(
                        "w-full p-3 glass-panel rounded-xl zara-body focus:glass-light transition-all duration-300 border-0 outline-none",
                        touchedFields.has('category') && validationErrors.category && "border-2 border-red-400 animate-wiggle"
                      )}
                    >
                      <option value="">Select category</option>
                      {CLOTHING_CATEGORIES.map(category => (
                        <option key={category} value={category}>{category}</option>
                      ))}
                    </select>
                    <div className="mt-1">
                      {touchedFields.has('category') && validationErrors.category ? (
                        <p className="zara-caption text-red-500 animate-slide-in-up">
                          {validationErrors.category}
                        </p>
                      ) : formData.isApiCategoryDetected ? (
                        <p className="zara-caption text-green-600">
                          ✓ Auto-detected from "{formData.originalApiCategory}" - you can change if needed
                        </p>
                      ) : formData.originalApiCategory ? (
                        <p className="zara-caption text-amber-600">
                          ⚠ Could not map "{formData.originalApiCategory}" - please select manually
                        </p>
                      ) : (
                        <p className="zara-caption text-muted-foreground">
                          Please select a category
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Color Field */}
                  <div>
                    <label className="block zara-body font-medium mb-2">Color</label>
                    <GlassInput
                      variant="default"
                      value={formData.color}
                      onChange={(e) => handleFieldChange('color', e.target.value)}
                      onBlur={() => handleFieldBlur('color')}
                      placeholder="Enter color"
                      className={cn(
                        "w-full",
                        touchedFields.has('color') && validationErrors.color && "border-2 border-red-400 animate-wiggle"
                      )}
                    />
                    {touchedFields.has('color') && validationErrors.color ? (
                      <p className="zara-caption text-red-500 mt-1 animate-slide-in-up">
                        {validationErrors.color}
                      </p>
                    ) : (
                      <p className="zara-caption text-muted-foreground mt-1">Auto-detected, you can edit</p>
                    )}
                  </div>

                  {/* Brand Field */}
                  <div>
                    <label className="block zara-body font-medium mb-2">Brand</label>
                    <GlassInput
                      variant="default"
                      value={formData.brand}
                      onChange={(e) => handleFieldChange('brand', e.target.value)}
                      onBlur={() => handleFieldBlur('brand')}
                      placeholder="Enter brand name"
                      className={cn(
                        "w-full",
                        touchedFields.has('brand') && validationErrors.brand && "border-2 border-red-400 animate-wiggle"
                      )}
                    />
                    {touchedFields.has('brand') && validationErrors.brand && (
                      <p className="zara-caption text-red-500 mt-1 animate-slide-in-up">
                        {validationErrors.brand}
                      </p>
                    )}
                  </div>

                  {/* Size Field */}
                  <div>
                    <label className="block zara-body font-medium mb-2">Size</label>
                    <select
                      value={formData.size}
                      onChange={(e) => handleFieldChange('size', e.target.value)}
                      onBlur={() => handleFieldBlur('size')}
                      className={cn(
                        "w-full p-3 glass-panel rounded-xl zara-body focus:glass-light transition-all duration-300 border-0 outline-none",
                        touchedFields.has('size') && validationErrors.size && "border-2 border-red-400 animate-wiggle"
                      )}
                      disabled={!formData.category}
                    >
                      <option value="">Select size</option>
                      {getSizeOptions().map(size => (
                        <option key={size} value={size}>{size}</option>
                      ))}
                    </select>
                    {touchedFields.has('size') && validationErrors.size ? (
                      <p className="zara-caption text-red-500 mt-1 animate-slide-in-up">
                        {validationErrors.size}
                      </p>
                    ) : !formData.category ? (
                      <p className="zara-caption text-muted-foreground mt-1">Select category first</p>
                    ) : null}
                  </div>

                  {/* Season Field */}
                  <div className="md:col-span-2">
                    <label className="block zara-body font-medium mb-2">Season</label>
                    <div className="grid grid-cols-4 gap-3">
                      {SEASONS.map(season => (
                        <button
                          key={season}
                          onClick={() => setFormData(prev => ({ ...prev, season }))}
                          className={cn(
                            "p-3 rounded-xl transition-all duration-300 zara-body",
                            formData.season === season
                              ? "glass-strong scale-105"
                              : "glass-panel hover:glass-light"
                          )}
                        >
                          {season}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Error Display */}
                {uploadError && (
                  <div className="p-4 glass-panel rounded-xl border border-red-300 flex items-center space-x-3">
                    <AlertCircle size={20} className="text-red-500 flex-shrink-0" />
                    <p className="text-red-500 zara-body">{uploadError}</p>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex space-x-4 pt-4">
                  <GlassButton
                    onClick={() => setStep('upload')}
                    variant="secondary"
                    className="flex-1"
                    disabled={isSaving}
                  >
                    Back
                  </GlassButton>
                  <GlassButton
                    onClick={handleSave}
                    variant="primary"
                    className="flex-1 flex items-center justify-center space-x-2"
                    disabled={isSaving || !formData.category || !formData.color}
                  >
                    {isSaving ? (
                      <>
                        <Loader2 size={16} className="animate-spin" />
                        <span>Saving...</span>
                      </>
                    ) : (
                      <>
                        <Save size={16} />
                        <span>Save Item</span>
                      </>
                    )}
                  </GlassButton>
                </div>
              </div>
            </>
          )}
        </GlassCard>
      </div>
    </div>
  );
};
