import { useState } from 'react';
import { GlassButton } from './ui/glass-button';
import { GlassCard } from './GlassCard';
import PersistentAuthService from '@/services/persistentAuth';
import { runAllConfigTests } from '@/utils/configTest';

export const DebugOnboarding = () => {
  const [results, setResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addResult = (message: string) => {
    setResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const clearResults = () => {
    setResults([]);
  };

  const testAuthentication = async () => {
    setIsLoading(true);
    addResult('🧪 Starting authentication test...');

    try {
      // Initialize database
      addResult('📦 Initializing database...');
      await initializeDatabase();
      addResult('✅ Database initialized');

      // Test password hashing
      addResult('🔒 Testing password hashing...');
      const testPassword = 'TestPassword123!';
      
      // Access the private method through a test
      const authService = AuthService as any;
      let hash1, hash2;
      
      try {
        // Test if we can hash passwords consistently
        hash1 = await authService.hashPassword(testPassword);
        hash2 = await authService.hashPassword(testPassword);
        
        if (hash1 === hash2) {
          addResult('✅ Password hashing is consistent');
        } else {
          addResult('❌ Password hashing is inconsistent');
        }
      } catch (error) {
        addResult(`❌ Password hashing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }

      // Test email validation
      addResult('📧 Testing email validation...');
      const validEmail = AuthService.validateEmail('<EMAIL>');
      const invalidEmail = AuthService.validateEmail('invalid-email');
      
      if (validEmail && !invalidEmail) {
        addResult('✅ Email validation works correctly');
      } else {
        addResult('❌ Email validation failed');
      }

      // Test password validation
      addResult('🔐 Testing password validation...');
      const passwordValidation = AuthService.validatePassword('TestPassword123!');
      
      if (passwordValidation.isValid) {
        addResult('✅ Password validation works correctly');
      } else {
        addResult(`❌ Password validation failed: ${passwordValidation.errors.join(', ')}`);
      }

      addResult('🎉 Authentication tests completed!');

    } catch (error) {
      addResult(`❌ Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testConfiguration = async () => {
    setIsLoading(true);
    addResult('🔧 Testing environment configuration...');

    try {
      const testResults = await runAllConfigTests();

      addResult(`📊 Configuration Test Results:`);
      addResult(`✅ Passed: ${testResults.summary.passedTests}/${testResults.summary.totalTests} (${testResults.summary.successRate}%)`);

      if (testResults.summary.failedTests > 0) {
        addResult(`❌ Failed: ${testResults.summary.failedTests}`);

        // Show failed tests
        [...testResults.environmentTests, ...testResults.connectivityTests]
          .filter(test => !test.success)
          .forEach(test => {
            addResult(`  - ${test.message}: ${JSON.stringify(test.details)}`);
          });
      }

      // Show key configuration values
      addResult('🔧 Key Configuration:');
      addResult(`  - Base URL: ${testResults.environmentTests.find(t => t.message.includes('Base URL is configured'))?.details?.baseUrl}`);
      addResult(`  - Frontend Port: ${testResults.environmentTests.find(t => t.message.includes('Frontend port'))?.details?.frontendPort}`);
      addResult(`  - Backend Port: ${testResults.environmentTests.find(t => t.message.includes('Backend port'))?.details?.backendPort}`);
      addResult(`  - Computed API URL: ${testResults.environmentTests.find(t => t.message.includes('API Base URL is properly computed'))?.details?.apiBaseUrl}`);
      addResult(`  - Computed Frontend URL: ${testResults.environmentTests.find(t => t.message.includes('Frontend URL is properly computed'))?.details?.frontendUrl}`);

    } catch (error) {
      addResult(`❌ Configuration test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testRegistration = async () => {
    setIsLoading(true);
    addResult('👤 Testing user registration...');

    try {
      const testUser = {
        email: `test-${Date.now()}@example.com`,
        password: 'TestPassword123!',
        firstName: 'Test',
        lastName: 'User',
        gender: 'Other' as const,
        dateOfBirth: '1990-01-01',
        cityName: 'Test City',
        latitude: 40.7128,
        longitude: -74.0060,
        weatherPreferences: {}
      };

      const result = await PersistentAuthService.register(
        { email: testUser.email, password: testUser.password },
        {
          firstName: testUser.firstName,
          lastName: testUser.lastName,
          gender: testUser.gender,
          dateOfBirth: testUser.dateOfBirth,
          cityName: testUser.cityName,
          latitude: testUser.latitude,
          longitude: testUser.longitude,
          weatherPreferences: testUser.weatherPreferences
        }
      );

      if (result.success) {
        addResult('✅ User registration successful!');
        addResult(`User ID: ${result.user?.id}`);
        addResult(`User Name: ${result.user?.firstName} ${result.user?.lastName}`);
        
        // Test sign in
        addResult('🔑 Testing sign in...');
        const signInResult = await PersistentAuthService.signIn({
          email: testUser.email,
          password: testUser.password
        });
        
        if (signInResult.success) {
          addResult('✅ Sign in successful!');
        } else {
          addResult(`❌ Sign in failed: ${signInResult.error}`);
        }
        
      } else {
        addResult(`❌ Registration failed: ${result.error}`);
      }

    } catch (error) {
      addResult(`❌ Registration test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const resetOnboarding = () => {
    localStorage.removeItem('onboarding-state');
    localStorage.removeItem('current-user-id');
    localStorage.removeItem('auth-token');
    addResult('🔄 Onboarding state reset - refresh page to start over');
  };

  return (
    <div className="fixed bottom-4 right-4 z-50 max-w-md">
      <GlassCard className="p-4 space-y-3">
        <h3 className="font-semibold text-sm">Debug Tools</h3>
        
        <div className="flex gap-2 flex-wrap">
          <GlassButton
            size="sm"
            onClick={testConfiguration}
            disabled={isLoading}
          >
            Test Config
          </GlassButton>

          <GlassButton
            size="sm"
            onClick={testAuthentication}
            disabled={isLoading}
          >
            Test Auth
          </GlassButton>

          <GlassButton
            size="sm"
            onClick={testRegistration}
            disabled={isLoading}
          >
            Test Registration
          </GlassButton>

          <GlassButton
            size="sm"
            variant="secondary"
            onClick={resetOnboarding}
          >
            Reset
          </GlassButton>
          
          <GlassButton 
            size="sm" 
            variant="secondary"
            onClick={clearResults}
          >
            Clear
          </GlassButton>
        </div>

        {results.length > 0 && (
          <div className="max-h-48 overflow-y-auto bg-black/10 rounded p-2">
            {results.map((result, index) => (
              <div key={index} className="text-xs font-mono mb-1">
                {result}
              </div>
            ))}
          </div>
        )}
      </GlassCard>
    </div>
  );
};
