import { Cloud, Shirt, Calendar } from "lucide-react";

const FeatureHighlights = () => {
  const features = [
    {
      icon: <Cloud className="w-8 h-8" />,
      title: "Weather-Aware Styling",
      description: "Smart outfit suggestions based on real-time weather conditions"
    },
    {
      icon: <Shirt className="w-8 h-8" />,
      title: "Effortless Closet Management",
      description: "Browse, organize, and discover your wardrobe with intelligent categorization"
    },
    {
      icon: <Calendar className="w-8 h-8" />,
      title: "Smart Schedule Planning",
      description: "Plan your outfits ahead with calendar integration and event-appropriate suggestions"
    }
  ];

  return (
    <section id="features" className="py-24 relative">
      {/* Clear Glass Ribbon */}
      <div className="absolute inset-0 bg-gradient-to-r from-white/3 to-white/6" />
      
      <div className="container mx-auto px-6 relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {features.map((feature, index) => (
            <div 
              key={index}
              className="feature-card glass-refraction text-center text-white group"
              style={{
                animationDelay: `${index * 0.2}s`
              }}
            >
              <div className="mb-6 flex justify-center">
                <div className="p-4 rounded-full bg-white/10 group-hover:bg-white/20 transition-all duration-300">
                  {feature.icon}
                </div>
              </div>
              
              <h3 className="text-xl font-medium mb-4 text-shadow">
                {feature.title}
              </h3>
              
              <p className="text-white/80 font-light leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeatureHighlights;
