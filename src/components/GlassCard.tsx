import { ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface GlassCardProps {
  children: ReactNode;
  variant?: 'subtle' | 'light' | 'medium' | 'strong' | 'prominent' | 'navigation' | 'modal' | 'hero' | 'product';
  morphOnHover?: boolean;
  glassIntensity?: 'light' | 'medium' | 'strong';
  className?: string;
  onClick?: () => void;
}

export const GlassCard = ({ 
  children, 
  variant = 'medium', 
  morphOnHover = false,
  glassIntensity,
  className,
  onClick 
}: GlassCardProps) => {
  const variants = {
    subtle: 'glass-subtle',
    light: 'glass-light',
    medium: 'glass-medium',
    strong: 'glass-strong',
    prominent: 'glass-prominent',
    navigation: 'glass-navigation',
    modal: 'glass-modal',
    hero: 'glass-hero',
    product: 'glass-product'
  };

  // Override variant with glassIntensity if provided
  const finalVariant = glassIntensity ? `glass-${glassIntensity}` : variants[variant];

  return (
    <div 
      className={cn(
        'rounded-2xl transition-glass',
        finalVariant,
        morphOnHover && 'glass-morphing',
        onClick && 'cursor-pointer glass-interactive',
        className
      )}
      onClick={onClick}
    >
      {children}
    </div>
  );
};