import { useState, useEffect } from 'react';
import { X, Plus, Shirt, Check, Calendar, Save } from 'lucide-react';
import { GlassCard } from '@/components/GlassCard';
import { GlassButton } from '@/components/ui/glass-button';
import { GlassInput } from '@/components/ui/glass-input';
import { cn } from '@/lib/utils';
import { ClothingApiService, ClothingItem } from '@/services/clothingApi';
import { ScheduleApiService } from '@/services/scheduleApi';
import { OutfitApiService } from '@/services/outfitApi';
import { useUserProfile } from '@/hooks/useUserProfile';
import { toast } from '@/components/ui/sonner';

interface OutfitSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedDate: Date;
  onSave?: () => void;
}

export const OutfitSelectionModal = ({ 
  isOpen, 
  onClose, 
  selectedDate,
  onSave 
}: OutfitSelectionModalProps) => {
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [clothingItems, setClothingItems] = useState<ClothingItem[]>([]);
  const [isLoadingItems, setIsLoadingItems] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [notes, setNotes] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const { userProfile } = useUserProfile();

  const categories = ['All', 'Tops', 'Bottoms', 'Outerwear', 'Dresses', 'Shoes', 'Accessories'];

  // Category mapping
  const categoryMapping: Record<string, string[]> = {
    'Tops': ['T-Shirts', 'Blouses', 'Sweaters', 'Shirts', 'Tank Tops', 'Hoodies'],
    'Bottoms': ['Jeans', 'Trousers', 'Skirts', 'Shorts', 'Leggings', 'Pants'],
    'Shoes': ['Sneakers', 'Heels', 'Boots', 'Sandals', 'Flats', 'Loafers'],
    'Accessories': ['Bags', 'Jewelry', 'Belts', 'Hats', 'Scarves', 'Watches'],
    'Outerwear': ['Jackets', 'Coats', 'Blazers', 'Cardigans', 'Vests'],
    'Dresses': ['Dresses', 'Gowns', 'Sundresses']
  };

  const itemBelongsToCategory = (item: ClothingItem, filterCategory: string): boolean => {
    if (filterCategory === 'All') return true;
    
    const itemCategory = item.category_name || '';
    const mappedCategories = categoryMapping[filterCategory] || [];
    
    return mappedCategories.some(category => 
      itemCategory.toLowerCase().includes(category.toLowerCase()) ||
      category.toLowerCase().includes(itemCategory.toLowerCase())
    );
  };

  useEffect(() => {
    const loadClothingItems = async () => {
      if (userProfile?.id && isOpen) {
        setIsLoadingItems(true);
        try {
          const items = await ClothingApiService.getClothingItems(userProfile.id);
          setClothingItems(items);
        } catch (error) {
          console.error('Failed to load clothing items:', error);
          toast.error('Failed to load clothing items');
        } finally {
          setIsLoadingItems(false);
        }
      }
    };

    loadClothingItems();
  }, [userProfile?.id, isOpen]);

  const filteredItems = clothingItems.filter(item => 
    itemBelongsToCategory(item, selectedCategory)
  );

  const toggleItemSelection = (itemId: string) => {
    setSelectedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(itemId)) {
        newSet.delete(itemId);
      } else {
        newSet.add(itemId);
      }
      return newSet;
    });
  };

  const handleSave = async () => {
    if (!userProfile?.id) {
      toast.error('User not logged in');
      return;
    }

    if (selectedItems.size === 0) {
      toast.error('Please select at least one clothing item');
      return;
    }

    setIsSaving(true);
    try {
      const dateString = selectedDate.toISOString().split('T')[0];
      const clothingItemIds = Array.from(selectedItems);

      // First, create the outfit with selected clothing items
      const outfit = await OutfitApiService.createOutfit({
        userId: userProfile.id,
        name: `Outfit for ${selectedDate.toLocaleDateString()}`,
        description: notes || `Outfit with ${selectedItems.size} items`,
        clothingItemIds,
        notes
      });

      // Then schedule the outfit for the selected date
      await ScheduleApiService.saveScheduledOutfit({
        userId: userProfile.id,
        outfitId: outfit.id,
        scheduledDate: dateString,
        notes: notes || `Outfit with ${selectedItems.size} items`,
      });

      toast.success('Outfit created and scheduled successfully!');
      onSave?.();
      onClose();
      resetForm();
    } catch (error) {
      console.error('Failed to save scheduled outfit:', error);
      toast.error('Failed to create and schedule outfit');
    } finally {
      setIsSaving(false);
    }
  };

  const resetForm = () => {
    setSelectedItems(new Set());
    setNotes('');
    setSelectedCategory('All');
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={handleClose}
      />
      
      {/* Modal */}
      <GlassCard 
        variant="hero" 
        className="relative w-full max-w-4xl max-h-[90vh] overflow-hidden animate-bounce-in"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <div className="flex items-center space-x-3">
            <Calendar size={24} className="text-zara-charcoal" />
            <div>
              <h2 className="zara-title">Plan Outfit</h2>
              <p className="zara-body text-zara-dark-gray">
                {selectedDate.toLocaleDateString('en-US', { 
                  weekday: 'long', 
                  month: 'long', 
                  day: 'numeric' 
                })}
              </p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="p-2 glass-subtle rounded-xl hover:glass-light spring-hover"
          >
            <X size={20} className="text-zara-charcoal" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {/* Category Filter */}
          <div className="mb-6">
            <div className="flex space-x-2 overflow-x-auto pb-2">
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={cn(
                    "px-4 py-2 rounded-full whitespace-nowrap spring-hover",
                    selectedCategory === category
                      ? 'glass-strong text-zara-charcoal'
                      : 'glass-subtle text-zara-dark-gray hover:glass-light'
                  )}
                >
                  <span className="zara-body font-medium">{category}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Selected Items Summary */}
          {selectedItems.size > 0 && (
            <div className="mb-6 p-4 glass-subtle rounded-xl">
              <h3 className="zara-subtitle mb-2">Selected Items ({selectedItems.size})</h3>
              <div className="flex flex-wrap gap-2">
                {Array.from(selectedItems).map(itemId => {
                  const item = clothingItems.find(i => i.id === itemId);
                  return item ? (
                    <span 
                      key={itemId}
                      className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm"
                    >
                      {item.name}
                    </span>
                  ) : null;
                })}
              </div>
            </div>
          )}

          {/* Clothing Items Grid */}
          {isLoadingItems ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {Array.from({ length: 8 }).map((_, index) => (
                <div key={index} className="aspect-[3/4] glass-subtle rounded-xl animate-pulse" />
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-6">
              {filteredItems.map((item) => {
                const isSelected = selectedItems.has(item.id);
                return (
                  <button
                    key={item.id}
                    onClick={() => toggleItemSelection(item.id)}
                    className={cn(
                      "aspect-[3/4] p-3 rounded-xl spring-hover relative",
                      isSelected 
                        ? 'glass-strong ring-2 ring-green-400' 
                        : 'glass-subtle hover:glass-light'
                    )}
                  >
                    {/* Selection indicator */}
                    {isSelected && (
                      <div className="absolute top-2 right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center z-10">
                        <Check size={14} className="text-white" />
                      </div>
                    )}

                    <div className="h-full flex flex-col">
                      <div className="flex-1 glass-subtle rounded-lg flex items-center justify-center mb-2 overflow-hidden">
                        {item.image_url ? (
                          <img
                            src={item.image_url}
                            alt={item.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <Shirt size={20} className="text-zara-dark-gray" />
                        )}
                      </div>
                      <div className="text-center">
                        <p className="zara-body font-medium truncate">{item.name}</p>
                        <p className="zara-caption text-zara-dark-gray">{item.color}</p>
                      </div>
                    </div>
                  </button>
                );
              })}
            </div>
          )}

          {/* Notes */}
          <div className="mb-6">
            <label className="block zara-body font-medium mb-2">Notes (Optional)</label>
            <GlassInput
              variant="default"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Add notes about this outfit..."
              className="w-full"
            />
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-border">
          <p className="zara-body text-zara-dark-gray">
            {selectedItems.size} item{selectedItems.size !== 1 ? 's' : ''} selected
          </p>
          <div className="flex space-x-3">
            <GlassButton variant="ghost" onClick={handleClose}>
              Cancel
            </GlassButton>
            <GlassButton 
              variant="primary" 
              onClick={handleSave}
              disabled={selectedItems.size === 0 || isSaving}
            >
              {isSaving ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  Saving...
                </>
              ) : (
                <>
                  <Save size={16} className="mr-2" />
                  Schedule Outfit
                </>
              )}
            </GlassButton>
          </div>
        </div>
      </GlassCard>
    </div>
  );
};
