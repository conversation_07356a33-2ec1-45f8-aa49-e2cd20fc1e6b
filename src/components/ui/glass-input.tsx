import { forwardRef, useState } from 'react';
import { cn } from '@/lib/utils';

interface GlassInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  variant?: 'default' | 'search' | 'minimal';
  glassIntensity?: 'subtle' | 'light' | 'medium' | 'strong';
  morphOnFocus?: boolean;
}

export const GlassInput = forwardRef<HTMLInputElement, GlassInputProps>(
  ({ 
    label,
    error,
    variant = 'default',
    glassIntensity = 'subtle',
    morphOnFocus = true,
    className,
    onFocus,
    onBlur,
    ...props 
  }, ref) => {
    const [isFocused, setIsFocused] = useState(false);

    const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(true);
      onFocus?.(e);
    };

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(false);
      onBlur?.(e);
    };

    const variants = {
      default: 'rounded-lg px-4 py-3',
      search: 'rounded-full px-6 py-3 pl-12',
      minimal: 'rounded-none border-0 border-b-2 px-0 py-2 bg-transparent'
    };

    const glassIntensities = {
      subtle: 'glass-subtle',
      light: 'glass-light', 
      medium: 'glass-medium',
      strong: 'glass-strong'
    };

    const baseClasses = [
      'w-full',
      'text-zara-charcoal placeholder:text-zara-dark-gray',
      'transition-all duration-300 ease-glass',
      'focus:outline-none',
      variant !== 'minimal' ? glassIntensities[glassIntensity] : '',
      morphOnFocus && isFocused ? 'scale-105 backdrop-blur-xl' : '',
      error ? 'border-red-500' : '',
      variant === 'minimal' ? 'border-zara-medium-gray focus:border-zara-charcoal' : ''
    ].filter(Boolean).join(' ');

    const focusClasses = isFocused ? [
      variant !== 'minimal' ? 'glass-light' : '',
      'ring-2 ring-zara-charcoal/20',
      variant === 'search' ? 'pl-12' : ''
    ].filter(Boolean).join(' ') : '';

    return (
      <div className="space-y-2">
        {label && (
          <label className="block zara-caption text-zara-dark-gray">
            {label}
          </label>
        )}
        
        <div className="relative">
          {variant === 'search' && (
            <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-zara-dark-gray">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          )}
          
          <input
            ref={ref}
            className={cn(
              baseClasses,
              variants[variant],
              focusClasses,
              className
            )}
            onFocus={handleFocus}
            onBlur={handleBlur}
            {...props}
          />
          
          {morphOnFocus && isFocused && (
            <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse pointer-events-none" />
          )}
        </div>
        
        {error && (
          <p className="text-sm text-red-500 zara-caption">
            {error}
          </p>
        )}
      </div>
    );
  }
);

GlassInput.displayName = 'GlassInput';