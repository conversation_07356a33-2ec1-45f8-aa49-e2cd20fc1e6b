import { cn } from '@/lib/utils';

interface LoadingSkeletonProps {
  className?: string;
  variant?: 'text' | 'circular' | 'rectangular' | 'card';
  animation?: 'pulse' | 'wave' | 'shimmer';
}

export const LoadingSkeleton = ({ 
  className, 
  variant = 'rectangular',
  animation = 'shimmer'
}: LoadingSkeletonProps) => {
  const variants = {
    text: 'h-4 rounded',
    circular: 'rounded-full',
    rectangular: 'rounded-lg',
    card: 'rounded-2xl'
  };

  const animations = {
    pulse: 'animate-pulse',
    wave: 'animate-wave',
    shimmer: 'shimmer'
  };

  return (
    <div 
      className={cn(
        'bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200',
        'dark:from-gray-700 dark:via-gray-600 dark:to-gray-700',
        variants[variant],
        animations[animation],
        className
      )}
    />
  );
};

interface ClothingItemSkeletonProps {
  viewMode?: 'grid' | 'list';
}

export const ClothingItemSkeleton = ({ viewMode = 'grid' }: ClothingItemSkeletonProps) => {
  if (viewMode === 'grid') {
    return (
      <div className="aspect-[3/4] p-4 glass-subtle rounded-2xl animate-pulse">
        <div className="h-full flex flex-col">
          <LoadingSkeleton className="flex-1 mb-4" variant="card" />
          <div className="space-y-2">
            <LoadingSkeleton className="h-4 w-3/4" variant="text" />
            <LoadingSkeleton className="h-3 w-1/2" variant="text" />
            <LoadingSkeleton className="h-3 w-1/3" variant="text" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 glass-subtle rounded-2xl animate-pulse">
      <div className="flex items-center space-x-4">
        <LoadingSkeleton className="w-16 h-16" variant="card" />
        <div className="flex-1 space-y-2">
          <LoadingSkeleton className="h-4 w-3/4" variant="text" />
          <LoadingSkeleton className="h-3 w-1/2" variant="text" />
          <LoadingSkeleton className="h-3 w-1/3" variant="text" />
        </div>
        <LoadingSkeleton className="w-5 h-5" variant="circular" />
      </div>
    </div>
  );
};

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const LoadingSpinner = ({ size = 'md', className }: LoadingSpinnerProps) => {
  const sizes = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  };

  return (
    <div className={cn('flex items-center justify-center', className)}>
      <div 
        className={cn(
          'border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin',
          sizes[size]
        )}
      />
    </div>
  );
};

interface LoadingOverlayProps {
  isVisible: boolean;
  message?: string;
  className?: string;
}

export const LoadingOverlay = ({ isVisible, message, className }: LoadingOverlayProps) => {
  if (!isVisible) return null;

  return (
    <div className={cn(
      'absolute inset-0 glass-modal rounded-2xl flex flex-col items-center justify-center z-50',
      'animate-fade-in-scale',
      className
    )}>
      <LoadingSpinner size="lg" className="mb-4" />
      {message && (
        <p className="zara-body text-center text-gray-600 animate-pulse">
          {message}
        </p>
      )}
    </div>
  );
};
