import { ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface GlassButtonProps {
  children: ReactNode;
  variant?: 'primary' | 'secondary' | 'ghost' | 'destructive';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  onClick?: () => void;
  disabled?: boolean;
  type?: 'button' | 'submit' | 'reset';
}

export const GlassButton = ({ 
  children, 
  variant = 'primary',
  size = 'md',
  className,
  onClick,
  disabled = false,
  type = 'button'
}: GlassButtonProps) => {
  const variants = {
    primary: 'glass-panel text-foreground hover:glass-strong hover:shadow-lg hover:-translate-y-0.5',
    secondary: 'glass-subtle text-foreground hover:glass-medium hover:shadow-md hover:-translate-y-0.5',
    ghost: 'bg-white/30 border border-white/40 text-muted-foreground hover:bg-white/50 hover:shadow-sm hover:-translate-y-0.5',
    destructive: 'bg-red-100/70 text-red-700 border border-red-200 hover:bg-red-200/80 hover:shadow-md hover:-translate-y-0.5',
  };

  const sizes = {
    sm: 'px-4 py-2 text-sm',
    md: 'px-6 py-3',
    lg: 'px-8 py-4 text-lg'
  };

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled}
      className={cn(
        'rounded-xl zara-body font-medium spring-hover',
        'disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 disabled:hover:translate-y-0',
        variants[variant],
        sizes[size],
        className
      )}
    >
      {children}
    </button>
  );
};
