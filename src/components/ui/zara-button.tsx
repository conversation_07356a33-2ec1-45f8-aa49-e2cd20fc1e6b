import { ReactNode, forwardRef } from 'react';
import { cn } from '@/lib/utils';

interface ZaraButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: ReactNode;
  variant?: 'primary' | 'secondary' | 'ghost' | 'minimal' | 'cta';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  fullWidth?: boolean;
  loading?: boolean;
  morphOnHover?: boolean;
}

export const ZaraButton = forwardRef<HTMLButtonElement, ZaraButtonProps>(
  ({ 
    children, 
    variant = 'primary', 
    size = 'md',
    fullWidth = false,
    loading = false,
    morphOnHover = false,
    className,
    disabled,
    ...props 
  }, ref) => {
    const variants = {
      primary: [
        'bg-zara-black text-zara-white',
        'hover:bg-zara-charcoal',
        'active:bg-zara-black active:scale-95',
        'border border-transparent'
      ].join(' '),
      
      secondary: [
        'bg-zara-white text-zara-charcoal',
        'border border-zara-medium-gray',
        'hover:bg-zara-light-gray hover:border-zara-dark-gray',
        'active:bg-zara-medium-gray active:scale-95'
      ].join(' '),
      
      ghost: [
        'bg-transparent text-zara-charcoal',
        'border border-transparent',
        'hover:bg-zara-light-gray',
        'active:bg-zara-medium-gray active:scale-95'
      ].join(' '),
      
      minimal: [
        'bg-transparent text-zara-charcoal',
        'border-b border-transparent',
        'hover:border-b hover:border-zara-charcoal',
        'active:text-zara-black',
        'rounded-none px-0'
      ].join(' '),
      
      cta: [
        'glass-medium text-zara-charcoal',
        'border border-white/20',
        'hover:glass-strong hover:text-zara-black',
        'active:scale-95'
      ].join(' ')
    };

    const sizes = {
      xs: 'px-2 py-1 text-xs min-h-[24px]',
      sm: 'px-3 py-1.5 text-sm min-h-[32px]',
      md: 'px-4 py-2 text-sm min-h-[40px]',
      lg: 'px-6 py-3 text-base min-h-[48px]',
      xl: 'px-8 py-4 text-lg min-h-[56px]'
    };

    const baseClasses = [
      'inline-flex items-center justify-center',
      'font-medium tracking-wide',
      'transition-all duration-300 ease-glass',
      'focus:outline-none focus:ring-2 focus:ring-zara-charcoal focus:ring-offset-2',
      'disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:transform-none',
      variant !== 'minimal' ? 'rounded-lg' : '',
      morphOnHover && !disabled ? 'hover:backdrop-blur-xl hover:scale-105' : '',
      fullWidth ? 'w-full' : ''
    ].filter(Boolean).join(' ');

    return (
      <button
        ref={ref}
        className={cn(
          baseClasses,
          variants[variant],
          sizes[size],
          className
        )}
        disabled={disabled || loading}
        {...props}
      >
        {loading ? (
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
            <span>Loading...</span>
          </div>
        ) : (
          children
        )}
      </button>
    );
  }
);

ZaraButton.displayName = 'ZaraButton';