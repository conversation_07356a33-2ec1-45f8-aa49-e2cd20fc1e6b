import { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Calendar as CalendarIcon } from 'lucide-react';
import { GlassCard } from '@/components/GlassCard';
import { cn } from '@/lib/utils';

interface CalendarProps {
  selectedDate?: Date;
  onDateSelect?: (date: Date) => void;
  scheduledDates?: Date[];
  className?: string;
}

export const Calendar = ({ 
  selectedDate, 
  onDateSelect, 
  scheduledDates = [], 
  className 
}: CalendarProps) => {
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsLoaded(true), 100);
    return () => clearTimeout(timer);
  }, []);

  const today = new Date();
  const year = currentMonth.getFullYear();
  const month = currentMonth.getMonth();

  // Get first day of the month and number of days
  const firstDayOfMonth = new Date(year, month, 1);
  const lastDayOfMonth = new Date(year, month + 1, 0);
  const firstDayWeekday = firstDayOfMonth.getDay();
  const daysInMonth = lastDayOfMonth.getDate();

  // Get previous month's last days to fill the grid
  const prevMonth = new Date(year, month - 1, 0);
  const daysInPrevMonth = prevMonth.getDate();

  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentMonth(prev => {
      const newDate = new Date(prev);
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1);
      } else {
        newDate.setMonth(prev.getMonth() + 1);
      }
      return newDate;
    });
  };

  const isDateScheduled = (date: Date) => {
    return scheduledDates.some(scheduledDate => 
      scheduledDate.toDateString() === date.toDateString()
    );
  };

  const isDateSelected = (date: Date) => {
    return selectedDate && selectedDate.toDateString() === date.toDateString();
  };

  const isToday = (date: Date) => {
    return today.toDateString() === date.toDateString();
  };

  const handleDateClick = (date: Date) => {
    if (onDateSelect) {
      onDateSelect(date);
    }
  };

  // Generate calendar days
  const calendarDays = [];

  // Previous month's trailing days
  for (let i = firstDayWeekday - 1; i >= 0; i--) {
    const date = new Date(year, month - 1, daysInPrevMonth - i);
    calendarDays.push({
      date,
      isCurrentMonth: false,
      isPrevMonth: true
    });
  }

  // Current month's days
  for (let day = 1; day <= daysInMonth; day++) {
    const date = new Date(year, month, day);
    calendarDays.push({
      date,
      isCurrentMonth: true,
      isPrevMonth: false
    });
  }

  // Next month's leading days to fill the grid (42 days total - 6 weeks)
  const remainingDays = 42 - calendarDays.length;
  for (let day = 1; day <= remainingDays; day++) {
    const date = new Date(year, month + 1, day);
    calendarDays.push({
      date,
      isCurrentMonth: false,
      isPrevMonth: false
    });
  }

  return (
    <GlassCard 
      variant="hero" 
      className={cn(
        "p-6 transition-all duration-800 ease-liquid",
        isLoaded ? "opacity-100 translate-y-0" : "opacity-0 translate-y-4",
        className
      )}
    >
      {/* Calendar Header */}
      <div className="flex items-center justify-between mb-6">
        <button
          onClick={() => navigateMonth('prev')}
          className="p-2 glass-subtle rounded-xl hover:glass-light spring-hover"
        >
          <ChevronLeft size={20} className="text-zara-charcoal" />
        </button>
        
        <div className="flex items-center space-x-3">
          <CalendarIcon size={20} className="text-zara-dark-gray" />
          <h2 className="zara-title">
            {monthNames[month]} {year}
          </h2>
        </div>
        
        <button
          onClick={() => navigateMonth('next')}
          className="p-2 glass-subtle rounded-xl hover:glass-light spring-hover"
        >
          <ChevronRight size={20} className="text-zara-charcoal" />
        </button>
      </div>

      {/* Day Names Header */}
      <div className="grid grid-cols-7 gap-1 mb-2">
        {dayNames.map(day => (
          <div key={day} className="p-2 text-center">
            <span className="zara-caption text-zara-dark-gray font-medium">
              {day}
            </span>
          </div>
        ))}
      </div>

      {/* Calendar Grid */}
      <div className="grid grid-cols-7 gap-1">
        {calendarDays.map((calendarDay, index) => {
          const { date, isCurrentMonth } = calendarDay;
          const scheduled = isDateScheduled(date);
          const selected = isDateSelected(date);
          const todayDate = isToday(date);
          
          return (
            <button
              key={index}
              onClick={() => handleDateClick(date)}
              className={cn(
                "relative p-3 rounded-xl transition-all duration-300 spring-hover",
                "hover:glass-light focus:outline-none focus:ring-2 focus:ring-blue-400",
                isCurrentMonth 
                  ? "text-zara-charcoal" 
                  : "text-zara-medium-gray opacity-50",
                selected && "glass-strong scale-105",
                todayDate && !selected && "glass-medium border-2 border-blue-400",
                scheduled && "bg-green-100 border border-green-300"
              )}
              style={{ animationDelay: `${index * 10}ms` }}
            >
              <span className={cn(
                "zara-body font-medium",
                selected && "text-zara-charcoal",
                todayDate && !selected && "text-blue-600"
              )}>
                {date.getDate()}
              </span>
              
              {/* Scheduled indicator */}
              {scheduled && (
                <div className="absolute bottom-1 left-1/2 transform -translate-x-1/2">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse" />
                </div>
              )}
              
              {/* Today indicator */}
              {todayDate && !selected && (
                <div className="absolute top-1 right-1">
                  <div className="w-2 h-2 bg-blue-500 rounded-full" />
                </div>
              )}
            </button>
          );
        })}
      </div>

      {/* Legend */}
      <div className="flex items-center justify-center space-x-6 mt-6 pt-4 border-t border-border">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-blue-500 rounded-full" />
          <span className="zara-caption">Today</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-green-500 rounded-full" />
          <span className="zara-caption">Scheduled</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 glass-strong rounded-full border border-gray-400" />
          <span className="zara-caption">Selected</span>
        </div>
      </div>
    </GlassCard>
  );
};
