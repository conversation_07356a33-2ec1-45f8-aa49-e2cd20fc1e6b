import React, { Component, ErrorInfo, ReactNode } from 'react';
import { GlassCard } from './GlassCard';
import { GlassButton } from './ui/glass-button';
import { AlertTriangle, RefreshCw, Copy } from 'lucide-react';
import { logger } from '@/utils/logger';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    logger.error('ERROR_BOUNDARY', 'React error boundary caught error', {
      errorMessage: error.message,
      errorStack: error.stack,
      componentStack: errorInfo.componentStack,
      errorBoundary: this.constructor.name
    }, error);

    this.setState({ error, errorInfo });
  }

  copyErrorToClipboard = () => {
    if (this.state.error) {
      const errorDetails = {
        message: this.state.error.message,
        stack: this.state.error.stack,
        componentStack: this.state.errorInfo?.componentStack,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href
      };

      navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2))
        .then(() => alert('Error details copied to clipboard'))
        .catch(() => console.error('Failed to copy error details'));
    }
  };

  handleReset = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-screen bg-zara-white flex items-center justify-center p-6">
          <GlassCard className="max-w-md w-full p-8 text-center">
            <div className="w-16 h-16 mx-auto mb-6 glass-medium rounded-full flex items-center justify-center">
              <AlertTriangle size={32} className="text-red-500" />
            </div>
            
            <h2 className="zara-title mb-4">Something went wrong</h2>
            <p className="zara-body text-zara-dark-gray mb-6">
              We're sorry, but something unexpected happened. Please try refreshing the page.
            </p>
            
            {this.state.error && (
              <div className="mb-6 p-4 glass-subtle rounded-xl text-left">
                <p className="zara-caption text-red-600 font-mono mb-2">
                  {this.state.error.message}
                </p>
                {process.env.NODE_ENV === 'development' && this.state.error.stack && (
                  <details className="mt-2">
                    <summary className="cursor-pointer zara-caption text-gray-600">
                      Stack Trace
                    </summary>
                    <pre className="mt-2 text-xs text-gray-500 overflow-auto max-h-32">
                      {this.state.error.stack}
                    </pre>
                  </details>
                )}
                <button
                  onClick={this.copyErrorToClipboard}
                  className="mt-2 flex items-center space-x-1 text-xs text-gray-600 hover:text-gray-800"
                >
                  <Copy size={12} />
                  <span>Copy Error Details</span>
                </button>
              </div>
            )}
            
            <div className="flex space-x-4">
              <GlassButton
                variant="secondary"
                onClick={this.handleReset}
                className="flex-1 flex items-center justify-center space-x-2"
              >
                <RefreshCw size={16} />
                <span>Try Again</span>
              </GlassButton>
              
              <GlassButton
                variant="primary"
                onClick={() => window.location.reload()}
                className="flex-1"
              >
                Refresh Page
              </GlassButton>
            </div>
          </GlassCard>
        </div>
      );
    }

    return this.props.children;
  }
}
