#!/usr/bin/env node

/**
 * Test script to verify schedule data persistence
 * This script tests the backend API endpoints to ensure data is properly saved and retrieved
 */

import fetch from 'node-fetch';

const API_BASE_URL = 'http://localhost:3001/api';
const TEST_USER_ID = 'fd2849d9-cb72-4385-b8c9-20a8188c6932';

async function testSchedulePersistence() {
  console.log('🧪 Testing Schedule Data Persistence...\n');

  try {
    // Test 1: Check if we can retrieve existing scheduled outfits
    console.log('📋 Test 1: Retrieving existing scheduled outfits...');
    const getResponse = await fetch(`${API_BASE_URL}/schedule/${TEST_USER_ID}`);
    
    if (!getResponse.ok) {
      throw new Error(`GET request failed: ${getResponse.status} ${getResponse.statusText}`);
    }
    
    const existingOutfits = await getResponse.json();
    console.log(`✅ Successfully retrieved ${existingOutfits.length} existing outfits`);
    
    if (existingOutfits.length > 0) {
      console.log('📝 Sample outfit:', JSON.stringify(existingOutfits[0], null, 2));
    }

    // Test 2: Test saving a new scheduled outfit
    console.log('\n📋 Test 2: Testing outfit save operation...');
    const testOutfit = {
      user_id: TEST_USER_ID,
      outfit_id: 'test-outfit-' + Date.now(),
      scheduled_date: new Date().toISOString().split('T')[0], // Today's date
      notes: 'Test outfit for persistence verification'
    };

    const saveResponse = await fetch(`${API_BASE_URL}/schedule`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testOutfit)
    });

    if (!saveResponse.ok) {
      throw new Error(`POST request failed: ${saveResponse.status} ${saveResponse.statusText}`);
    }

    const savedOutfit = await saveResponse.json();
    console.log('✅ Successfully saved test outfit:', savedOutfit.id);

    // Test 3: Verify the saved outfit can be retrieved
    console.log('\n📋 Test 3: Verifying saved outfit can be retrieved...');
    const verifyResponse = await fetch(`${API_BASE_URL}/schedule/${TEST_USER_ID}`);
    
    if (!verifyResponse.ok) {
      throw new Error(`Verification GET request failed: ${verifyResponse.status} ${verifyResponse.statusText}`);
    }
    
    const updatedOutfits = await verifyResponse.json();
    const foundTestOutfit = updatedOutfits.find(outfit => outfit.id === savedOutfit.id);
    
    if (foundTestOutfit) {
      console.log('✅ Test outfit successfully retrieved after save');
      console.log('📝 Retrieved outfit:', JSON.stringify(foundTestOutfit, null, 2));
    } else {
      throw new Error('❌ Test outfit not found in retrieved data');
    }

    // Test 4: Clean up - delete the test outfit
    console.log('\n📋 Test 4: Cleaning up test data...');
    const deleteResponse = await fetch(`${API_BASE_URL}/schedule/${savedOutfit.id}`, {
      method: 'DELETE'
    });

    if (deleteResponse.ok) {
      console.log('✅ Test outfit successfully deleted');
    } else {
      console.log('⚠️  Warning: Could not delete test outfit (this is okay)');
    }

    console.log('\n🎉 All tests passed! Schedule data persistence is working correctly.');
    
    return {
      success: true,
      existingOutfitsCount: existingOutfits.length,
      testOutfitId: savedOutfit.id
    };

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

// Run the test
testSchedulePersistence()
  .then(result => {
    if (result.success) {
      console.log('\n✅ Schedule persistence test completed successfully');
      process.exit(0);
    } else {
      console.log('\n❌ Schedule persistence test failed');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('\n💥 Unexpected error:', error);
    process.exit(1);
  });

export { testSchedulePersistence };
