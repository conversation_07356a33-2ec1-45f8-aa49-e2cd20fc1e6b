# Application Configuration
VITE_APP_NAME=Closet Glass Chic
VITE_APP_VERSION=1.0.0

# Network Configuration
# Base URL/IP address for all services (change this for external access)
# Examples:
# Local development: 127.0.0.1 or localhost
# Local network access: ************* (your machine's IP)
# Production: your-domain.com or production-server-ip
VITE_BASE_URL=**************

# Port Configuration
VITE_FRONTEND_PORT=5173
VITE_BACKEND_PORT=3001

# Non-VITE versions for Docker Compose
BASE_URL=**************
FRONTEND_PORT=5173
BACKEND_PORT=3001

# API Configuration
VITE_API_TIMEOUT=10000

# Environment
NODE_ENV=development

# Development server configuration (for dev mode)
VITE_HOST=0.0.0.0
VITE_PORT=5173

# Build configuration
VITE_BUILD_PATH=dist

# Feature flags (if needed)
# VITE_ENABLE_ANALYTICS=false
# VITE_ENABLE_DEBUG=false

# External API Services
# OpenWeatherMap API - Get your free API key from: https://openweathermap.org/api
VITE_OPENWEATHER_API_KEY=your_openweather_api_key_here
VITE_OPENWEATHER_API_URL=https://api.openweathermap.org/data/2.5

# Nominatim API for city search (OpenStreetMap)
VITE_NOMINATIM_API_URL=https://nominatim.openstreetmap.org

# API Rate Limiting Configuration
VITE_API_RATE_LIMIT_REQUESTS=60
VITE_API_RATE_LIMIT_WINDOW=60000
VITE_API_CACHE_DURATION=300000

# Database Configuration
VITE_DB_NAME=closet_glass_chic
VITE_DB_VERSION=1

# File Upload Configuration
VITE_MAX_FILE_SIZE=5242880
VITE_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp,image/heic
VITE_MAX_IMAGE_WIDTH=2048
VITE_MAX_IMAGE_HEIGHT=2048

# Feature flags
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_DEBUG=true
VITE_ENABLE_WEATHER_INTEGRATION=true
VITE_ENABLE_CITY_AUTOCOMPLETE=true

# Google Analytics (if needed in the future)
# VITE_GOOGLE_ANALYTICS_ID=your_ga_id_here
